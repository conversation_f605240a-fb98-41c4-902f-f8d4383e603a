package layoutconfiguration

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/be-common/pkg/events"

	"go.uber.org/zap"

	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	pkgSyncMap "github.com/epifi/be-common/pkg/syncmap"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/home/<USER>/attributes"
	homeEvents "github.com/epifi/gamma/frontend/home/<USER>/events"
	"github.com/epifi/gamma/frontend/home/<USER>/helper"
	"github.com/epifi/gamma/frontend/home/<USER>/sort"
)

var EngineWireSet = wire.NewSet(NewEngine, wire.Bind(new(IEngine), new(*Engine)))

type IEngine interface {
	// EvaluateLayout evaluates the dynamic home layout and returns evaluated slotId to screen elements map.
	EvaluateLayout(ctx context.Context, actorId string, layoutV2Params *genconf.HomeLayoutV2Params) (*config.SlotIdToScreenElementIdMap, error)
}

type Engine struct {
	AttributeEvaluatorFactory attributes.IAttributeEvaluatorFactory
	SortingStrategyFactory    sort.ISortingStrategyFactory
	eventsBroker              events.Broker
}

func NewEngine(attributeEvaluatorFactory attributes.IAttributeEvaluatorFactory, sortingStrategyFactory sort.ISortingStrategyFactory, eventsBroker events.Broker) *Engine {
	return &Engine{
		AttributeEvaluatorFactory: attributeEvaluatorFactory,
		SortingStrategyFactory:    sortingStrategyFactory,
		eventsBroker:              eventsBroker,
	}
}

func (e *Engine) EvaluateLayout(ctx context.Context, actorId string, layoutV2Params *genconf.HomeLayoutV2Params) (*config.SlotIdToScreenElementIdMap, error) {
	var (
		attributeEvaluatedMap = &pkgSyncMap.Map[attributes.Attribute, any]{}
	)

	// if there is no dynamic layout, return nil.
	if layoutV2Params.SlotIdToScreenElementIdsMap() == nil {
		return nil, nil
	}

	// evaluate all attribute conditions in parallel.
	errGrp, grpCtx := errgroup.WithContext(ctx)
	for _, a := range attributes.AttributeEvaluatorList {
		attributeEvaluatorName := a
		errGrp.Go(func() error {
			// get attribute evaluator
			attributeEvaluator, err := e.AttributeEvaluatorFactory.GetAttributeEvaluator(attributeEvaluatorName)
			if err != nil {
				logger.Error(grpCtx, "error while fetching attribute evaluator", zap.String("AttributeEvaluator", string(attributeEvaluatorName)), zap.Error(err))
				return fmt.Errorf("error while fetching attribute evaluator, err: %w", err)
			}
			val, err := attributeEvaluator.EvaluateAttribute(grpCtx, actorId, layoutV2Params.HomeElementAttributes())
			if err != nil {
				logger.Error(grpCtx, "error while evaluating attribute", zap.String("AttributeEvaluator", string(attributeEvaluatorName)), zap.Error(err))
				return fmt.Errorf("error while evaluating attribute, err: %w", err)
			}

			attributeEvaluatedMap.Store(attributeEvaluatorName, val)
			return nil
		})
	}
	if err := errGrp.Wait(); err != nil {
		logger.Error(grpCtx, "error while evaluating dynamic layout", zap.Error(err))
		return nil, fmt.Errorf("error while evaluating dynamic layout, err: %w", err)
	}

	// get the element id to score map.
	// by default, we assign score 0 to all elements and will update based on element attribute conditions.
	elementIdToScoreMap := helper.GetElementIdToScoreMap(layoutV2Params.SlotIdToScreenElementIdsMap())

	// now with evaluated attributes, assign score to each element
	for _, a := range attributes.AttributeEvaluatorList {
		attributeEvaluatorName := a
		// get attribute evaluator
		attributeEvaluator, err := e.AttributeEvaluatorFactory.GetAttributeEvaluator(attributeEvaluatorName)
		if err != nil {
			logger.Error(ctx, "error while fetching attribute evaluator", zap.String("AttributeEvaluator", string(attributeEvaluatorName)), zap.Error(err))
			continue
		}
		elementIdToScoreMap, err = attributeEvaluator.AssignEvaluatedScore(ctx, elementIdToScoreMap, layoutV2Params.HomeElementAttributes(), attributeEvaluatedMap.Get(attributeEvaluatorName))
		if err != nil {
			logger.Error(ctx, "error while assigning evaluated element attributes scores to elements", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return nil, err
		}
	}

	// Currently, we support only priority order sorting only.
	// If we support different sorting strategies based on layout params we can change this based on requirement.
	sortingStrategy, err := e.SortingStrategyFactory.GetSortingStrategy(sort.PriorityOrderSorting)
	if err != nil {
		logger.Error(ctx, "error while fetching sorting strategy factory", zap.String("SortingStrategy", string(sort.PriorityOrderSorting)), zap.Error(err))
		return nil, err
	}
	// now assign high user affinity elements to slots based on scores.
	slotIdToElementIdMap := sortingStrategy.Sort(ctx, actorId, elementIdToScoreMap, layoutV2Params)

	// assign layout id to the dynamically generated layout based on content.
	slotIdToElementIdMap.LayoutId, err = helper.GetLayoutIdFromSlotIdToScreenElementIdMap(slotIdToElementIdMap)
	if err != nil {
		logger.Error(ctx, "error while getting layout id from slot id to element id map", zap.Error(err))
		return nil, err
	}

	goroutine.RunWithCtx(epificontext.WithEventAttributesV2(ctx), func(gctx context.Context) {
		e.eventsBroker.AddToBatch(gctx, homeEvents.NewEvaluatedHomeLayoutServer(actorId, elementIdToScoreMap, slotIdToElementIdMap))
	})

	return slotIdToElementIdMap, nil
}
