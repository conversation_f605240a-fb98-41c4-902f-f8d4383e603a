// nolint:gosec,ineffassign
package dynamic_elements

import (
	"github.com/samber/lo"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"math/rand"
	"strings"
	"time"

	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/hash"
	"github.com/epifi/be-common/pkg/logger"
	beDePb "github.com/epifi/gamma/api/dynamic_elements"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	feDePb "github.com/epifi/gamma/api/frontend/dynamic_elements"
	"github.com/epifi/gamma/api/frontend/header"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	genConf "github.com/epifi/gamma/frontend/config/genconf"
)

type Service struct {
	feDePb.UnimplementedDynamicElementsServer
	dynamicElementsClient beDePb.DynamicElementsClient
	genconf               *genConf.Config
}

func NewDynamicElementsService(dynamicElementsClient beDePb.DynamicElementsClient, genconf *genConf.Config) *Service {
	return &Service{
		dynamicElementsClient: dynamicElementsClient,
		genconf:               genconf,
	}
}

var _ feDePb.DynamicElementsServer = &Service{}

var (
	// defines probability of picking the element which will be displayed first in a list of elements
	// **Note** any modifications made here should result in sum of all weights equal to 1
	utilityTypeToDisplayFirstElementWeightMap = map[feDePb.ElementUtilityType]float64{
		feDePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT:              0.4,
		feDePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING:          0.2,
		feDePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_INSIGHT:            0.2,
		feDePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_DEFAULT:            0.1,
		feDePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_CONSENT_COLLECTION: 0.1,
	}
)

func (s *Service) FetchDynamicElements(ctx context.Context, req *feDePb.FetchDynamicElementsRequest) (*feDePb.FetchDynamicElementsResponse, error) {
	beClientContext := convertToBeClientContext(req.GetClientContext())
	beResp, err := s.dynamicElementsClient.FetchDynamicElementsFromAllServices(ctx, &beDePb.FetchDynamicElementsFromAllServicesRequest{
		ActorId:       req.GetReq().GetAuth().GetActorId(),
		ClientContext: beClientContext,
		SessionId:     req.GetReq().GetSessionId(),
	})
	if err != nil {
		logger.Error(ctx, "error fetching dynamic elements from be service", zap.Error(err))
		return &feDePb.FetchDynamicElementsResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusInternal(),
			},
		}, nil
	}
	switch {
	case beResp.GetStatus().IsRecordNotFound():
		return &feDePb.FetchDynamicElementsResponse{
			RespHeader: &header.ResponseHeader{
				Status: beResp.GetStatus(),
			},
		}, nil
	case !beResp.GetStatus().IsSuccess():
		logger.Error(ctx, "grpc status is NOT ok from BE dynamic elements service", zap.String(logger.SCREEN, beClientContext.GetHomeInfo().GetSection().String()),
			zap.Error(err))
		return &feDePb.FetchDynamicElementsResponse{
			RespHeader: &header.ResponseHeader{
				Status: beResp.GetStatus(),
			},
		}, nil
	}

	var title *commontypes.Text
	beDynamicElementsList := beResp.GetElementsList()
	switch {
	case req.GetClientContext().GetHomeInfo().GetSection() == feDePb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY && req.GetClientContext().GetScreenName() == dlPb.Screen_HOME:
		// If the call is made for primary feature space, we will return a single element in list
		title, beDynamicElementsList = filterDynamicElementsForPrimaryFeatureWidget(ctx, beDynamicElementsList)
	case req.GetClientContext().GetHomeInfo().GetSection() == feDePb.HomeScreenAdditionalInfo_SECTION_FEATURE_SECONDARY && req.GetClientContext().GetScreenName() == dlPb.Screen_HOME:
		// If the call is made for secondary feature space, we will skip the first priority widget and return others
		title, beDynamicElementsList = filterDynamicElementsForSecondaryFeatureWidget(ctx, beDynamicElementsList)
	case req.GetClientContext().GetHomeInfo().GetSection() == feDePb.HomeScreenAdditionalInfo_SECTION_BODY2 && req.GetClientContext().GetScreenName() == dlPb.Screen_HOME:
		// If the call is made for lending banner space, we will return a single element in list
		if len(beDynamicElementsList) > 0 {
			beDynamicElementsList = beDynamicElementsList[:1]
		}
	case req.GetClientContext().GetHomeInfo().GetSection() == feDePb.HomeScreenAdditionalInfo_SECTION_BODY && req.GetClientContext().GetScreenName() == dlPb.Screen_HOME:
		// If the call is made for usual banner space, we randomise the order of dynamic elements and apply filters if any
		beDynamicElementsList = filterDynamicElementsForBodyBannerV2Widget(beDynamicElementsList, req.GetReq().GetSessionId())
	case req.GetClientContext().GetHomeInfo().GetSection() == feDePb.HomeScreenAdditionalInfo_SECTION_TABBED_CARD:
		// returning the first element for tabbed card
		beDynamicElementsList = beDynamicElementsList[:1]
	}

	dynamicElementsList := ConvertToFeElementList(ctx, beClientContext, beDynamicElementsList, s.genconf.DynamicElementsConfig())
	if len(dynamicElementsList) == 0 {
		logger.Error(ctx, "dynamic elements list empty")
		return &feDePb.FetchDynamicElementsResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusRecordNotFound(),
			},
		}, nil
	}

	// Client will use start index as the index to start scrolling promotional banner list
	startIndex := getFeDynamicElementsStartIndex(dynamicElementsList)

	return &feDePb.FetchDynamicElementsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		Title:        title,
		ElementsList: dynamicElementsList,
		StartIndex:   startIndex,
	}, nil
}

func convertToBeClientContext(feClientContext *feDePb.ClientContext) *beDePb.ClientContext {
	beClientContext := &beDePb.ClientContext{
		ScreenName: feClientContext.GetScreenName(),
	}
	//nolint:gocritic
	switch feClientContext.GetScreenAdditionalInfo().(type) {
	case *feDePb.ClientContext_FaqCategoryInfo:
		faqCategory := feClientContext.GetFaqCategoryInfo()
		beClientContext.ScreenAdditionalInfo = &beDePb.ClientContext_FaqCategory{
			FaqCategory: &beDePb.FAQCategoryScreenAdditionalInfo{
				CategoryId:   faqCategory.GetCategoryId(),
				CategoryName: faqCategory.GetCategoryName(),
			},
		}
	case *feDePb.ClientContext_HomeInfo:
		feHomeInfo := feClientContext.GetHomeInfo()
		beClientContext.ScreenAdditionalInfo = &beDePb.ClientContext_HomeInfo{
			HomeInfo: &beDePb.HomeScreenAdditionalInfo{
				Section: convertToBeHomeSection(feHomeInfo.GetSection()),
				Version: convertToBeHomeVersion(feHomeInfo.GetVersion()),
			},
		}
	case *feDePb.ClientContext_AnalyserScreenInfo:
		analyserInfo := feClientContext.GetAnalyserScreenInfo()
		beClientContext.ScreenAdditionalInfo = &beDePb.ClientContext_AnalyserScreenInfo{
			AnalyserScreenInfo: &beDePb.AnalyserScreenAdditionalInfo{
				AnalyserName: analyserInfo.GetAnalyserName(),
			},
		}
	}
	return beClientContext
}

func convertToBeHomeSection(feHomeSection feDePb.HomeScreenAdditionalInfo_Section) beDePb.HomeScreenAdditionalInfo_Section {
	beHomeSection, ok := beDePb.HomeScreenAdditionalInfo_Section_value[feHomeSection.String()]
	if !ok {
		return beDePb.HomeScreenAdditionalInfo_SECTION_UNSPECIFIED
	}
	return beDePb.HomeScreenAdditionalInfo_Section(beHomeSection)
}

func convertToBeHomeVersion(feHomeVersion feDePb.HomeScreenAdditionalInfo_Version) beDePb.HomeScreenAdditionalInfo_Version {
	beHomeVersion, ok := beDePb.HomeScreenAdditionalInfo_Version_value[feHomeVersion.String()]
	if !ok {
		return beDePb.HomeScreenAdditionalInfo_VERSION_UNSPECIFIED
	}
	return beDePb.HomeScreenAdditionalInfo_Version(beHomeVersion)
}

func (s *Service) DynamicElementCallback(ctx context.Context, req *feDePb.DynamicElementCallbackRequest) (*feDePb.DynamicElementCallbackResponse, error) {
	var (
		serviceName     = req.GetService()
		callbackPayload = &beDePb.CallbackPayload{}
	)
	// TODO: remove below check once element id empty in request issue is resolved.
	// returning success currently to silence the alert.
	if req.GetElementId() == "" {
		logger.Error(ctx, "element id shouldn't be empty", zap.String("req", req.String()))
		return &feDePb.DynamicElementCallbackResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusOk(),
			},
		}, nil
	}

	switch req.GetCallbackPayload().GetPayload().(type) {
	case *feDePb.CallbackPayload_InAppTargetedComms:
		// defaulting to comms service in case service name isn't specified by client
		if serviceName == types.ServiceName_SERVICE_UNSPECIFIED {
			serviceName = types.ServiceName_COMMS_SERVICE
		}

		// this is a patch to handle rekyc popup related callback alerts
		// until android client starts sending appropriate owner svc name
		if strings.Contains(req.GetElementId(), "PERIODICKYC") {
			serviceName = types.ServiceName_BANK_CUSTOMER_SERVICE
		}

		callbackPayload = &beDePb.CallbackPayload{
			Payload: &beDePb.CallbackPayload_InAppTargetedComms{
				InAppTargetedComms: &beDePb.InAppTargetedCommsCallbackPayload{
					IsDismissed: req.GetCallbackPayload().GetInAppTargetedComms().GetIsDismissed(),
				},
			},
		}

	default:
		logger.Error(ctx, "unknown payload for FE DynamicElementCallback", zap.Any(logger.PAYLOAD, req.GetCallbackPayload().GetPayload()))
		return &feDePb.DynamicElementCallbackResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusOk(),
			},
		}, nil
	}

	beResp, err := s.dynamicElementsClient.DynamicElementCallbackToService(ctx, &beDePb.DynamicElementCallbackToServiceRequest{
		Service:         serviceName,
		ActorId:         req.GetReq().GetAuth().GetActorId(),
		ElementId:       req.GetElementId(),
		CallbackPayload: callbackPayload,
	})
	if te := epifigrpc.RPCError(beResp, err); te != nil {
		logger.Error(ctx, "failure in BE DynamicElementCallback", zap.Error(te))
		return &feDePb.DynamicElementCallbackResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("failure in BE DynamicElementCallback"),
			},
		}, nil
	}

	return &feDePb.DynamicElementCallbackResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
	}, nil
}

func ConvertToFeElementList(ctx context.Context, beClientContext *beDePb.ClientContext, belist []*beDePb.DynamicElement, dynamicElementsConfig *genConf.DynamicElementsConfig) []*feDePb.DynamicElement {
	var feList []*feDePb.DynamicElement
	for _, beElt := range belist {
		feElt, err := convertToFeElement(ctx, beClientContext, beElt, dynamicElementsConfig)
		if err != nil {
			logger.Error(ctx, "error converting be dynamic element to fe dynamic element", zap.String(logger.ELEMENT_ID, beElt.GetId()), zap.Error(err))
			continue
		}
		feList = append(feList, feElt)
	}
	return feList
}

func convertToFeElement(ctx context.Context, beClientContext *beDePb.ClientContext, beElt *beDePb.DynamicElement, dynamicElementsConfig *genConf.DynamicElementsConfig) (*feDePb.DynamicElement, error) {
	elementContent, err := convertToFeElementContent(ctx, beClientContext, beElt.GetContent(), beElt.GetEndTime(), dynamicElementsConfig)
	if err != nil {
		return nil, fmt.Errorf("invalid content for dynamic element, err: %w", err)
	}
	return &feDePb.DynamicElement{
		OwnerService:     beElt.GetOwnerService(),
		Id:               beElt.GetId(),
		UtilityType:      convertToFeElementUtilityType(beElt.GetUtilityType()),
		StructureType:    convertToFeElementStructureType(beElt.GetStructureType()),
		Content:          elementContent,
		BizAnalyticsData: beElt.GetBizAnalyticsData(),
	}, nil
}

func convertToFeElementUtilityType(beContentType beDePb.ElementUtilityType) feDePb.ElementUtilityType {
	feStructureType, ok := feDePb.ElementUtilityType_value[beContentType.String()]
	if !ok {
		return feDePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_UNSPECIFIED
	}
	return feDePb.ElementUtilityType(feStructureType)
}

func convertToFeElementStructureType(beStructureType beDePb.ElementStructureType) feDePb.ElementStructureType {
	feStructureType, ok := feDePb.ElementStructureType_value[beStructureType.String()]
	if !ok {
		return feDePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_UNSPECIFIED
	}
	return feDePb.ElementStructureType(feStructureType)
}

func convertToFeCtaList(beCtaList []*beDePb.DynamicElementCta) []*feDePb.DynamicElementCta {
	var feCtaList []*feDePb.DynamicElementCta
	for _, beElt := range beCtaList {
		feElt := convertToFeCta(beElt)
		if feElt == nil {
			continue
		}
		feCtaList = append(feCtaList, feElt)
	}
	return feCtaList
}

func convertToFeCta(cta *beDePb.DynamicElementCta) *feDePb.DynamicElementCta {
	feCta := &feDePb.DynamicElementCta{
		Type:            convertToFeCtaType(cta.GetType()),
		Text:            cta.GetText(),
		BackgroundColor: cta.GetBackgroundColor(),
		Deeplink:        cta.GetDeeplink(),
		CtaImageUrl:     cta.GetCtaImageUrl(),
		TextColor:       cta.GetTextColor(),
	}
	//nolint:gocritic
	switch cta.GetOptions().(type) {
	case *beDePb.DynamicElementCta_PopUpOptions:
		popUp := cta.GetPopUpOptions()

		feCta.Options = &feDePb.DynamicElementCta_PopUpOptions{
			PopUpOptions: &feDePb.PopUpElementContent{
				Title:                 popUp.GetTitle(),
				Body:                  popUp.GetBody(),
				IconUrl:               popUp.GetIconUrl(),
				BackgroundColor:       popUp.GetBackgroundColor(),
				CtaList:               convertToFeCtaList(popUp.GetCtaList()),
				AdditionalTextSection: convertToFeAdditionalTextSection(popUp.GetAdditionalTextSection()),
			},
		}
	}
	return feCta
}

func convertToFeCtaType(beCtaType beDePb.DynamicElementCta_Type) feDePb.DynamicElementCta_Type {
	feCtaType, ok := feDePb.DynamicElementCta_Type_value[beCtaType.String()]
	if !ok {
		return feDePb.DynamicElementCta_TYPE_UNSPECIFIED
	}
	return feDePb.DynamicElementCta_Type(feCtaType)
}

// nolint:funlen
func convertToFeElementContent(ctx context.Context, beClientContext *beDePb.ClientContext, beEltContent *beDePb.ElementContent, endTime *timestamp.Timestamp, dynamicElementsConfig *genConf.DynamicElementsConfig) (*feDePb.ElementContent, error) {
	feEltContent := &feDePb.ElementContent{}
	switch beEltContent.GetContent().(type) {
	case *beDePb.ElementContent_Banner:
		banner := beEltContent.GetBanner()
		feEltContent.Content = &feDePb.ElementContent_Banner{
			Banner: &feDePb.BannerElementContent{
				Title:           banner.GetTitle(),
				Body:            banner.GetBody(),
				IconUrl:         banner.GetIconUrl(),
				BackgroundColor: banner.GetBackgroundColor(),
				CtaList:         convertToFeCtaList(banner.GetCtaList()),
				Deeplink:        banner.GetDeeplink(),
				TitleTextColor:  banner.GetTitleTextColor(),
				BodyTextColor:   banner.GetBodyTextColor(),
			},
		}
	case *beDePb.ElementContent_BottomSheet:
		bottomSheet := beEltContent.GetBottomSheet()
		feEltContent.Content = &feDePb.ElementContent_BottomSheet{
			BottomSheet: &feDePb.BottomSheetElementContent{
				Title:   bottomSheet.GetTitle(),
				Body:    bottomSheet.GetBody(),
				IconUrl: bottomSheet.GetIconUrl(),
				CtaList: convertToFeCtaList(bottomSheet.GetCtaList()),
			},
		}
	case *beDePb.ElementContent_PopUp:
		popUp := beEltContent.GetPopUp()
		feEltContent.Content = &feDePb.ElementContent_PopUp{
			PopUp: &feDePb.PopUpElementContent{
				Title:                 popUp.GetTitle(),
				Body:                  popUp.GetBody(),
				IconUrl:               popUp.GetIconUrl(),
				BackgroundColor:       popUp.GetBackgroundColor(),
				CtaList:               convertToFeCtaList(popUp.GetCtaList()),
				AdditionalTextSection: convertToFeAdditionalTextSection(popUp.GetAdditionalTextSection()),
			},
		}
	case *beDePb.ElementContent_BannerV2:
		bannerV2 := beEltContent.GetBannerV2()

		var timeCounterComponent *ui.IconTextComponent
		if bannerV2.GetTimeCounterParams() != nil {
			timeCounterComponent = getBannerTimerComponent(bannerV2, endTime)
		}

		bannerElementContentV2UiVariant := convertToFeBannerElementContentV2UiVariant(ctx, dynamicElementsConfig)
		updateBannerV2StyleByContentVariant(beClientContext, bannerV2, bannerElementContentV2UiVariant)

		feEltContent.Content = &feDePb.ElementContent_BannerV2{
			BannerV2: &feDePb.BannerElementContentV2{
				Title:                           bannerV2.GetTitle(),
				Image:                           bannerV2.GetImage(),
				VisualElement:                   bannerV2.GetVisualElement(),
				VisualElementFullBanner:         bannerV2.GetVisualElementFullBanner(),
				Body:                            bannerV2.GetBody(),
				BackgroundColor:                 bannerV2.GetBackgroundColor(),
				CtaList:                         convertToFeCtaList(bannerV2.GetCtaList()),
				Deeplink:                        bannerV2.GetDeeplink(),
				Shadows:                         bannerV2.GetShadows(),
				IndicatorDefaultColor:           bannerV2.GetIndicatorDefaultColor(),
				IndicatorSelectedColor:          bannerV2.GetIndicatorSelectedColor(),
				TimeCounterView:                 timeCounterComponent,
				BannerElementContentV2UiVariant: bannerElementContentV2UiVariant,
			},
		}
	case *beDePb.ElementContent_ScrollableBanner:
		scrollableBanner := beEltContent.GetScrollableBanner()
		feEltContent.Content = &feDePb.ElementContent_ScrollableBanner{
			ScrollableBanner: &feDePb.ScrollableBannerElementContent{
				Header:            convertToFeBannerHeader(scrollableBanner.GetHeader()),
				ScrollingElements: convertToFeBannerScrollingElements(scrollableBanner.GetScrollingElements()),
				BgColour:          scrollableBanner.GetBgColour(),
			},
		}
	case *beDePb.ElementContent_GtmPopUpBanner:
		gtmPopUpBanner, err := convertToFePopUpBanner(beEltContent.GetGtmPopUpBanner())
		if err != nil {
			return nil, fmt.Errorf("invalid content for gtp popup banner, err: %w", err)
		}
		feEltContent.Content = &feDePb.ElementContent_GtmPopUpBanner{
			GtmPopUpBanner: gtmPopUpBanner,
		}
	case *beDePb.ElementContent_FeatureWidgetWithFourPoints:
		feEltContent.Content = &feDePb.ElementContent_FeatureWidgetWithFourPoints{
			FeatureWidgetWithFourPoints: convertToFeFeatureWidgetWithFourPoints(beEltContent.GetFeatureWidgetWithFourPoints()),
		}
	case *beDePb.ElementContent_FeatureWidgetWithThreePoints:
		feEltContent.Content = &feDePb.ElementContent_FeatureWidgetWithThreePoints{
			FeatureWidgetWithThreePoints: convertToFeFeatureWidgetWithThreePoints(beEltContent.GetFeatureWidgetWithThreePoints()),
		}
	case *beDePb.ElementContent_FeatureWidgetWithTwoPoints:
		feEltContent.Content = &feDePb.ElementContent_FeatureWidgetWithTwoPoints{
			FeatureWidgetWithTwoPoints: convertToFeFeatureWidgetWithTwoPoints(beEltContent.GetFeatureWidgetWithTwoPoints()),
		}
	case *beDePb.ElementContent_TabbedCard:
		feEltContent.Content = &feDePb.ElementContent_TabbedCard{
			TabbedCard: convertToFeTabbedCard(beEltContent.GetTabbedCard()),
		}
	case *beDePb.ElementContent_RedirectElement:
		feEltContent.Content = &feDePb.ElementContent_RedirectElement{
			RedirectElement: convertToFeRedirectElementContent(beEltContent.GetRedirectElement()),
		}
	case *beDePb.ElementContent_BannerV3:
		feEltContent.Content = &feDePb.ElementContent_BannerV3{
			BannerV3: &feDePb.BannerElementContentV3{
				Title:              beEltContent.GetBannerV3().GetTitle(),
				Body:               beEltContent.GetBannerV3().GetBody(),
				LeftVisualElement:  beEltContent.GetBannerV3().GetLeftVisualElement(),
				RightVisualElement: beEltContent.GetBannerV3().GetRightVisualElement(),
				BgColour:           beEltContent.GetBannerV3().GetBgColour(),
				Deeplink:           beEltContent.GetBannerV3().GetDeeplink(),
				BorderColour:       beEltContent.GetBannerV3().GetBorderColour(),
			},
		}
	case *beDePb.ElementContent_ProgressBarElement:
		feEltContent.Content = &feDePb.ElementContent_ProgressBarElement{
			ProgressBarElement: &feDePb.ProgressBarCardContent{
				Title:              beEltContent.GetProgressBarElement().GetTitle(),
				LeftVisualElement:  beEltContent.GetProgressBarElement().GetLeftVisualElement(),
				ProgressBar:        beEltContent.GetProgressBarElement().GetProgressBar(),
				RightVisualElement: beEltContent.GetProgressBarElement().GetRightVisualElement(),
				SubtitleContent: lo.Map(beEltContent.GetProgressBarElement().GetSubtitleContent(), func(subtitleContent *beDePb.ProgressBarCardContent_SubtitleContent, _ int) *feDePb.ProgressBarCardContent_SubtitleContent {
					return &feDePb.ProgressBarCardContent_SubtitleContent{
						Subtitle:   subtitleContent.GetSubtitle(),
						TextColour: subtitleContent.GetTextColour(),
					}
				}),
				BgColour: beEltContent.GetProgressBarElement().GetBgColour(),
			},
		}

	default:
		return nil, fmt.Errorf("invalid content type for dynamic element, be element content: %s", beEltContent.String())
	}
	return feEltContent, nil
}

func convertToFeRedirectElementContent(element *beDePb.RedirectElementContent) *feDePb.RedirectElementContent {
	return &feDePb.RedirectElementContent{
		Deeplink: element.GetDeeplink(),
	}
}

func convertToFeTabbedCard(card *beDePb.TabbedCard) *feDePb.TabbedCard {
	tabs := convertToFeTabs(card.GetTabs())
	return &feDePb.TabbedCard{
		Title:                card.GetTitle(),
		Tabs:                 tabs,
		SelectedTabBgColor:   card.GetSelectedTabBgColor(),
		UnselectedTabBgColor: card.GetUnselectedTabBgColor(),
	}
}

func convertToFeTabs(tabs []*beDePb.TabbedCard_Tab) []*feDePb.TabbedCard_Tab {
	var feTabs []*feDePb.TabbedCard_Tab
	for _, t := range tabs {
		var collectiveInfoItems []*feDePb.TabbedCard_Card_CollectiveInfo
		for _, collectiveInfo := range t.GetCard().GetCollectiveInfo().GetCollectiveInfos() {
			collectiveInfoItems = append(collectiveInfoItems, &feDePb.TabbedCard_Card_CollectiveInfo{
				Primary:   collectiveInfo.GetPrimary(),
				Secondary: collectiveInfo.GetSecondary(),
			})
		}

		var chips []*feDePb.TabbedCard_Card_Chip
		for _, chip := range t.GetCard().GetChips() {
			chips = append(chips, &feDePb.TabbedCard_Card_Chip{
				Image:               chip.GetImage(),
				Title:               chip.GetTitle(),
				Subtitle:            chip.GetSubtitle(),
				ShouldTrackRealtime: chip.GetShouldTrackRealtime(),
				StockId:             chip.GetStockId(),
				Deeplink:            chip.GetDeeplink(),
			})
		}

		var collectiveInfo *feDePb.TabbedCard_Card_CollectiveInfoView
		if len(collectiveInfoItems) > 0 {
			collectiveInfo = &feDePb.TabbedCard_Card_CollectiveInfoView{
				BgColour:        t.GetCard().GetCollectiveInfo().GetBgColour(),
				CollectiveInfos: collectiveInfoItems,
				CornerRadius:    t.GetCard().GetCollectiveInfo().GetCornerRadius(),
			}
		}

		feTabs = append(feTabs, &feDePb.TabbedCard_Tab{
			Tab: t.GetTab(),
			Card: &feDePb.TabbedCard_Card{
				CollectiveInfo: collectiveInfo,
				Description:    t.GetCard().GetDescription(),
				Chips:          chips,
				Footer:         t.GetCard().GetFooter(),
				BgColor:        t.GetCard().GetBgColor(),
				CornerRadius:   t.GetCard().GetCornerRadius(),
			},
		})
	}
	return feTabs
}

func convertToFeBannerHeader(beHeader *beDePb.BannerHeader) *feDePb.BannerHeader {
	return &feDePb.BannerHeader{
		Title: beHeader.GetTitle(),
		Cta:   beHeader.GetCta(),
	}
}

func convertToFeBannerScrollingElements(beScrollingElements []*beDePb.BannerSingleShapeElement) []*feDePb.BannerSingleShapeElement {
	var feElement []*feDePb.BannerSingleShapeElement
	for _, scrollingElement := range beScrollingElements {
		feElement = append(feElement, &feDePb.BannerSingleShapeElement{
			Shape:    convertToFeBannerShape(scrollingElement.GetShape()),
			Image:    scrollingElement.GetImage(),
			Title:    scrollingElement.GetTitle(),
			BgColour: scrollingElement.GetBgColour(),
			Shadow:   scrollingElement.GetShadow(),
			Deeplink: scrollingElement.GetDeeplink(),
		})
	}
	return feElement
}

func convertToFeAdditionalTextSection(beSection *beDePb.PopUpElementContent_AdditionalTextSection) *feDePb.PopUpElementContent_AdditionalTextSection {
	return &feDePb.PopUpElementContent_AdditionalTextSection{
		BackgroundColor: beSection.GetBackgroundColor(),
		Texts:           beSection.GetTexts(),
	}
}

func convertToFeBannerShape(beShape beDePb.BannerSingleShapeElement_Shape) feDePb.BannerSingleShapeElement_Shape {
	feShapeType, ok := feDePb.BannerSingleShapeElement_Shape_value[beShape.String()]
	if !ok {
		return feDePb.BannerSingleShapeElement_SHAPE_UNSPECIFIED
	}
	return feDePb.BannerSingleShapeElement_Shape(feShapeType)
}

func fetchTimeCounterString(endTime *timestamp.Timestamp) string {

	currTime := timestamp.Now()
	timeDiff := endTime.AsTime().Sub(currTime.AsTime())
	/*
		1. >= 1d, show days
		2. >= 2hrs, show hours
		3. Show minutes
	*/
	switch {
	case timeDiff.Hours() >= 24:
		return fmt.Sprintf("ENDS IN %d DAYS", int32(timeDiff.Hours()/24))
	case timeDiff.Minutes() >= 120:
		return fmt.Sprintf("ENDS IN %d HRS", int(timeDiff.Hours()))
	case timeDiff.Minutes() > 0:
		return fmt.Sprintf("ENDS IN %d MINS", int(timeDiff.Minutes()))
	default:
		return fmt.Sprintf("ENDS IN 1 MIN")
	}
}

func getBannerTimerComponent(bannerV2 *beDePb.BannerElementContentV2, endTime *timestamp.Timestamp) *ui.IconTextComponent {
	timeCounterText := bannerV2.GetTimeCounterParams().GetTextParams()
	if timeCounterText == nil {
		return nil
	}
	counterTimerText := fetchTimeCounterString(endTime)
	timeCounterText.DisplayValue = &commontypes.Text_PlainString{PlainString: counterTimerText}

	timeCounterComponent := &ui.IconTextComponent{
		Texts:               []*commontypes.Text{timeCounterText},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: bannerV2.GetTimeCounterParams().GetBgColour()},
	}
	return timeCounterComponent
}

func convertToFePopUpBanner(gtmPopUp *beDePb.GTMPopUpBanner) (*feDePb.GTMPopUpBanner, error) {
	res := &feDePb.GTMPopUpBanner{
		Body:                       nil,
		BgColour:                   gtmPopUp.GetBgColour(),
		Ctas:                       convertToFeCtaList(gtmPopUp.GetCtas()),
		Deeplink:                   gtmPopUp.GetDeeplink(),
		BgVisualElement:            gtmPopUp.GetBgVisualElement(),
		StartPopUpAfter:            gtmPopUp.GetStartPopUpAfter(),
		DismissOnClickOutsidePopUp: gtmPopUp.GetDismissOnClickOutsidePopUp(),
	}
	switch gtmPopUp.GetBody().(type) {
	case *beDePb.GTMPopUpBanner_BodyLayoutParagraph_:
		body := gtmPopUp.GetBody().(*beDePb.GTMPopUpBanner_BodyLayoutParagraph_).BodyLayoutParagraph
		if body.GetTitle() == nil {
			return nil, fmt.Errorf("invalid title for gtm pop up")
		}
		if body.GetBodyContent() == nil {
			return nil, fmt.Errorf("invalid body content for gtm pop up")
		}
		if body.GetPopUpVisualElement().GetLottie() == nil && body.GetPopUpVisualElement().GetImage() == nil {
			return nil, fmt.Errorf("invalid visual element for gtm pop up")
		}
		res.Body = &feDePb.GTMPopUpBanner_BodyLayoutParagraph_{
			BodyLayoutParagraph: &feDePb.GTMPopUpBanner_BodyLayoutParagraph{
				Title:              body.GetTitle(),
				BodyContent:        body.GetBodyContent(),
				PopUpVisualElement: body.GetPopUpVisualElement(),
			}}
	case *beDePb.GTMPopUpBanner_BodyLayoutBulletPoints_:
		body := gtmPopUp.GetBody().(*beDePb.GTMPopUpBanner_BodyLayoutBulletPoints_).BodyLayoutBulletPoints
		// Converting bullet point to fe contract
		var feBulletPoints []*feDePb.GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint
		for _, beBulletPoint := range body.GetBulletPoints() {
			feBulletPoints = append(feBulletPoints, &feDePb.GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint{
				Image: beBulletPoint.GetImage(),
				Text:  beBulletPoint.GetText(),
			})
		}
		if body.GetTitle() == nil {
			return nil, fmt.Errorf("invalid title for gtm pop up")
		}
		if len(body.GetBulletPoints()) == 0 {
			return nil, fmt.Errorf("invalid bullet points for gtm pop up")
		}
		if body.GetPopUpVisualElement().GetLottie() == nil && body.GetPopUpVisualElement().GetImage() == nil {
			return nil, fmt.Errorf("invalid visual element for gtm pop up")
		}
		res.Body = &feDePb.GTMPopUpBanner_BodyLayoutBulletPoints_{
			BodyLayoutBulletPoints: &feDePb.GTMPopUpBanner_BodyLayoutBulletPoints{
				Title:              body.GetTitle(),
				BulletPoints:       feBulletPoints,
				PopUpVisualElement: body.GetPopUpVisualElement(),
			}}
	case *beDePb.GTMPopUpBanner_BodyLayoutFullLottie_:
		body := gtmPopUp.GetBody().(*beDePb.GTMPopUpBanner_BodyLayoutFullLottie_).BodyLayoutFullLottie
		if body.GetPopUpVisualElement().GetLottie() == nil && body.GetPopUpVisualElement().GetImage() == nil {
			return nil, fmt.Errorf("invalid visual element for gtm pop up")
		}
		res.Body = &feDePb.GTMPopUpBanner_BodyLayoutFullLottie_{
			BodyLayoutFullLottie: &feDePb.GTMPopUpBanner_BodyLayoutFullLottie{
				PopUpVisualElement: body.GetPopUpVisualElement(),
			}}
	default:
		return nil, fmt.Errorf("invalid body type for gtm pop up")
	}
	return res, nil
}

func convertToFeFeatureWidgetWithTwoPoints(beFeatureWidgetWithTwoPoints *beDePb.FeatureWidgetWithTwoPoints) *feDePb.FeatureWidgetWithTwoPoints {
	return &feDePb.FeatureWidgetWithTwoPoints{
		TopHorizontalFlyer:     convertToFeTopHorizontalFlyer(beFeatureWidgetWithTwoPoints.GetTopHorizontalFlyer()),
		BottomHorizontalFlyers: convertToFeBottomHorizontalFlyers(beFeatureWidgetWithTwoPoints.GetBottomHorizontalFlyers()),
	}
}

func convertToFeBottomHorizontalFlyers(bottomHorizontalFlyers []*beDePb.FeatureWidgetWithTwoPoints_BottomHorizontalFlyer) []*feDePb.FeatureWidgetWithTwoPoints_BottomHorizontalFlyer {
	feBottomHorizontalFlyers := make([]*feDePb.FeatureWidgetWithTwoPoints_BottomHorizontalFlyer, 0)
	for _, bottomHorizontalFlyer := range bottomHorizontalFlyers {
		feBottomHorizontalFlyer := &feDePb.FeatureWidgetWithTwoPoints_BottomHorizontalFlyer{
			PreText:   bottomHorizontalFlyer.GetPreText(),
			Text:      bottomHorizontalFlyer.GetText(),
			RightIcon: bottomHorizontalFlyer.GetRightIcon(),
			Deeplink:  bottomHorizontalFlyer.GetDeeplink(),
			BgColour:  bottomHorizontalFlyer.GetBgColour(),
			Shadow:    bottomHorizontalFlyer.GetShadow(),
		}
		feBottomHorizontalFlyers = append(feBottomHorizontalFlyers, feBottomHorizontalFlyer)
	}
	return feBottomHorizontalFlyers
}

func convertToFeTopHorizontalFlyer(topHorizontalFlyer *beDePb.FeatureWidgetWithTwoPoints_TopHorizontalFlyer) *feDePb.FeatureWidgetWithTwoPoints_TopHorizontalFlyer {
	feTopHorizontalFlyer := &feDePb.FeatureWidgetWithTwoPoints_TopHorizontalFlyer{
		PreHeading:    topHorizontalFlyer.GetPreHeading(),
		VisualElement: topHorizontalFlyer.GetVisualElement(),
		Cta:           topHorizontalFlyer.GetCta(),
		BgColour:      topHorizontalFlyer.GetBgColour(),
		Shadow:        topHorizontalFlyer.GetShadow(),
	}

	switch v := topHorizontalFlyer.GetHeading().(type) {
	case *beDePb.FeatureWidgetWithTwoPoints_TopHorizontalFlyer_Image:
		feTopHorizontalFlyer.Heading = &feDePb.FeatureWidgetWithTwoPoints_TopHorizontalFlyer_Image{Image: v.Image}
	case *beDePb.FeatureWidgetWithTwoPoints_TopHorizontalFlyer_Text:
		feTopHorizontalFlyer.Heading = &feDePb.FeatureWidgetWithTwoPoints_TopHorizontalFlyer_Text{Text: v.Text}
	}
	return feTopHorizontalFlyer
}

func convertToFeFeatureWidgetWithThreePoints(beFeatureWidgetWithThreePoints *beDePb.FeatureWidgetWithThreePoints) *feDePb.FeatureWidgetWithThreePoints {
	return &feDePb.FeatureWidgetWithThreePoints{
		LeftVerticalFlyer:     convertToFeLeftVerticalFlyer(beFeatureWidgetWithThreePoints.GetLeftVerticalFlyer()),
		RightHorizontalFlyers: convertToFeRightHorizontalFlyers(beFeatureWidgetWithThreePoints.GetRightHorizontalFlyers()),
	}
}

func convertToFeRightHorizontalFlyers(rightHorizontalFlyers []*beDePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer) []*feDePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer {
	feRightHorizontalFlyers := make([]*feDePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer, 0)
	for _, rightHorizontalFlyer := range rightHorizontalFlyers {
		feRightHorizontalFlyer := &feDePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer{
			PreText:   rightHorizontalFlyer.GetPreText(),
			Text:      rightHorizontalFlyer.GetText(),
			RightIcon: rightHorizontalFlyer.GetRightIcon(),
			Deeplink:  rightHorizontalFlyer.GetDeeplink(),
			BgColour:  rightHorizontalFlyer.GetBgColour(),
			Shadow:    rightHorizontalFlyer.GetShadow(),
		}
		feRightHorizontalFlyers = append(feRightHorizontalFlyers, feRightHorizontalFlyer)
	}
	return feRightHorizontalFlyers
}

func convertToFeLeftVerticalFlyer(leftVerticalFlyer *beDePb.FeatureWidgetWithThreePoints_LeftVerticalFlyer) *feDePb.FeatureWidgetWithThreePoints_LeftVerticalFlyer {
	return &feDePb.FeatureWidgetWithThreePoints_LeftVerticalFlyer{
		VisualElement: leftVerticalFlyer.GetVisualElement(),
		Cta:           leftVerticalFlyer.GetCta(),
		BgColour:      leftVerticalFlyer.GetBgColour(),
		Shadow:        leftVerticalFlyer.GetShadow(),
	}
}

func convertToFeFeatureWidgetWithFourPoints(beFeatureWidgetWithFourPoints *beDePb.FeatureWidgetWithFourPoints) *feDePb.FeatureWidgetWithFourPoints {
	feFeatureWidgetWithFourPoints := &feDePb.FeatureWidgetWithFourPoints{
		IsCarouselVariant: beFeatureWidgetWithFourPoints.GetIsCarouselVariant(),
	}

	switch v := beFeatureWidgetWithFourPoints.GetCard().(type) {
	case *beDePb.FeatureWidgetWithFourPoints_FullVisualElementCard_:
		feFeatureWidgetWithFourPoints.Card = &feDePb.FeatureWidgetWithFourPoints_FullVisualElementCard_{
			FullVisualElementCard: &feDePb.FeatureWidgetWithFourPoints_FullVisualElementCard{
				VisualElement: v.FullVisualElementCard.GetVisualElement(),
				Cta:           v.FullVisualElementCard.GetCta(),
				BgColour:      v.FullVisualElementCard.GetBgColour(),
				Shadow:        v.FullVisualElementCard.GetShadow(),
			},
		}
	case *beDePb.FeatureWidgetWithFourPoints_TextVisualElementCard_:
		feFeatureWidgetWithFourPoints.Card = &feDePb.FeatureWidgetWithFourPoints_TextVisualElementCard_{
			TextVisualElementCard: &feDePb.FeatureWidgetWithFourPoints_TextVisualElementCard{
				TopSection:    convertToFeTextVisualElementCardTopSection(v.TextVisualElementCard.GetTopSection()),
				MiddleSection: convertToFeTextVisualElementCardMiddleSection(v.TextVisualElementCard.GetMiddleSection()),
				Cta:           v.TextVisualElementCard.GetCta(),
				BgColour:      v.TextVisualElementCard.GetBgColour(),
				Shadow:        v.TextVisualElementCard.GetShadow(),
			},
		}
	}

	return feFeatureWidgetWithFourPoints
}

func convertToFeTextVisualElementCardTopSection(beTextVisualElementCardTopSection *beDePb.FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection) *feDePb.FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection {
	return &feDePb.FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection{
		VisualElement: beTextVisualElementCardTopSection.GetVisualElement(),
	}
}

func convertToFeTextVisualElementCardMiddleSection(beTextVisualElementCardMiddleSection *beDePb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection) *feDePb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection {
	feHighlightedPoints := make([]*feDePb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint, 0)
	beHighlightedPoints := beTextVisualElementCardMiddleSection.GetHighlightedPoints()
	for _, beHighlightedPoint := range beHighlightedPoints {
		feHighlightedPoint := &feDePb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint{
			LeftIcon: beHighlightedPoint.GetLeftIcon(),
			PreText:  beHighlightedPoint.GetPreText(),
			Text:     beHighlightedPoint.GetText(),
		}
		feHighlightedPoints = append(feHighlightedPoints, feHighlightedPoint)
	}

	return &feDePb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection{
		HighlightedPoints: feHighlightedPoints,
	}
}

// filterDynamicElementsForPrimaryFeatureWidget filters dynamic elements to be rendered in primary feature slot on home
func filterDynamicElementsForPrimaryFeatureWidget(ctx context.Context, beDynamicElementsList []*beDePb.DynamicElement) (*commontypes.Text, []*beDePb.DynamicElement) {
	if len(beDynamicElementsList) > 0 {
		return getTitleFromFeatureWidgetContent(ctx, beDynamicElementsList[0].GetContent()), beDynamicElementsList[:1]
	}
	return nil, beDynamicElementsList
}

// filterDynamicElementsForSecondaryFeatureWidget filters dynamic elements to be rendered in secondary feature slot on home
// 1. if there are less than 2 unique owner services, we skip serving the secondary widget since the service already occupies primary widget
// 2. if there are just 2 unique owner services, we can serve the primary version for the second owner service
// 3. if there are more than 2 unique owner services, we have to serve the secondary widget in carousel form for all owner services
func filterDynamicElementsForSecondaryFeatureWidget(ctx context.Context, beDynamicElementsList []*beDePb.DynamicElement) (*commontypes.Text, []*beDePb.DynamicElement) {
	// get unique owner services wanting to serve secondary feature widget
	uniqueOwnerServices := make([]types.ServiceName, 0)
	uniqueOwnerServicesMap := make(map[types.ServiceName]bool)
	for _, dynamicElement := range beDynamicElementsList {
		if _, found := uniqueOwnerServicesMap[dynamicElement.GetOwnerService()]; !found {
			uniqueOwnerServices = append(uniqueOwnerServices, dynamicElement.GetOwnerService())
			uniqueOwnerServicesMap[dynamicElement.GetOwnerService()] = true
		}
	}

	// if there are less than 2 unique owner services, we skip serving the secondary widget since the service already occupies primary widget
	if len(uniqueOwnerServices) < 2 {
		logger.Debug(ctx, "not enough elements to serve secondary feature widget", zap.Any(logger.SERVICE, uniqueOwnerServices))
		return nil, []*beDePb.DynamicElement{}
	}

	// if there are just 2 unique owner services, we can serve the primary version for the second owner service
	if len(uniqueOwnerServices) == 2 {
		for _, dynamicElement := range beDynamicElementsList {
			if dynamicElement.GetOwnerService() == uniqueOwnerServices[0] {
				continue
			}
			return getTitleFromFeatureWidgetContent(ctx, dynamicElement.GetContent()), []*beDePb.DynamicElement{dynamicElement}
		}
	}

	// if there are more than 2 unique owner services, we have to serve the secondary widget in carousel form for all owner services
	title := commontypes.GetPlainStringText("Explore our cool products").WithFontStyle(commontypes.FontStyle_HEADLINE_M).WithFontColor("#38393B")
	carouselDynamicElements := make([]*beDePb.DynamicElement, 0)
	for _, dynamicElement := range beDynamicElementsList {
		if dynamicElement.GetOwnerService() == uniqueOwnerServices[0] {
			continue
		}
		// only four points feature widget is supported for carousel variant
		if dynamicElement.GetStructureType() == beDePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS &&
			dynamicElement.GetContent().GetFeatureWidgetWithFourPoints().GetIsCarouselVariant() {
			carouselDynamicElements = append(carouselDynamicElements, dynamicElement)
		}
	}
	return title, carouselDynamicElements
}

func getTitleFromFeatureWidgetContent(ctx context.Context, elementContent *beDePb.ElementContent) *commontypes.Text {
	switch v := elementContent.GetContent().(type) {
	case *beDePb.ElementContent_FeatureWidgetWithTwoPoints:
		return v.FeatureWidgetWithTwoPoints.GetTitle()
	case *beDePb.ElementContent_FeatureWidgetWithThreePoints:
		return v.FeatureWidgetWithThreePoints.GetTitle()
	case *beDePb.ElementContent_FeatureWidgetWithFourPoints:
		return v.FeatureWidgetWithFourPoints.GetTitle()
	case *beDePb.ElementContent_TabbedCard:
		return nil
	default:
		logger.Error(ctx, "dynamic element content is not of feature widget type", zap.Any(logger.CONTENT_TYPE, elementContent))
		return nil
	}
}

// filterDynamicElementsForBodyBannerV2Widget randomises the order of dynamic elements and apply filters if any
func filterDynamicElementsForBodyBannerV2Widget(beDynamicElementsList []*beDePb.DynamicElement, sessionId string) []*beDePb.DynamicElement {
	rnd := rand.New(rand.NewSource(int64(hash.Hash(sessionId, 0))))
	rnd.Shuffle(len(beDynamicElementsList), func(i, j int) {
		beDynamicElementsList[i], beDynamicElementsList[j] = beDynamicElementsList[j], beDynamicElementsList[i]
	})
	return beDynamicElementsList
}

func getFeDynamicElementsStartIndex(feDynamicElementsList []*feDePb.DynamicElement) int64 {
	rand.Seed(time.Now().UnixNano())

	// generate a random float between [0.0,1.0)
	randomWeight := rand.Float64()

	// find the utility type corresponding to the selected weight
	var chosenUtilityType feDePb.ElementUtilityType
	for utilityType, weight := range utilityTypeToDisplayFirstElementWeightMap {
		randomWeight -= weight
		if randomWeight <= 0 {
			chosenUtilityType = utilityType
			break
		}
	}

	// return the index of element with chosen utility type
	for idx, feDynamicElement := range feDynamicElementsList {
		if feDynamicElement.GetUtilityType() == chosenUtilityType {
			return int64(idx)
		}
	}
	return 0
}

func convertToFeBannerElementContentV2UiVariant(ctx context.Context, dynamicElementsConfig *genConf.DynamicElementsConfig) feDePb.BannerElementContentV2_BannerElementContentV2UiVariant {
	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, dynamicElementsConfig.BannerElementContentV2UiVariantV2FeatureConfig()) {
		return feDePb.BannerElementContentV2_BANNER_ELEMENT_CONTENT_V2_UI_VARIANT_V2
	}
	return feDePb.BannerElementContentV2_BANNER_ELEMENT_CONTENT_V2_UI_VARIANT_UNSPECIFIED
}

func updateBannerV2StyleByContentVariant(beClientContext *beDePb.ClientContext, bannerV2 *beDePb.BannerElementContentV2, variant feDePb.BannerElementContentV2_BannerElementContentV2UiVariant) {
	switch {
	case beClientContext.GetHomeInfo().GetSection() == beDePb.HomeScreenAdditionalInfo_SECTION_BODY && variant == feDePb.BannerElementContentV2_BANNER_ELEMENT_CONTENT_V2_UI_VARIANT_V2:
		if bannerV2.GetTitle() != nil {
			bannerV2.Title.FontStyle = &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M}
			bannerV2.Body = bannerV2.GetTitle()
		}
		if bannerV2.GetImage() != nil {
			bannerV2.Image.Height, bannerV2.Image.Width = 92, 92
			if bannerV2.GetVisualElement() == nil {
				bannerV2.VisualElement = commontypes.GetVisualElementImageFromUrl(bannerV2.GetImage().GetImageUrl()).WithProperties(&commontypes.VisualElementProperties{Height: 92, Width: 92})
			}
		}
	case beClientContext.GetHomeInfo().GetSection() == beDePb.HomeScreenAdditionalInfo_SECTION_BODY && variant == feDePb.BannerElementContentV2_BANNER_ELEMENT_CONTENT_V2_UI_VARIANT_UNSPECIFIED:
		if bannerV2.GetTitle() != nil {
			bannerV2.Title.FontStyle = &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}
		}
		if bannerV2.GetImage() != nil {
			bannerV2.Image.Height, bannerV2.Image.Width = 92, 88
		}
	default:
		return
	}
}
