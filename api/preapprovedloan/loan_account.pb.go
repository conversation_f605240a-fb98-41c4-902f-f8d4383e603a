// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/preapprovedloan/loan_account.proto

package preapprovedloan

import (
	enums "github.com/epifi/gamma/api/preapprovedloan/enums"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RecurringPaymentDetails_RegistrationStatus int32

const (
	RecurringPaymentDetails_REGISTRATION_STATUS_UNSPECIFIED RecurringPaymentDetails_RegistrationStatus = 0
	RecurringPaymentDetails_REGISTRATION_STATUS_SUCCESS     RecurringPaymentDetails_RegistrationStatus = 1
	RecurringPaymentDetails_REGISTRATION_STATUS_FAILURE     RecurringPaymentDetails_RegistrationStatus = 2
	RecurringPaymentDetails_REGISTRATION_STATUS_IN_PROGRESS RecurringPaymentDetails_RegistrationStatus = 3
)

// Enum value maps for RecurringPaymentDetails_RegistrationStatus.
var (
	RecurringPaymentDetails_RegistrationStatus_name = map[int32]string{
		0: "REGISTRATION_STATUS_UNSPECIFIED",
		1: "REGISTRATION_STATUS_SUCCESS",
		2: "REGISTRATION_STATUS_FAILURE",
		3: "REGISTRATION_STATUS_IN_PROGRESS",
	}
	RecurringPaymentDetails_RegistrationStatus_value = map[string]int32{
		"REGISTRATION_STATUS_UNSPECIFIED": 0,
		"REGISTRATION_STATUS_SUCCESS":     1,
		"REGISTRATION_STATUS_FAILURE":     2,
		"REGISTRATION_STATUS_IN_PROGRESS": 3,
	}
)

func (x RecurringPaymentDetails_RegistrationStatus) Enum() *RecurringPaymentDetails_RegistrationStatus {
	p := new(RecurringPaymentDetails_RegistrationStatus)
	*p = x
	return p
}

func (x RecurringPaymentDetails_RegistrationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecurringPaymentDetails_RegistrationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_loan_account_proto_enumTypes[0].Descriptor()
}

func (RecurringPaymentDetails_RegistrationStatus) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_loan_account_proto_enumTypes[0]
}

func (x RecurringPaymentDetails_RegistrationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecurringPaymentDetails_RegistrationStatus.Descriptor instead.
func (RecurringPaymentDetails_RegistrationStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_account_proto_rawDescGZIP(), []int{8, 0}
}

//go:generate gen_sql -types=LoanAccount,LoanAmountInfo,LoanAccountDetails
type LoanAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId        string                 `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Vendor         Vendor                 `protobuf:"varint,3,opt,name=vendor,proto3,enum=preapprovedloan.Vendor" json:"vendor,omitempty"`
	AccountNumber  string                 `protobuf:"bytes,4,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	LoanType       LoanType               `protobuf:"varint,5,opt,name=loan_type,json=loanType,proto3,enum=preapprovedloan.LoanType" json:"loan_type,omitempty"`
	IfscCode       string                 `protobuf:"bytes,6,opt,name=ifsc_code,json=ifscCode,proto3" json:"ifsc_code,omitempty"`
	LoanAmountInfo *LoanAmountInfo        `protobuf:"bytes,7,opt,name=loan_amount_info,json=loanAmountInfo,proto3" json:"loan_amount_info,omitempty"`
	LoanEndDate    *date.Date             `protobuf:"bytes,8,opt,name=loan_end_date,json=loanEndDate,proto3" json:"loan_end_date,omitempty"`
	MaturityDate   *date.Date             `protobuf:"bytes,9,opt,name=maturity_date,json=maturityDate,proto3" json:"maturity_date,omitempty"`
	Details        *LoanAccountDetails    `protobuf:"bytes,10,opt,name=details,proto3" json:"details,omitempty"`
	Status         LoanAccountStatus      `protobuf:"varint,11,opt,name=status,proto3,enum=preapprovedloan.LoanAccountStatus" json:"status,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt      *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	LoanProgram    LoanProgram            `protobuf:"varint,15,opt,name=loan_program,json=loanProgram,proto3,enum=preapprovedloan.LoanProgram" json:"loan_program,omitempty"`
	// denotes the Fi lms partner who is managing this loan account.
	LmsPartner enums.LmsPartner `protobuf:"varint,16,opt,name=lms_partner,json=lmsPartner,proto3,enum=preapprovedloan.enums.LmsPartner" json:"lms_partner,omitempty"`
	// denotes a unique id of loan account in partner lms system.
	LmsPartnerLoanId string `protobuf:"bytes,17,opt,name=lms_partner_loan_id,json=lmsPartnerLoanId,proto3" json:"lms_partner_loan_id,omitempty"`
}

func (x *LoanAccount) Reset() {
	*x = LoanAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanAccount) ProtoMessage() {}

func (x *LoanAccount) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanAccount.ProtoReflect.Descriptor instead.
func (*LoanAccount) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_account_proto_rawDescGZIP(), []int{0}
}

func (x *LoanAccount) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LoanAccount) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *LoanAccount) GetVendor() Vendor {
	if x != nil {
		return x.Vendor
	}
	return Vendor_VENDOR_UNSPECIFIED
}

func (x *LoanAccount) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *LoanAccount) GetLoanType() LoanType {
	if x != nil {
		return x.LoanType
	}
	return LoanType_LOAN_TYPE_UNSPECIFIED
}

func (x *LoanAccount) GetIfscCode() string {
	if x != nil {
		return x.IfscCode
	}
	return ""
}

func (x *LoanAccount) GetLoanAmountInfo() *LoanAmountInfo {
	if x != nil {
		return x.LoanAmountInfo
	}
	return nil
}

func (x *LoanAccount) GetLoanEndDate() *date.Date {
	if x != nil {
		return x.LoanEndDate
	}
	return nil
}

func (x *LoanAccount) GetMaturityDate() *date.Date {
	if x != nil {
		return x.MaturityDate
	}
	return nil
}

func (x *LoanAccount) GetDetails() *LoanAccountDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *LoanAccount) GetStatus() LoanAccountStatus {
	if x != nil {
		return x.Status
	}
	return LoanAccountStatus_LOAN_ACCOUNT_STATUS_UNSPECIFIED
}

func (x *LoanAccount) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LoanAccount) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *LoanAccount) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *LoanAccount) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *LoanAccount) GetLmsPartner() enums.LmsPartner {
	if x != nil {
		return x.LmsPartner
	}
	return enums.LmsPartner(0)
}

func (x *LoanAccount) GetLmsPartnerLoanId() string {
	if x != nil {
		return x.LmsPartnerLoanId
	}
	return ""
}

type LoanAmountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanAmount      *money.Money `protobuf:"bytes,1,opt,name=loan_amount,json=loanAmount,proto3" json:"loan_amount,omitempty"`
	DisbursedAmount *money.Money `protobuf:"bytes,2,opt,name=disbursed_amount,json=disbursedAmount,proto3" json:"disbursed_amount,omitempty"`
	// For LAMF_FIFTYFIN outstanding_amount field stores the outstanding principal amount
	OutstandingAmount  *money.Money `protobuf:"bytes,3,opt,name=outstanding_amount,json=outstandingAmount,proto3" json:"outstanding_amount,omitempty"`
	TotalPayableAmount *money.Money `protobuf:"bytes,4,opt,name=total_payable_amount,json=totalPayableAmount,proto3" json:"total_payable_amount,omitempty"`
}

func (x *LoanAmountInfo) Reset() {
	*x = LoanAmountInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanAmountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanAmountInfo) ProtoMessage() {}

func (x *LoanAmountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanAmountInfo.ProtoReflect.Descriptor instead.
func (*LoanAmountInfo) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_account_proto_rawDescGZIP(), []int{1}
}

func (x *LoanAmountInfo) GetLoanAmount() *money.Money {
	if x != nil {
		return x.LoanAmount
	}
	return nil
}

func (x *LoanAmountInfo) GetDisbursedAmount() *money.Money {
	if x != nil {
		return x.DisbursedAmount
	}
	return nil
}

func (x *LoanAmountInfo) GetOutstandingAmount() *money.Money {
	if x != nil {
		return x.OutstandingAmount
	}
	return nil
}

func (x *LoanAmountInfo) GetTotalPayableAmount() *money.Money {
	if x != nil {
		return x.TotalPayableAmount
	}
	return nil
}

type LoanAccountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InterestRate            float64                  `protobuf:"fixed64,1,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	TenureInMonths          int32                    `protobuf:"varint,2,opt,name=tenure_in_months,json=tenureInMonths,proto3" json:"tenure_in_months,omitempty"`
	LoanName                string                   `protobuf:"bytes,3,opt,name=loan_name,json=loanName,proto3" json:"loan_name,omitempty"`
	AprRate                 float64                  `protobuf:"fixed64,4,opt,name=apr_rate,json=aprRate,proto3" json:"apr_rate,omitempty"`
	LoanDocuments           *LoanDocuments           `protobuf:"bytes,5,opt,name=loan_documents,json=loanDocuments,proto3" json:"loan_documents,omitempty"`
	RecurringPaymentDetails *RecurringPaymentDetails `protobuf:"bytes,6,opt,name=recurring_payment_details,json=recurringPaymentDetails,proto3" json:"recurring_payment_details,omitempty"`
	RepaymentAccountDetails *RepaymentAccountDetails `protobuf:"bytes,7,opt,name=repayment_account_details,json=repaymentAccountDetails,proto3" json:"repayment_account_details,omitempty"`
	CollateralDetails       *CollateralDetails       `protobuf:"bytes,8,opt,name=collateral_details,json=collateralDetails,proto3" json:"collateral_details,omitempty"`
	LoanStartDate           *date.Date               `protobuf:"bytes,9,opt,name=loan_start_date,json=loanStartDate,proto3" json:"loan_start_date,omitempty"`
	LenderAccountDetails    *LenderAccountDetails    `protobuf:"bytes,10,opt,name=lender_account_details,json=lenderAccountDetails,proto3" json:"lender_account_details,omitempty"`
	// denotes the product in partner lms system with which the account is associated.
	LmsPartnerProductId string `protobuf:"bytes,11,opt,name=lms_partner_product_id,json=lmsPartnerProductId,proto3" json:"lms_partner_product_id,omitempty"`
	// timestamp when the loan account was last synced with the partner lms system.
	LmsSyncedAt        *timestamppb.Timestamp   `protobuf:"bytes,12,opt,name=lms_synced_at,json=lmsSyncedAt,proto3" json:"lms_synced_at,omitempty"`
	LoanProgramVersion enums.LoanProgramVersion `protobuf:"varint,13,opt,name=loan_program_version,json=loanProgramVersion,proto3,enum=preapprovedloan.enums.LoanProgramVersion" json:"loan_program_version,omitempty"`
}

func (x *LoanAccountDetails) Reset() {
	*x = LoanAccountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanAccountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanAccountDetails) ProtoMessage() {}

func (x *LoanAccountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanAccountDetails.ProtoReflect.Descriptor instead.
func (*LoanAccountDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_account_proto_rawDescGZIP(), []int{2}
}

func (x *LoanAccountDetails) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

func (x *LoanAccountDetails) GetTenureInMonths() int32 {
	if x != nil {
		return x.TenureInMonths
	}
	return 0
}

func (x *LoanAccountDetails) GetLoanName() string {
	if x != nil {
		return x.LoanName
	}
	return ""
}

func (x *LoanAccountDetails) GetAprRate() float64 {
	if x != nil {
		return x.AprRate
	}
	return 0
}

func (x *LoanAccountDetails) GetLoanDocuments() *LoanDocuments {
	if x != nil {
		return x.LoanDocuments
	}
	return nil
}

func (x *LoanAccountDetails) GetRecurringPaymentDetails() *RecurringPaymentDetails {
	if x != nil {
		return x.RecurringPaymentDetails
	}
	return nil
}

func (x *LoanAccountDetails) GetRepaymentAccountDetails() *RepaymentAccountDetails {
	if x != nil {
		return x.RepaymentAccountDetails
	}
	return nil
}

func (x *LoanAccountDetails) GetCollateralDetails() *CollateralDetails {
	if x != nil {
		return x.CollateralDetails
	}
	return nil
}

func (x *LoanAccountDetails) GetLoanStartDate() *date.Date {
	if x != nil {
		return x.LoanStartDate
	}
	return nil
}

func (x *LoanAccountDetails) GetLenderAccountDetails() *LenderAccountDetails {
	if x != nil {
		return x.LenderAccountDetails
	}
	return nil
}

func (x *LoanAccountDetails) GetLmsPartnerProductId() string {
	if x != nil {
		return x.LmsPartnerProductId
	}
	return ""
}

func (x *LoanAccountDetails) GetLmsSyncedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LmsSyncedAt
	}
	return nil
}

func (x *LoanAccountDetails) GetLoanProgramVersion() enums.LoanProgramVersion {
	if x != nil {
		return x.LoanProgramVersion
	}
	return enums.LoanProgramVersion(0)
}

type LoanAccountVendorSpecificDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Details:
	//
	//	*LoanAccountVendorSpecificDetails_MoneyviewDetails
	//	*LoanAccountVendorSpecificDetails_AbflDetails
	Details isLoanAccountVendorSpecificDetails_Details `protobuf_oneof:"details"`
}

func (x *LoanAccountVendorSpecificDetails) Reset() {
	*x = LoanAccountVendorSpecificDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanAccountVendorSpecificDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanAccountVendorSpecificDetails) ProtoMessage() {}

func (x *LoanAccountVendorSpecificDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanAccountVendorSpecificDetails.ProtoReflect.Descriptor instead.
func (*LoanAccountVendorSpecificDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_account_proto_rawDescGZIP(), []int{3}
}

func (m *LoanAccountVendorSpecificDetails) GetDetails() isLoanAccountVendorSpecificDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *LoanAccountVendorSpecificDetails) GetMoneyviewDetails() *MoneyViewLoanAccountDetails {
	if x, ok := x.GetDetails().(*LoanAccountVendorSpecificDetails_MoneyviewDetails); ok {
		return x.MoneyviewDetails
	}
	return nil
}

func (x *LoanAccountVendorSpecificDetails) GetAbflDetails() *AbflLoanAccountDetails {
	if x, ok := x.GetDetails().(*LoanAccountVendorSpecificDetails_AbflDetails); ok {
		return x.AbflDetails
	}
	return nil
}

type isLoanAccountVendorSpecificDetails_Details interface {
	isLoanAccountVendorSpecificDetails_Details()
}

type LoanAccountVendorSpecificDetails_MoneyviewDetails struct {
	MoneyviewDetails *MoneyViewLoanAccountDetails `protobuf:"bytes,1,opt,name=moneyview_details,json=moneyviewDetails,proto3,oneof"`
}

type LoanAccountVendorSpecificDetails_AbflDetails struct {
	AbflDetails *AbflLoanAccountDetails `protobuf:"bytes,2,opt,name=abfl_details,json=abflDetails,proto3,oneof"`
}

func (*LoanAccountVendorSpecificDetails_MoneyviewDetails) isLoanAccountVendorSpecificDetails_Details() {
}

func (*LoanAccountVendorSpecificDetails_AbflDetails) isLoanAccountVendorSpecificDetails_Details() {}

type MoneyViewLoanAccountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the id of the lead which was used for initiating the loan application journey, needed for generating pwa url for loan servicing flows.
	LeadId string `protobuf:"bytes,1,opt,name=lead_id,json=leadId,proto3" json:"lead_id,omitempty"`
}

func (x *MoneyViewLoanAccountDetails) Reset() {
	*x = MoneyViewLoanAccountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MoneyViewLoanAccountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoneyViewLoanAccountDetails) ProtoMessage() {}

func (x *MoneyViewLoanAccountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoneyViewLoanAccountDetails.ProtoReflect.Descriptor instead.
func (*MoneyViewLoanAccountDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_account_proto_rawDescGZIP(), []int{4}
}

func (x *MoneyViewLoanAccountDetails) GetLeadId() string {
	if x != nil {
		return x.LeadId
	}
	return ""
}

type AbflLoanAccountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DealNumber string `protobuf:"bytes,1,opt,name=deal_number,json=dealNumber,proto3" json:"deal_number,omitempty"`
	AccountId  string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // Vendor request id
}

func (x *AbflLoanAccountDetails) Reset() {
	*x = AbflLoanAccountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AbflLoanAccountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AbflLoanAccountDetails) ProtoMessage() {}

func (x *AbflLoanAccountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AbflLoanAccountDetails.ProtoReflect.Descriptor instead.
func (*AbflLoanAccountDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_account_proto_rawDescGZIP(), []int{5}
}

func (x *AbflLoanAccountDetails) GetDealNumber() string {
	if x != nil {
		return x.DealNumber
	}
	return ""
}

func (x *AbflLoanAccountDetails) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

// This can be used for storing Loan account details of the loan providing entity.
// Incase of fiftyfin lamf, bajaj is the loan providing entity. This object will store loan account details present with Bajaj.
type LenderAccountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountNumber string `protobuf:"bytes,1,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// denotes the vendor specific details for the loan account.
	VendorSpecificDetails *LoanAccountVendorSpecificDetails `protobuf:"bytes,2,opt,name=vendor_specific_details,json=vendorSpecificDetails,proto3" json:"vendor_specific_details,omitempty"`
}

func (x *LenderAccountDetails) Reset() {
	*x = LenderAccountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LenderAccountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LenderAccountDetails) ProtoMessage() {}

func (x *LenderAccountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LenderAccountDetails.ProtoReflect.Descriptor instead.
func (*LenderAccountDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_account_proto_rawDescGZIP(), []int{6}
}

func (x *LenderAccountDetails) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *LenderAccountDetails) GetVendorSpecificDetails() *LoanAccountVendorSpecificDetails {
	if x != nil {
		return x.VendorSpecificDetails
	}
	return nil
}

type LoanDocuments struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountStatementPdfUrl string `protobuf:"bytes,1,opt,name=account_statement_pdf_url,json=accountStatementPdfUrl,proto3" json:"account_statement_pdf_url,omitempty"`
}

func (x *LoanDocuments) Reset() {
	*x = LoanDocuments{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanDocuments) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanDocuments) ProtoMessage() {}

func (x *LoanDocuments) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanDocuments.ProtoReflect.Descriptor instead.
func (*LoanDocuments) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_account_proto_rawDescGZIP(), []int{7}
}

func (x *LoanDocuments) GetAccountStatementPdfUrl() string {
	if x != nil {
		return x.AccountStatementPdfUrl
	}
	return ""
}

type RecurringPaymentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 string                                     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	RegistrationStatus RecurringPaymentDetails_RegistrationStatus `protobuf:"varint,2,opt,name=registration_status,json=registrationStatus,proto3,enum=preapprovedloan.RecurringPaymentDetails_RegistrationStatus" json:"registration_status,omitempty"`
}

func (x *RecurringPaymentDetails) Reset() {
	*x = RecurringPaymentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecurringPaymentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecurringPaymentDetails) ProtoMessage() {}

func (x *RecurringPaymentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecurringPaymentDetails.ProtoReflect.Descriptor instead.
func (*RecurringPaymentDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_account_proto_rawDescGZIP(), []int{8}
}

func (x *RecurringPaymentDetails) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RecurringPaymentDetails) GetRegistrationStatus() RecurringPaymentDetails_RegistrationStatus {
	if x != nil {
		return x.RegistrationStatus
	}
	return RecurringPaymentDetails_REGISTRATION_STATUS_UNSPECIFIED
}

type RepaymentAccountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to AccountDetails:
	//
	//	*RepaymentAccountDetails_FiftyFinRepaymentAccountDetails
	AccountDetails isRepaymentAccountDetails_AccountDetails `protobuf_oneof:"AccountDetails"`
}

func (x *RepaymentAccountDetails) Reset() {
	*x = RepaymentAccountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepaymentAccountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepaymentAccountDetails) ProtoMessage() {}

func (x *RepaymentAccountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepaymentAccountDetails.ProtoReflect.Descriptor instead.
func (*RepaymentAccountDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_account_proto_rawDescGZIP(), []int{9}
}

func (m *RepaymentAccountDetails) GetAccountDetails() isRepaymentAccountDetails_AccountDetails {
	if m != nil {
		return m.AccountDetails
	}
	return nil
}

func (x *RepaymentAccountDetails) GetFiftyFinRepaymentAccountDetails() *FiftyFinRepaymentAccountDetails {
	if x, ok := x.GetAccountDetails().(*RepaymentAccountDetails_FiftyFinRepaymentAccountDetails); ok {
		return x.FiftyFinRepaymentAccountDetails
	}
	return nil
}

type isRepaymentAccountDetails_AccountDetails interface {
	isRepaymentAccountDetails_AccountDetails()
}

type RepaymentAccountDetails_FiftyFinRepaymentAccountDetails struct {
	FiftyFinRepaymentAccountDetails *FiftyFinRepaymentAccountDetails `protobuf:"bytes,1,opt,name=fifty_fin_repayment_account_details,json=fiftyFinRepaymentAccountDetails,proto3,oneof"`
}

func (*RepaymentAccountDetails_FiftyFinRepaymentAccountDetails) isRepaymentAccountDetails_AccountDetails() {
}

type FiftyFinRepaymentAccountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrincipalRepaymentAccountDetails *typesv2.BankAccountDetails `protobuf:"bytes,1,opt,name=principal_repayment_account_details,json=principalRepaymentAccountDetails,proto3" json:"principal_repayment_account_details,omitempty"`
	InterestRepaymentAccountDetails  *typesv2.BankAccountDetails `protobuf:"bytes,2,opt,name=interest_repayment_account_details,json=interestRepaymentAccountDetails,proto3" json:"interest_repayment_account_details,omitempty"`
}

func (x *FiftyFinRepaymentAccountDetails) Reset() {
	*x = FiftyFinRepaymentAccountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiftyFinRepaymentAccountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiftyFinRepaymentAccountDetails) ProtoMessage() {}

func (x *FiftyFinRepaymentAccountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_account_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiftyFinRepaymentAccountDetails.ProtoReflect.Descriptor instead.
func (*FiftyFinRepaymentAccountDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_account_proto_rawDescGZIP(), []int{10}
}

func (x *FiftyFinRepaymentAccountDetails) GetPrincipalRepaymentAccountDetails() *typesv2.BankAccountDetails {
	if x != nil {
		return x.PrincipalRepaymentAccountDetails
	}
	return nil
}

func (x *FiftyFinRepaymentAccountDetails) GetInterestRepaymentAccountDetails() *typesv2.BankAccountDetails {
	if x != nil {
		return x.InterestRepaymentAccountDetails
	}
	return nil
}

var File_api_preapprovedloan_loan_account_proto protoreflect.FileDescriptor

var file_api_preapprovedloan_loan_account_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x63,
	0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x6c, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x62, 0x61, 0x6e, 0x6b,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e,
	0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xff, 0x06, 0x0a, 0x0b, 0x4c, 0x6f, 0x61,
	0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x09, 0x6c,
	0x6f, 0x61, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x6c, 0x6f, 0x61, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x66, 0x73, 0x63, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x66, 0x73, 0x63, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x49, 0x0a, 0x10, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61,
	0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x6c, 0x6f, 0x61,
	0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x35, 0x0a, 0x0d, 0x6c,
	0x6f, 0x61, 0x6e, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x45, 0x6e, 0x64, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x36, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x6d, 0x61,
	0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x07, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f,
	0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3a, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3f, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c,
	0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x42, 0x0a, 0x0b, 0x6c, 0x6d, 0x73, 0x5f, 0x70,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4c, 0x6d, 0x73, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x52,
	0x0a, 0x6c, 0x6d, 0x73, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x13, 0x6c,
	0x6d, 0x73, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6c, 0x6d, 0x73, 0x50, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x22, 0x8d, 0x02, 0x0a, 0x0e, 0x4c,
	0x6f, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a,
	0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x64, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0f, 0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x41, 0x0a, 0x12, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x11, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x44, 0x0a, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61,
	0x79, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x79,
	0x61, 0x62, 0x6c, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xeb, 0x06, 0x0a, 0x12, 0x4c,
	0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65,
	0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65,
	0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73,
	0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x70, 0x72, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x07, 0x61, 0x70, 0x72, 0x52, 0x61, 0x74, 0x65, 0x12, 0x45, 0x0a, 0x0e, 0x6c, 0x6f, 0x61, 0x6e,
	0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x0d, 0x6c, 0x6f, 0x61, 0x6e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x64, 0x0a, 0x19, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x17, 0x72, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x64, 0x0a, 0x19, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x17, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x51, 0x0a, 0x12, 0x63,
	0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x74,
	0x65, 0x72, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x63, 0x6f, 0x6c,
	0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39,
	0x0a, 0x0f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x6c, 0x6f, 0x61, 0x6e,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x5b, 0x0a, 0x16, 0x6c, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x14, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x33, 0x0a, 0x16, 0x6c, 0x6d, 0x73, 0x5f, 0x70, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6c, 0x6d, 0x73, 0x50, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0d, 0x6c,
	0x6d, 0x73, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b,
	0x6c, 0x6d, 0x73, 0x53, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x41, 0x74, 0x12, 0x5b, 0x0a, 0x14, 0x6c,
	0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xd8, 0x01, 0x0a, 0x20, 0x4c, 0x6f, 0x61,
	0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5b, 0x0a,
	0x11, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x56, 0x69, 0x65, 0x77, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x10, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x76,
	0x69, 0x65, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4c, 0x0a, 0x0c, 0x61, 0x62,
	0x66, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x41, 0x62, 0x66, 0x6c, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0b, 0x61, 0x62, 0x66,
	0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x22, 0x36, 0x0a, 0x1b, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x56, 0x69, 0x65, 0x77,
	0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x65, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x65, 0x61, 0x64, 0x49, 0x64, 0x22, 0x58, 0x0a, 0x16, 0x41,
	0x62, 0x66, 0x6c, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x61, 0x6c,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xa8, 0x01, 0x0a, 0x14, 0x4c, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x69, 0x0a, 0x17, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x15, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x22, 0x4a, 0x0a, 0x0d, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x39, 0x0a, 0x19, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x64, 0x66, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x64, 0x66, 0x55, 0x72, 0x6c, 0x22, 0xba, 0x02, 0x0a,
	0x17, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x6c, 0x0a, 0x13, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x12, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a,
	0x1f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55,
	0x52, 0x45, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x03, 0x22, 0xae, 0x01, 0x0a, 0x17, 0x52, 0x65,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x80, 0x01, 0x0a, 0x23, 0x66, 0x69, 0x66, 0x74, 0x79, 0x5f,
	0x66, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x66, 0x74, 0x79, 0x46, 0x69, 0x6e, 0x52, 0x65,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x1f, 0x66, 0x69, 0x66, 0x74, 0x79, 0x46, 0x69,
	0x6e, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x10, 0x0a, 0x0e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xff, 0x01, 0x0a, 0x1f, 0x46,
	0x69, 0x66, 0x74, 0x79, 0x46, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x6e,
	0x0a, 0x23, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x20, 0x70, 0x72,
	0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x6c,
	0x0a, 0x22, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x1f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x58, 0x0a, 0x2a,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_preapprovedloan_loan_account_proto_rawDescOnce sync.Once
	file_api_preapprovedloan_loan_account_proto_rawDescData = file_api_preapprovedloan_loan_account_proto_rawDesc
)

func file_api_preapprovedloan_loan_account_proto_rawDescGZIP() []byte {
	file_api_preapprovedloan_loan_account_proto_rawDescOnce.Do(func() {
		file_api_preapprovedloan_loan_account_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_preapprovedloan_loan_account_proto_rawDescData)
	})
	return file_api_preapprovedloan_loan_account_proto_rawDescData
}

var file_api_preapprovedloan_loan_account_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_preapprovedloan_loan_account_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_preapprovedloan_loan_account_proto_goTypes = []interface{}{
	(RecurringPaymentDetails_RegistrationStatus)(0), // 0: preapprovedloan.RecurringPaymentDetails.RegistrationStatus
	(*LoanAccount)(nil),                             // 1: preapprovedloan.LoanAccount
	(*LoanAmountInfo)(nil),                          // 2: preapprovedloan.LoanAmountInfo
	(*LoanAccountDetails)(nil),                      // 3: preapprovedloan.LoanAccountDetails
	(*LoanAccountVendorSpecificDetails)(nil),        // 4: preapprovedloan.LoanAccountVendorSpecificDetails
	(*MoneyViewLoanAccountDetails)(nil),             // 5: preapprovedloan.MoneyViewLoanAccountDetails
	(*AbflLoanAccountDetails)(nil),                  // 6: preapprovedloan.AbflLoanAccountDetails
	(*LenderAccountDetails)(nil),                    // 7: preapprovedloan.LenderAccountDetails
	(*LoanDocuments)(nil),                           // 8: preapprovedloan.LoanDocuments
	(*RecurringPaymentDetails)(nil),                 // 9: preapprovedloan.RecurringPaymentDetails
	(*RepaymentAccountDetails)(nil),                 // 10: preapprovedloan.RepaymentAccountDetails
	(*FiftyFinRepaymentAccountDetails)(nil),         // 11: preapprovedloan.FiftyFinRepaymentAccountDetails
	(Vendor)(0),                                     // 12: preapprovedloan.Vendor
	(LoanType)(0),                                   // 13: preapprovedloan.LoanType
	(*date.Date)(nil),                               // 14: google.type.Date
	(LoanAccountStatus)(0),                          // 15: preapprovedloan.LoanAccountStatus
	(*timestamppb.Timestamp)(nil),                   // 16: google.protobuf.Timestamp
	(LoanProgram)(0),                                // 17: preapprovedloan.LoanProgram
	(enums.LmsPartner)(0),                           // 18: preapprovedloan.enums.LmsPartner
	(*money.Money)(nil),                             // 19: google.type.Money
	(*CollateralDetails)(nil),                       // 20: preapprovedloan.CollateralDetails
	(enums.LoanProgramVersion)(0),                   // 21: preapprovedloan.enums.LoanProgramVersion
	(*typesv2.BankAccountDetails)(nil),              // 22: api.typesv2.BankAccountDetails
}
var file_api_preapprovedloan_loan_account_proto_depIdxs = []int32{
	12, // 0: preapprovedloan.LoanAccount.vendor:type_name -> preapprovedloan.Vendor
	13, // 1: preapprovedloan.LoanAccount.loan_type:type_name -> preapprovedloan.LoanType
	2,  // 2: preapprovedloan.LoanAccount.loan_amount_info:type_name -> preapprovedloan.LoanAmountInfo
	14, // 3: preapprovedloan.LoanAccount.loan_end_date:type_name -> google.type.Date
	14, // 4: preapprovedloan.LoanAccount.maturity_date:type_name -> google.type.Date
	3,  // 5: preapprovedloan.LoanAccount.details:type_name -> preapprovedloan.LoanAccountDetails
	15, // 6: preapprovedloan.LoanAccount.status:type_name -> preapprovedloan.LoanAccountStatus
	16, // 7: preapprovedloan.LoanAccount.created_at:type_name -> google.protobuf.Timestamp
	16, // 8: preapprovedloan.LoanAccount.updated_at:type_name -> google.protobuf.Timestamp
	16, // 9: preapprovedloan.LoanAccount.deleted_at:type_name -> google.protobuf.Timestamp
	17, // 10: preapprovedloan.LoanAccount.loan_program:type_name -> preapprovedloan.LoanProgram
	18, // 11: preapprovedloan.LoanAccount.lms_partner:type_name -> preapprovedloan.enums.LmsPartner
	19, // 12: preapprovedloan.LoanAmountInfo.loan_amount:type_name -> google.type.Money
	19, // 13: preapprovedloan.LoanAmountInfo.disbursed_amount:type_name -> google.type.Money
	19, // 14: preapprovedloan.LoanAmountInfo.outstanding_amount:type_name -> google.type.Money
	19, // 15: preapprovedloan.LoanAmountInfo.total_payable_amount:type_name -> google.type.Money
	8,  // 16: preapprovedloan.LoanAccountDetails.loan_documents:type_name -> preapprovedloan.LoanDocuments
	9,  // 17: preapprovedloan.LoanAccountDetails.recurring_payment_details:type_name -> preapprovedloan.RecurringPaymentDetails
	10, // 18: preapprovedloan.LoanAccountDetails.repayment_account_details:type_name -> preapprovedloan.RepaymentAccountDetails
	20, // 19: preapprovedloan.LoanAccountDetails.collateral_details:type_name -> preapprovedloan.CollateralDetails
	14, // 20: preapprovedloan.LoanAccountDetails.loan_start_date:type_name -> google.type.Date
	7,  // 21: preapprovedloan.LoanAccountDetails.lender_account_details:type_name -> preapprovedloan.LenderAccountDetails
	16, // 22: preapprovedloan.LoanAccountDetails.lms_synced_at:type_name -> google.protobuf.Timestamp
	21, // 23: preapprovedloan.LoanAccountDetails.loan_program_version:type_name -> preapprovedloan.enums.LoanProgramVersion
	5,  // 24: preapprovedloan.LoanAccountVendorSpecificDetails.moneyview_details:type_name -> preapprovedloan.MoneyViewLoanAccountDetails
	6,  // 25: preapprovedloan.LoanAccountVendorSpecificDetails.abfl_details:type_name -> preapprovedloan.AbflLoanAccountDetails
	4,  // 26: preapprovedloan.LenderAccountDetails.vendor_specific_details:type_name -> preapprovedloan.LoanAccountVendorSpecificDetails
	0,  // 27: preapprovedloan.RecurringPaymentDetails.registration_status:type_name -> preapprovedloan.RecurringPaymentDetails.RegistrationStatus
	11, // 28: preapprovedloan.RepaymentAccountDetails.fifty_fin_repayment_account_details:type_name -> preapprovedloan.FiftyFinRepaymentAccountDetails
	22, // 29: preapprovedloan.FiftyFinRepaymentAccountDetails.principal_repayment_account_details:type_name -> api.typesv2.BankAccountDetails
	22, // 30: preapprovedloan.FiftyFinRepaymentAccountDetails.interest_repayment_account_details:type_name -> api.typesv2.BankAccountDetails
	31, // [31:31] is the sub-list for method output_type
	31, // [31:31] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_api_preapprovedloan_loan_account_proto_init() }
func file_api_preapprovedloan_loan_account_proto_init() {
	if File_api_preapprovedloan_loan_account_proto != nil {
		return
	}
	file_api_preapprovedloan_collateral_proto_init()
	file_api_preapprovedloan_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_preapprovedloan_loan_account_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_account_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanAmountInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_account_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanAccountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_account_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanAccountVendorSpecificDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_account_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MoneyViewLoanAccountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_account_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AbflLoanAccountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_account_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LenderAccountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_account_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanDocuments); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_account_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecurringPaymentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_account_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepaymentAccountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_account_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiftyFinRepaymentAccountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_preapprovedloan_loan_account_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*LoanAccountVendorSpecificDetails_MoneyviewDetails)(nil),
		(*LoanAccountVendorSpecificDetails_AbflDetails)(nil),
	}
	file_api_preapprovedloan_loan_account_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*RepaymentAccountDetails_FiftyFinRepaymentAccountDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_preapprovedloan_loan_account_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_preapprovedloan_loan_account_proto_goTypes,
		DependencyIndexes: file_api_preapprovedloan_loan_account_proto_depIdxs,
		EnumInfos:         file_api_preapprovedloan_loan_account_proto_enumTypes,
		MessageInfos:      file_api_preapprovedloan_loan_account_proto_msgTypes,
	}.Build()
	File_api_preapprovedloan_loan_account_proto = out.File
	file_api_preapprovedloan_loan_account_proto_rawDesc = nil
	file_api_preapprovedloan_loan_account_proto_goTypes = nil
	file_api_preapprovedloan_loan_account_proto_depIdxs = nil
}
