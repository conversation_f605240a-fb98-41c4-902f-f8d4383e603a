// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/preapprovedloan/loan_info.proto

package preapprovedloan

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LoanInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanAccount         *LoanAccount       `protobuf:"bytes,1,opt,name=loan_account,json=loanAccount,proto3" json:"loan_account,omitempty"`
	InstallmentInfoList []*InstallmentInfo `protobuf:"bytes,2,rep,name=installment_info_list,json=installmentInfoList,proto3" json:"installment_info_list,omitempty"`
}

func (x *LoanInfo) Reset() {
	*x = LoanInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_info_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanInfo) ProtoMessage() {}

func (x *LoanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_info_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanInfo.ProtoReflect.Descriptor instead.
func (*LoanInfo) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_info_proto_rawDescGZIP(), []int{0}
}

func (x *LoanInfo) GetLoanAccount() *LoanAccount {
	if x != nil {
		return x.LoanAccount
	}
	return nil
}

func (x *LoanInfo) GetInstallmentInfoList() []*InstallmentInfo {
	if x != nil {
		return x.InstallmentInfoList
	}
	return nil
}

type LoanRequestInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lr        *LoanRequest       `protobuf:"bytes,1,opt,name=lr,proto3" json:"lr,omitempty"`
	LatestLse *LoanStepExecution `protobuf:"bytes,2,opt,name=latest_lse,json=latestLse,proto3" json:"latest_lse,omitempty"`
}

func (x *LoanRequestInfo) Reset() {
	*x = LoanRequestInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_info_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanRequestInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanRequestInfo) ProtoMessage() {}

func (x *LoanRequestInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_info_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanRequestInfo.ProtoReflect.Descriptor instead.
func (*LoanRequestInfo) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_info_proto_rawDescGZIP(), []int{1}
}

func (x *LoanRequestInfo) GetLr() *LoanRequest {
	if x != nil {
		return x.Lr
	}
	return nil
}

func (x *LoanRequestInfo) GetLatestLse() *LoanStepExecution {
	if x != nil {
		return x.LatestLse
	}
	return nil
}

type InstallmentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanInstallmentInfo    *LoanInstallmentInfo     `protobuf:"bytes,1,opt,name=loan_installment_info,json=loanInstallmentInfo,proto3" json:"loan_installment_info,omitempty"`
	LoanInstallmentPayouts []*LoanInstallmentPayout `protobuf:"bytes,2,rep,name=loan_installment_payouts,json=loanInstallmentPayouts,proto3" json:"loan_installment_payouts,omitempty"`
}

func (x *InstallmentInfo) Reset() {
	*x = InstallmentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_info_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstallmentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallmentInfo) ProtoMessage() {}

func (x *InstallmentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_info_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallmentInfo.ProtoReflect.Descriptor instead.
func (*InstallmentInfo) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_info_proto_rawDescGZIP(), []int{2}
}

func (x *InstallmentInfo) GetLoanInstallmentInfo() *LoanInstallmentInfo {
	if x != nil {
		return x.LoanInstallmentInfo
	}
	return nil
}

func (x *InstallmentInfo) GetLoanInstallmentPayouts() []*LoanInstallmentPayout {
	if x != nil {
		return x.LoanInstallmentPayouts
	}
	return nil
}

var File_api_preapprovedloan_loan_info_proto protoreflect.FileDescriptor

var file_api_preapprovedloan_loan_info_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x6f, 0x61, 0x6e,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x31, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa1, 0x01, 0x0a, 0x08, 0x4c, 0x6f,
	0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3f, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c,
	0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x54, 0x0a, 0x15, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x13, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x82, 0x01,
	0x0a, 0x0f, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x2c, 0x0a, 0x02, 0x6c, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x02, 0x6c, 0x72, 0x12,
	0x41, 0x0a, 0x0a, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6c, 0x73, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x4c,
	0x73, 0x65, 0x22, 0xcd, 0x01, 0x0a, 0x0f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x58, 0x0a, 0x15, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x13, 0x6c, 0x6f, 0x61,
	0x6e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x60, 0x0a, 0x18, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x52, 0x16, 0x6c, 0x6f, 0x61, 0x6e,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6f, 0x75,
	0x74, 0x73, 0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_preapprovedloan_loan_info_proto_rawDescOnce sync.Once
	file_api_preapprovedloan_loan_info_proto_rawDescData = file_api_preapprovedloan_loan_info_proto_rawDesc
)

func file_api_preapprovedloan_loan_info_proto_rawDescGZIP() []byte {
	file_api_preapprovedloan_loan_info_proto_rawDescOnce.Do(func() {
		file_api_preapprovedloan_loan_info_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_preapprovedloan_loan_info_proto_rawDescData)
	})
	return file_api_preapprovedloan_loan_info_proto_rawDescData
}

var file_api_preapprovedloan_loan_info_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_preapprovedloan_loan_info_proto_goTypes = []interface{}{
	(*LoanInfo)(nil),              // 0: preapprovedloan.LoanInfo
	(*LoanRequestInfo)(nil),       // 1: preapprovedloan.LoanRequestInfo
	(*InstallmentInfo)(nil),       // 2: preapprovedloan.InstallmentInfo
	(*LoanAccount)(nil),           // 3: preapprovedloan.LoanAccount
	(*LoanRequest)(nil),           // 4: preapprovedloan.LoanRequest
	(*LoanStepExecution)(nil),     // 5: preapprovedloan.LoanStepExecution
	(*LoanInstallmentInfo)(nil),   // 6: preapprovedloan.LoanInstallmentInfo
	(*LoanInstallmentPayout)(nil), // 7: preapprovedloan.LoanInstallmentPayout
}
var file_api_preapprovedloan_loan_info_proto_depIdxs = []int32{
	3, // 0: preapprovedloan.LoanInfo.loan_account:type_name -> preapprovedloan.LoanAccount
	2, // 1: preapprovedloan.LoanInfo.installment_info_list:type_name -> preapprovedloan.InstallmentInfo
	4, // 2: preapprovedloan.LoanRequestInfo.lr:type_name -> preapprovedloan.LoanRequest
	5, // 3: preapprovedloan.LoanRequestInfo.latest_lse:type_name -> preapprovedloan.LoanStepExecution
	6, // 4: preapprovedloan.InstallmentInfo.loan_installment_info:type_name -> preapprovedloan.LoanInstallmentInfo
	7, // 5: preapprovedloan.InstallmentInfo.loan_installment_payouts:type_name -> preapprovedloan.LoanInstallmentPayout
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_preapprovedloan_loan_info_proto_init() }
func file_api_preapprovedloan_loan_info_proto_init() {
	if File_api_preapprovedloan_loan_info_proto != nil {
		return
	}
	file_api_preapprovedloan_loan_account_proto_init()
	file_api_preapprovedloan_loan_installment_info_proto_init()
	file_api_preapprovedloan_loan_installment_payout_proto_init()
	file_api_preapprovedloan_loan_request_proto_init()
	file_api_preapprovedloan_loan_step_execution_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_preapprovedloan_loan_info_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_info_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanRequestInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_info_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstallmentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_preapprovedloan_loan_info_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_preapprovedloan_loan_info_proto_goTypes,
		DependencyIndexes: file_api_preapprovedloan_loan_info_proto_depIdxs,
		MessageInfos:      file_api_preapprovedloan_loan_info_proto_msgTypes,
	}.Build()
	File_api_preapprovedloan_loan_info_proto = out.File
	file_api_preapprovedloan_loan_info_proto_rawDesc = nil
	file_api_preapprovedloan_loan_info_proto_goTypes = nil
	file_api_preapprovedloan_loan_info_proto_depIdxs = nil
}
