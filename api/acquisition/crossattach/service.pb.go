// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/acquisition/crossattach/service.proto

package crossattach

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	product "github.com/epifi/gamma/api/product"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetCrossSellInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetCrossSellInfoRequest) Reset() {
	*x = GetCrossSellInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_acquisition_crossattach_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCrossSellInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCrossSellInfoRequest) ProtoMessage() {}

func (x *GetCrossSellInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_acquisition_crossattach_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCrossSellInfoRequest.ProtoReflect.Descriptor instead.
func (*GetCrossSellInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_acquisition_crossattach_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetCrossSellInfoRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetCrossSellInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                     *rpc.Status           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PrioritizedProductsToPitch []product.ProductType `protobuf:"varint,2,rep,packed,name=prioritized_products_to_pitch,json=prioritizedProductsToPitch,proto3,enum=product.ProductType" json:"prioritized_products_to_pitch,omitempty"`
	CanCrossSell               common.BooleanEnum    `protobuf:"varint,3,opt,name=can_cross_sell,json=canCrossSell,proto3,enum=api.typesv2.common.BooleanEnum" json:"can_cross_sell,omitempty"`
}

func (x *GetCrossSellInfoResponse) Reset() {
	*x = GetCrossSellInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_acquisition_crossattach_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCrossSellInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCrossSellInfoResponse) ProtoMessage() {}

func (x *GetCrossSellInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_acquisition_crossattach_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCrossSellInfoResponse.ProtoReflect.Descriptor instead.
func (*GetCrossSellInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_acquisition_crossattach_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetCrossSellInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCrossSellInfoResponse) GetPrioritizedProductsToPitch() []product.ProductType {
	if x != nil {
		return x.PrioritizedProductsToPitch
	}
	return nil
}

func (x *GetCrossSellInfoResponse) GetCanCrossSell() common.BooleanEnum {
	if x != nil {
		return x.CanCrossSell
	}
	return common.BooleanEnum(0)
}

var File_api_acquisition_crossattach_service_proto protoreflect.FileDescriptor

var file_api_acquisition_crossattach_service_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x34, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x72, 0x6f, 0x73,
	0x73, 0x53, 0x65, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xdf, 0x01, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x65, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x57, 0x0a,
	0x1d, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x70, 0x69, 0x74, 0x63, 0x68, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x2e, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x1a, 0x70, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x69, 0x7a, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x54,
	0x6f, 0x50, 0x69, 0x74, 0x63, 0x68, 0x12, 0x45, 0x0a, 0x0e, 0x63, 0x61, 0x6e, 0x5f, 0x63, 0x72,
	0x6f, 0x73, 0x73, 0x5f, 0x73, 0x65, 0x6c, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x0c, 0x63, 0x61, 0x6e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x65, 0x6c, 0x6c, 0x32, 0x88, 0x01,
	0x0a, 0x0b, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x12, 0x79, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x65, 0x6c, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x30, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x63, 0x72, 0x6f, 0x73, 0x73, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x72, 0x6f, 0x73, 0x73, 0x53, 0x65, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x65, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x68, 0x0a, 0x32, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x5a, 0x32,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x61, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_acquisition_crossattach_service_proto_rawDescOnce sync.Once
	file_api_acquisition_crossattach_service_proto_rawDescData = file_api_acquisition_crossattach_service_proto_rawDesc
)

func file_api_acquisition_crossattach_service_proto_rawDescGZIP() []byte {
	file_api_acquisition_crossattach_service_proto_rawDescOnce.Do(func() {
		file_api_acquisition_crossattach_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_acquisition_crossattach_service_proto_rawDescData)
	})
	return file_api_acquisition_crossattach_service_proto_rawDescData
}

var file_api_acquisition_crossattach_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_acquisition_crossattach_service_proto_goTypes = []interface{}{
	(*GetCrossSellInfoRequest)(nil),  // 0: acquisition.crossattach.GetCrossSellInfoRequest
	(*GetCrossSellInfoResponse)(nil), // 1: acquisition.crossattach.GetCrossSellInfoResponse
	(*rpc.Status)(nil),               // 2: rpc.Status
	(product.ProductType)(0),         // 3: product.ProductType
	(common.BooleanEnum)(0),          // 4: api.typesv2.common.BooleanEnum
}
var file_api_acquisition_crossattach_service_proto_depIdxs = []int32{
	2, // 0: acquisition.crossattach.GetCrossSellInfoResponse.status:type_name -> rpc.Status
	3, // 1: acquisition.crossattach.GetCrossSellInfoResponse.prioritized_products_to_pitch:type_name -> product.ProductType
	4, // 2: acquisition.crossattach.GetCrossSellInfoResponse.can_cross_sell:type_name -> api.typesv2.common.BooleanEnum
	0, // 3: acquisition.crossattach.CrossAttach.GetCrossSellInfo:input_type -> acquisition.crossattach.GetCrossSellInfoRequest
	1, // 4: acquisition.crossattach.CrossAttach.GetCrossSellInfo:output_type -> acquisition.crossattach.GetCrossSellInfoResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_acquisition_crossattach_service_proto_init() }
func file_api_acquisition_crossattach_service_proto_init() {
	if File_api_acquisition_crossattach_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_acquisition_crossattach_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCrossSellInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_acquisition_crossattach_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCrossSellInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_acquisition_crossattach_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_acquisition_crossattach_service_proto_goTypes,
		DependencyIndexes: file_api_acquisition_crossattach_service_proto_depIdxs,
		MessageInfos:      file_api_acquisition_crossattach_service_proto_msgTypes,
	}.Build()
	File_api_acquisition_crossattach_service_proto = out.File
	file_api_acquisition_crossattach_service_proto_rawDesc = nil
	file_api_acquisition_crossattach_service_proto_goTypes = nil
	file_api_acquisition_crossattach_service_proto_depIdxs = nil
}
