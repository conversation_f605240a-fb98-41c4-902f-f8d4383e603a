// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=CardNetworkType,CardState,CardRequestStageState,CardRequestStage,CardRequestType,CardRequestStatus,CardTrackingDeliveryState,CreditCardApplicantType,CardTrackingInfoFieldMask

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/firefly/v2/enums/enums.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CardNetworkType int32

const (
	CardNetworkType_CARD_NETWORK_TYPE_UNSPECIFIED CardNetworkType = 0
	CardNetworkType_CARD_NETWORK_TYPE_VISA        CardNetworkType = 1
	CardNetworkType_CARD_NETWORK_TYPE_RUPAY       CardNetworkType = 2
)

// Enum value maps for CardNetworkType.
var (
	CardNetworkType_name = map[int32]string{
		0: "CARD_NETWORK_TYPE_UNSPECIFIED",
		1: "CARD_NETWORK_TYPE_VISA",
		2: "CARD_NETWORK_TYPE_RUPAY",
	}
	CardNetworkType_value = map[string]int32{
		"CARD_NETWORK_TYPE_UNSPECIFIED": 0,
		"CARD_NETWORK_TYPE_VISA":        1,
		"CARD_NETWORK_TYPE_RUPAY":       2,
	}
)

func (x CardNetworkType) Enum() *CardNetworkType {
	p := new(CardNetworkType)
	*p = x
	return p
}

func (x CardNetworkType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardNetworkType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_v2_enums_enums_proto_enumTypes[0].Descriptor()
}

func (CardNetworkType) Type() protoreflect.EnumType {
	return &file_api_firefly_v2_enums_enums_proto_enumTypes[0]
}

func (x CardNetworkType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardNetworkType.Descriptor instead.
func (CardNetworkType) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_v2_enums_enums_proto_rawDescGZIP(), []int{0}
}

type CardState int32

const (
	CardState_CARD_STATE_UNSPECIFIED CardState = 0
	CardState_CARD_STATE_CREATED     CardState = 2
	CardState_CARD_STATE_CLOSED      CardState = 3
)

// Enum value maps for CardState.
var (
	CardState_name = map[int32]string{
		0: "CARD_STATE_UNSPECIFIED",
		2: "CARD_STATE_CREATED",
		3: "CARD_STATE_CLOSED",
	}
	CardState_value = map[string]int32{
		"CARD_STATE_UNSPECIFIED": 0,
		"CARD_STATE_CREATED":     2,
		"CARD_STATE_CLOSED":      3,
	}
)

func (x CardState) Enum() *CardState {
	p := new(CardState)
	*p = x
	return p
}

func (x CardState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_v2_enums_enums_proto_enumTypes[1].Descriptor()
}

func (CardState) Type() protoreflect.EnumType {
	return &file_api_firefly_v2_enums_enums_proto_enumTypes[1]
}

func (x CardState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardState.Descriptor instead.
func (CardState) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_v2_enums_enums_proto_rawDescGZIP(), []int{1}
}

type CardRequestStageState int32

const (
	CardRequestStageState_CARD_REQUEST_STAGE_STATE_UNSPECIFIED CardRequestStageState = 0
	// stage has been initiated
	CardRequestStageState_CARD_REQUEST_STAGE_STATE_INITIATED CardRequestStageState = 1
	// stage has been completed
	CardRequestStageState_CARD_REQUEST_STAGE_STATE_SUCCESS CardRequestStageState = 2
	// stage has been failed
	CardRequestStageState_CARD_REQUEST_STAGE_STATE_FAILED CardRequestStageState = 3
)

// Enum value maps for CardRequestStageState.
var (
	CardRequestStageState_name = map[int32]string{
		0: "CARD_REQUEST_STAGE_STATE_UNSPECIFIED",
		1: "CARD_REQUEST_STAGE_STATE_INITIATED",
		2: "CARD_REQUEST_STAGE_STATE_SUCCESS",
		3: "CARD_REQUEST_STAGE_STATE_FAILED",
	}
	CardRequestStageState_value = map[string]int32{
		"CARD_REQUEST_STAGE_STATE_UNSPECIFIED": 0,
		"CARD_REQUEST_STAGE_STATE_INITIATED":   1,
		"CARD_REQUEST_STAGE_STATE_SUCCESS":     2,
		"CARD_REQUEST_STAGE_STATE_FAILED":      3,
	}
)

func (x CardRequestStageState) Enum() *CardRequestStageState {
	p := new(CardRequestStageState)
	*p = x
	return p
}

func (x CardRequestStageState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardRequestStageState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_v2_enums_enums_proto_enumTypes[2].Descriptor()
}

func (CardRequestStageState) Type() protoreflect.EnumType {
	return &file_api_firefly_v2_enums_enums_proto_enumTypes[2]
}

func (x CardRequestStageState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardRequestStageState.Descriptor instead.
func (CardRequestStageState) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_v2_enums_enums_proto_rawDescGZIP(), []int{2}
}

type CardRequestStage int32

const (
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_UNSPECIFIED                CardRequestStage = 0
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_PAN_CHECK                  CardRequestStage = 1
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_PRE_ELIGIBILITY_CHECK      CardRequestStage = 2
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_EKYC                       CardRequestStage = 3
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_EKYC_VERIFICATION          CardRequestStage = 4
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_BRE                        CardRequestStage = 5
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_SELFIE_CHECK               CardRequestStage = 6
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_VKYC                       CardRequestStage = 7
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_VKYC_POLLING               CardRequestStage = 8
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_EMBOSS_NAME                CardRequestStage = 9
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_CIF_POLLING                CardRequestStage = 10
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_CUSTOMER_REGISTRATION      CardRequestStage = 11
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_SIM_BINDING                CardRequestStage = 12
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_SETUP_MPIN                 CardRequestStage = 13
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_PENNY_DROP                 CardRequestStage = 14
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_ADDITIONAL_REVIEW_REQUIRED CardRequestStage = 15
	CardRequestStage_CARD_REQUEST_STAGE_STAGE_REJECTED                   CardRequestStage = 16
	CardRequestStage_CARD_REQUEST_STAGE_ONBOARDING_INIT_AT_VENDOR        CardRequestStage = 17
	CardRequestStage_CARD_REQUEST_STAGE_SELECT_DELIVERY_ADDRESS          CardRequestStage = 18
	CardRequestStage_CARD_REQUEST_STAGE_TERMS_CONSENTS                   CardRequestStage = 19
	CardRequestStage_CARD_REQUEST_STAGE_CARD_CONSENTS                    CardRequestStage = 20
	CardRequestStage_CARD_REQUEST_STAGE_VERIFY_OTP                       CardRequestStage = 21
	CardRequestStage_CARD_REQUEST_STAGE_LIMIT_CHECK                      CardRequestStage = 22
	CardRequestStage_CARD_REQUEST_STAGE_ONBOARDING_COMPLETED             CardRequestStage = 23
	CardRequestStage_CARD_REQUEST_STAGE_CMS_READY                        CardRequestStage = 24
	CardRequestStage_CARD_REQUEST_STAGE_PAN_SUCCESS_CHECK                CardRequestStage = 25
	CardRequestStage_CARD_REQUEST_STAGE_LIMIT_RE_CHECK                   CardRequestStage = 26
	CardRequestStage_CARD_REQUEST_STAGE_CARD_ISSUED                      CardRequestStage = 27
	CardRequestStage_CARD_REQUEST_STAGE_PENNY_DROP_POLLING               CardRequestStage = 28
	CardRequestStage_CARD_REQUEST_STAGE_ADDITIONAL_DETAILS               CardRequestStage = 29
	CardRequestStage_CARD_REQUEST_STAGE_PERSONAL_DETAILS                 CardRequestStage = 30
	CardRequestStage_CARD_REQUEST_STAGE_PRE_APPROVED                     CardRequestStage = 31
	CardRequestStage_CARD_REQUEST_STAGE_VERIFY_CUSTOMER                  CardRequestStage = 32
	CardRequestStage_CARD_REQUEST_STAGE_CUSTOMER_DETAILS                 CardRequestStage = 33
	CardRequestStage_CARD_REQUEST_STAGE_OFFICE_ADDRESS                   CardRequestStage = 34
	CardRequestStage_CARD_REQUEST_STAGE_RE_KYC                           CardRequestStage = 35
	// When customer registration fails at bank due to some technical error we move user to this state and retry customer registration.
	// On UI user will see a retry CTA.
	CardRequestStage_CARD_REQUEST_STAGE_CUSTOMER_REGISTRATION_RETRY          CardRequestStage = 36
	CardRequestStage_CARD_REQUEST_STAGE_REGISTER_TRACKING_DETAILS_AT_SHIPWAY CardRequestStage = 37
	CardRequestStage_CARD_REQUEST_STAGE_POLL_DELIVERY_STATUS                 CardRequestStage = 38
	// User will be transitioned to this state after failing the penny drop verification and exceeding configured retry threshold at vendor.
	CardRequestStage_CARD_REQUEST_STAGE_PENNY_DROP_HOLD CardRequestStage = 39
)

// Enum value maps for CardRequestStage.
var (
	CardRequestStage_name = map[int32]string{
		0:  "CARD_REQUEST_STAGE_STAGE_UNSPECIFIED",
		1:  "CARD_REQUEST_STAGE_STAGE_PAN_CHECK",
		2:  "CARD_REQUEST_STAGE_STAGE_PRE_ELIGIBILITY_CHECK",
		3:  "CARD_REQUEST_STAGE_STAGE_EKYC",
		4:  "CARD_REQUEST_STAGE_STAGE_EKYC_VERIFICATION",
		5:  "CARD_REQUEST_STAGE_STAGE_BRE",
		6:  "CARD_REQUEST_STAGE_STAGE_SELFIE_CHECK",
		7:  "CARD_REQUEST_STAGE_STAGE_VKYC",
		8:  "CARD_REQUEST_STAGE_STAGE_VKYC_POLLING",
		9:  "CARD_REQUEST_STAGE_STAGE_EMBOSS_NAME",
		10: "CARD_REQUEST_STAGE_STAGE_CIF_POLLING",
		11: "CARD_REQUEST_STAGE_STAGE_CUSTOMER_REGISTRATION",
		12: "CARD_REQUEST_STAGE_STAGE_SIM_BINDING",
		13: "CARD_REQUEST_STAGE_STAGE_SETUP_MPIN",
		14: "CARD_REQUEST_STAGE_STAGE_PENNY_DROP",
		15: "CARD_REQUEST_STAGE_STAGE_ADDITIONAL_REVIEW_REQUIRED",
		16: "CARD_REQUEST_STAGE_STAGE_REJECTED",
		17: "CARD_REQUEST_STAGE_ONBOARDING_INIT_AT_VENDOR",
		18: "CARD_REQUEST_STAGE_SELECT_DELIVERY_ADDRESS",
		19: "CARD_REQUEST_STAGE_TERMS_CONSENTS",
		20: "CARD_REQUEST_STAGE_CARD_CONSENTS",
		21: "CARD_REQUEST_STAGE_VERIFY_OTP",
		22: "CARD_REQUEST_STAGE_LIMIT_CHECK",
		23: "CARD_REQUEST_STAGE_ONBOARDING_COMPLETED",
		24: "CARD_REQUEST_STAGE_CMS_READY",
		25: "CARD_REQUEST_STAGE_PAN_SUCCESS_CHECK",
		26: "CARD_REQUEST_STAGE_LIMIT_RE_CHECK",
		27: "CARD_REQUEST_STAGE_CARD_ISSUED",
		28: "CARD_REQUEST_STAGE_PENNY_DROP_POLLING",
		29: "CARD_REQUEST_STAGE_ADDITIONAL_DETAILS",
		30: "CARD_REQUEST_STAGE_PERSONAL_DETAILS",
		31: "CARD_REQUEST_STAGE_PRE_APPROVED",
		32: "CARD_REQUEST_STAGE_VERIFY_CUSTOMER",
		33: "CARD_REQUEST_STAGE_CUSTOMER_DETAILS",
		34: "CARD_REQUEST_STAGE_OFFICE_ADDRESS",
		35: "CARD_REQUEST_STAGE_RE_KYC",
		36: "CARD_REQUEST_STAGE_CUSTOMER_REGISTRATION_RETRY",
		37: "CARD_REQUEST_STAGE_REGISTER_TRACKING_DETAILS_AT_SHIPWAY",
		38: "CARD_REQUEST_STAGE_POLL_DELIVERY_STATUS",
		39: "CARD_REQUEST_STAGE_PENNY_DROP_HOLD",
	}
	CardRequestStage_value = map[string]int32{
		"CARD_REQUEST_STAGE_STAGE_UNSPECIFIED":                    0,
		"CARD_REQUEST_STAGE_STAGE_PAN_CHECK":                      1,
		"CARD_REQUEST_STAGE_STAGE_PRE_ELIGIBILITY_CHECK":          2,
		"CARD_REQUEST_STAGE_STAGE_EKYC":                           3,
		"CARD_REQUEST_STAGE_STAGE_EKYC_VERIFICATION":              4,
		"CARD_REQUEST_STAGE_STAGE_BRE":                            5,
		"CARD_REQUEST_STAGE_STAGE_SELFIE_CHECK":                   6,
		"CARD_REQUEST_STAGE_STAGE_VKYC":                           7,
		"CARD_REQUEST_STAGE_STAGE_VKYC_POLLING":                   8,
		"CARD_REQUEST_STAGE_STAGE_EMBOSS_NAME":                    9,
		"CARD_REQUEST_STAGE_STAGE_CIF_POLLING":                    10,
		"CARD_REQUEST_STAGE_STAGE_CUSTOMER_REGISTRATION":          11,
		"CARD_REQUEST_STAGE_STAGE_SIM_BINDING":                    12,
		"CARD_REQUEST_STAGE_STAGE_SETUP_MPIN":                     13,
		"CARD_REQUEST_STAGE_STAGE_PENNY_DROP":                     14,
		"CARD_REQUEST_STAGE_STAGE_ADDITIONAL_REVIEW_REQUIRED":     15,
		"CARD_REQUEST_STAGE_STAGE_REJECTED":                       16,
		"CARD_REQUEST_STAGE_ONBOARDING_INIT_AT_VENDOR":            17,
		"CARD_REQUEST_STAGE_SELECT_DELIVERY_ADDRESS":              18,
		"CARD_REQUEST_STAGE_TERMS_CONSENTS":                       19,
		"CARD_REQUEST_STAGE_CARD_CONSENTS":                        20,
		"CARD_REQUEST_STAGE_VERIFY_OTP":                           21,
		"CARD_REQUEST_STAGE_LIMIT_CHECK":                          22,
		"CARD_REQUEST_STAGE_ONBOARDING_COMPLETED":                 23,
		"CARD_REQUEST_STAGE_CMS_READY":                            24,
		"CARD_REQUEST_STAGE_PAN_SUCCESS_CHECK":                    25,
		"CARD_REQUEST_STAGE_LIMIT_RE_CHECK":                       26,
		"CARD_REQUEST_STAGE_CARD_ISSUED":                          27,
		"CARD_REQUEST_STAGE_PENNY_DROP_POLLING":                   28,
		"CARD_REQUEST_STAGE_ADDITIONAL_DETAILS":                   29,
		"CARD_REQUEST_STAGE_PERSONAL_DETAILS":                     30,
		"CARD_REQUEST_STAGE_PRE_APPROVED":                         31,
		"CARD_REQUEST_STAGE_VERIFY_CUSTOMER":                      32,
		"CARD_REQUEST_STAGE_CUSTOMER_DETAILS":                     33,
		"CARD_REQUEST_STAGE_OFFICE_ADDRESS":                       34,
		"CARD_REQUEST_STAGE_RE_KYC":                               35,
		"CARD_REQUEST_STAGE_CUSTOMER_REGISTRATION_RETRY":          36,
		"CARD_REQUEST_STAGE_REGISTER_TRACKING_DETAILS_AT_SHIPWAY": 37,
		"CARD_REQUEST_STAGE_POLL_DELIVERY_STATUS":                 38,
		"CARD_REQUEST_STAGE_PENNY_DROP_HOLD":                      39,
	}
)

func (x CardRequestStage) Enum() *CardRequestStage {
	p := new(CardRequestStage)
	*p = x
	return p
}

func (x CardRequestStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardRequestStage) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_v2_enums_enums_proto_enumTypes[3].Descriptor()
}

func (CardRequestStage) Type() protoreflect.EnumType {
	return &file_api_firefly_v2_enums_enums_proto_enumTypes[3]
}

func (x CardRequestStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardRequestStage.Descriptor instead.
func (CardRequestStage) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_v2_enums_enums_proto_rawDescGZIP(), []int{3}
}

type CardRequestType int32

const (
	CardRequestType_CARD_REQUEST_TYPE_UNSPECIFIED       CardRequestType = 0
	CardRequestType_CARD_REQUEST_TYPE_ONBOARDING        CardRequestType = 1
	CardRequestType_CARD_REQUEST_TYPE_DELIVERY_TRACKING CardRequestType = 2
)

// Enum value maps for CardRequestType.
var (
	CardRequestType_name = map[int32]string{
		0: "CARD_REQUEST_TYPE_UNSPECIFIED",
		1: "CARD_REQUEST_TYPE_ONBOARDING",
		2: "CARD_REQUEST_TYPE_DELIVERY_TRACKING",
	}
	CardRequestType_value = map[string]int32{
		"CARD_REQUEST_TYPE_UNSPECIFIED":       0,
		"CARD_REQUEST_TYPE_ONBOARDING":        1,
		"CARD_REQUEST_TYPE_DELIVERY_TRACKING": 2,
	}
)

func (x CardRequestType) Enum() *CardRequestType {
	p := new(CardRequestType)
	*p = x
	return p
}

func (x CardRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_v2_enums_enums_proto_enumTypes[4].Descriptor()
}

func (CardRequestType) Type() protoreflect.EnumType {
	return &file_api_firefly_v2_enums_enums_proto_enumTypes[4]
}

func (x CardRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardRequestType.Descriptor instead.
func (CardRequestType) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_v2_enums_enums_proto_rawDescGZIP(), []int{4}
}

type CardRequestStatus int32

const (
	CardRequestStatus_CARD_REQUEST_STATUS_UNSPECIFIED CardRequestStatus = 0
	CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS CardRequestStatus = 1
	CardRequestStatus_CARD_REQUEST_STATUS_FAILED      CardRequestStatus = 2
	CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS     CardRequestStatus = 3
)

// Enum value maps for CardRequestStatus.
var (
	CardRequestStatus_name = map[int32]string{
		0: "CARD_REQUEST_STATUS_UNSPECIFIED",
		1: "CARD_REQUEST_STATUS_IN_PROGRESS",
		2: "CARD_REQUEST_STATUS_FAILED",
		3: "CARD_REQUEST_STATUS_SUCCESS",
	}
	CardRequestStatus_value = map[string]int32{
		"CARD_REQUEST_STATUS_UNSPECIFIED": 0,
		"CARD_REQUEST_STATUS_IN_PROGRESS": 1,
		"CARD_REQUEST_STATUS_FAILED":      2,
		"CARD_REQUEST_STATUS_SUCCESS":     3,
	}
)

func (x CardRequestStatus) Enum() *CardRequestStatus {
	p := new(CardRequestStatus)
	*p = x
	return p
}

func (x CardRequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardRequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_v2_enums_enums_proto_enumTypes[5].Descriptor()
}

func (CardRequestStatus) Type() protoreflect.EnumType {
	return &file_api_firefly_v2_enums_enums_proto_enumTypes[5]
}

func (x CardRequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardRequestStatus.Descriptor instead.
func (CardRequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_v2_enums_enums_proto_rawDescGZIP(), []int{5}
}

type CreditCardFieldMask int32

const (
	CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_UNSPECIFIED      CreditCardFieldMask = 0
	CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID               CreditCardFieldMask = 1
	CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACTOR_ID         CreditCardFieldMask = 2
	CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_NETWORK_TYPE     CreditCardFieldMask = 3
	CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_EXTERNAL_USER_ID CreditCardFieldMask = 4
	CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE            CreditCardFieldMask = 5
	CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_VENDOR           CreditCardFieldMask = 6
	CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_PROGRAM     CreditCardFieldMask = 7
	CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CREATED_AT       CreditCardFieldMask = 8
	CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_UPDATED_AT       CreditCardFieldMask = 9
	CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_DELETED_AT       CreditCardFieldMask = 10
)

// Enum value maps for CreditCardFieldMask.
var (
	CreditCardFieldMask_name = map[int32]string{
		0:  "CREDIT_CARD_FIELD_MASK_UNSPECIFIED",
		1:  "CREDIT_CARD_FIELD_MASK_ID",
		2:  "CREDIT_CARD_FIELD_MASK_ACTOR_ID",
		3:  "CREDIT_CARD_FIELD_MASK_NETWORK_TYPE",
		4:  "CREDIT_CARD_FIELD_MASK_EXTERNAL_USER_ID",
		5:  "CREDIT_CARD_FIELD_MASK_STATE",
		6:  "CREDIT_CARD_FIELD_MASK_VENDOR",
		7:  "CREDIT_CARD_FIELD_MASK_CARD_PROGRAM",
		8:  "CREDIT_CARD_FIELD_MASK_CREATED_AT",
		9:  "CREDIT_CARD_FIELD_MASK_UPDATED_AT",
		10: "CREDIT_CARD_FIELD_MASK_DELETED_AT",
	}
	CreditCardFieldMask_value = map[string]int32{
		"CREDIT_CARD_FIELD_MASK_UNSPECIFIED":      0,
		"CREDIT_CARD_FIELD_MASK_ID":               1,
		"CREDIT_CARD_FIELD_MASK_ACTOR_ID":         2,
		"CREDIT_CARD_FIELD_MASK_NETWORK_TYPE":     3,
		"CREDIT_CARD_FIELD_MASK_EXTERNAL_USER_ID": 4,
		"CREDIT_CARD_FIELD_MASK_STATE":            5,
		"CREDIT_CARD_FIELD_MASK_VENDOR":           6,
		"CREDIT_CARD_FIELD_MASK_CARD_PROGRAM":     7,
		"CREDIT_CARD_FIELD_MASK_CREATED_AT":       8,
		"CREDIT_CARD_FIELD_MASK_UPDATED_AT":       9,
		"CREDIT_CARD_FIELD_MASK_DELETED_AT":       10,
	}
)

func (x CreditCardFieldMask) Enum() *CreditCardFieldMask {
	p := new(CreditCardFieldMask)
	*p = x
	return p
}

func (x CreditCardFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreditCardFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_v2_enums_enums_proto_enumTypes[6].Descriptor()
}

func (CreditCardFieldMask) Type() protoreflect.EnumType {
	return &file_api_firefly_v2_enums_enums_proto_enumTypes[6]
}

func (x CreditCardFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreditCardFieldMask.Descriptor instead.
func (CreditCardFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_v2_enums_enums_proto_rawDescGZIP(), []int{6}
}

type CardRequestFieldMask int32

const (
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_UNSPECIFIED      CardRequestFieldMask = 0
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID               CardRequestFieldMask = 1
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ACTOR_ID         CardRequestFieldMask = 2
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_TYPE             CardRequestFieldMask = 3
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS           CardRequestFieldMask = 4
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS  CardRequestFieldMask = 5
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STAGE_DETAILS    CardRequestFieldMask = 6
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_VENDOR           CardRequestFieldMask = 7
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_EXTERNAL_USER_ID CardRequestFieldMask = 8
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_CREATED_AT       CardRequestFieldMask = 9
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_UPDATED_AT       CardRequestFieldMask = 10
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_DELETED_AT       CardRequestFieldMask = 11
	CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_CLIENT_REQ_ID    CardRequestFieldMask = 12
)

// Enum value maps for CardRequestFieldMask.
var (
	CardRequestFieldMask_name = map[int32]string{
		0:  "CARD_REQUEST_FIELD_MASK_UNSPECIFIED",
		1:  "CARD_REQUEST_FIELD_MASK_ID",
		2:  "CARD_REQUEST_FIELD_MASK_ACTOR_ID",
		3:  "CARD_REQUEST_FIELD_MASK_TYPE",
		4:  "CARD_REQUEST_FIELD_MASK_STATUS",
		5:  "CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS",
		6:  "CARD_REQUEST_FIELD_MASK_STAGE_DETAILS",
		7:  "CARD_REQUEST_FIELD_MASK_VENDOR",
		8:  "CARD_REQUEST_FIELD_MASK_EXTERNAL_USER_ID",
		9:  "CARD_REQUEST_FIELD_MASK_CREATED_AT",
		10: "CARD_REQUEST_FIELD_MASK_UPDATED_AT",
		11: "CARD_REQUEST_FIELD_MASK_DELETED_AT",
		12: "CARD_REQUEST_FIELD_MASK_CLIENT_REQ_ID",
	}
	CardRequestFieldMask_value = map[string]int32{
		"CARD_REQUEST_FIELD_MASK_UNSPECIFIED":      0,
		"CARD_REQUEST_FIELD_MASK_ID":               1,
		"CARD_REQUEST_FIELD_MASK_ACTOR_ID":         2,
		"CARD_REQUEST_FIELD_MASK_TYPE":             3,
		"CARD_REQUEST_FIELD_MASK_STATUS":           4,
		"CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS":  5,
		"CARD_REQUEST_FIELD_MASK_STAGE_DETAILS":    6,
		"CARD_REQUEST_FIELD_MASK_VENDOR":           7,
		"CARD_REQUEST_FIELD_MASK_EXTERNAL_USER_ID": 8,
		"CARD_REQUEST_FIELD_MASK_CREATED_AT":       9,
		"CARD_REQUEST_FIELD_MASK_UPDATED_AT":       10,
		"CARD_REQUEST_FIELD_MASK_DELETED_AT":       11,
		"CARD_REQUEST_FIELD_MASK_CLIENT_REQ_ID":    12,
	}
)

func (x CardRequestFieldMask) Enum() *CardRequestFieldMask {
	p := new(CardRequestFieldMask)
	*p = x
	return p
}

func (x CardRequestFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardRequestFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_v2_enums_enums_proto_enumTypes[7].Descriptor()
}

func (CardRequestFieldMask) Type() protoreflect.EnumType {
	return &file_api_firefly_v2_enums_enums_proto_enumTypes[7]
}

func (x CardRequestFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardRequestFieldMask.Descriptor instead.
func (CardRequestFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_v2_enums_enums_proto_rawDescGZIP(), []int{7}
}

type CreditCardOfferFieldMask int32

const (
	CreditCardOfferFieldMask_CREDIT_CARD_OFFER_FIELD_MASK_UNSPECIFIED       CreditCardOfferFieldMask = 0
	CreditCardOfferFieldMask_CREDIT_CARD_OFFER_FIELD_MASK_ID                CreditCardOfferFieldMask = 1
	CreditCardOfferFieldMask_CREDIT_CARD_OFFER_FIELD_MASK_ACTOR_ID          CreditCardOfferFieldMask = 2
	CreditCardOfferFieldMask_CREDIT_CARD_OFFER_FIELD_MASK_VENDOR            CreditCardOfferFieldMask = 3
	CreditCardOfferFieldMask_CREDIT_CARD_OFFER_FIELD_MASK_OFFER_CONSTRAINTS CreditCardOfferFieldMask = 4
	CreditCardOfferFieldMask_CREDIT_CARD_OFFER_FIELD_MASK_VALID_SINCE       CreditCardOfferFieldMask = 5
	CreditCardOfferFieldMask_CREDIT_CARD_OFFER_FIELD_MASK_VALID_TILL        CreditCardOfferFieldMask = 6
	CreditCardOfferFieldMask_CREDIT_CARD_OFFER_FIELD_MASK_CARD_PROGRAM      CreditCardOfferFieldMask = 7
	CreditCardOfferFieldMask_CREDIT_CARD_OFFER_FIELD_MASK_CREATED_AT        CreditCardOfferFieldMask = 8
	CreditCardOfferFieldMask_CREDIT_CARD_OFFER_FIELD_MASK_UPDATED_AT        CreditCardOfferFieldMask = 9
	CreditCardOfferFieldMask_CREDIT_CARD_OFFER_FIELD_MASK_DELETED_AT        CreditCardOfferFieldMask = 10
)

// Enum value maps for CreditCardOfferFieldMask.
var (
	CreditCardOfferFieldMask_name = map[int32]string{
		0:  "CREDIT_CARD_OFFER_FIELD_MASK_UNSPECIFIED",
		1:  "CREDIT_CARD_OFFER_FIELD_MASK_ID",
		2:  "CREDIT_CARD_OFFER_FIELD_MASK_ACTOR_ID",
		3:  "CREDIT_CARD_OFFER_FIELD_MASK_VENDOR",
		4:  "CREDIT_CARD_OFFER_FIELD_MASK_OFFER_CONSTRAINTS",
		5:  "CREDIT_CARD_OFFER_FIELD_MASK_VALID_SINCE",
		6:  "CREDIT_CARD_OFFER_FIELD_MASK_VALID_TILL",
		7:  "CREDIT_CARD_OFFER_FIELD_MASK_CARD_PROGRAM",
		8:  "CREDIT_CARD_OFFER_FIELD_MASK_CREATED_AT",
		9:  "CREDIT_CARD_OFFER_FIELD_MASK_UPDATED_AT",
		10: "CREDIT_CARD_OFFER_FIELD_MASK_DELETED_AT",
	}
	CreditCardOfferFieldMask_value = map[string]int32{
		"CREDIT_CARD_OFFER_FIELD_MASK_UNSPECIFIED":       0,
		"CREDIT_CARD_OFFER_FIELD_MASK_ID":                1,
		"CREDIT_CARD_OFFER_FIELD_MASK_ACTOR_ID":          2,
		"CREDIT_CARD_OFFER_FIELD_MASK_VENDOR":            3,
		"CREDIT_CARD_OFFER_FIELD_MASK_OFFER_CONSTRAINTS": 4,
		"CREDIT_CARD_OFFER_FIELD_MASK_VALID_SINCE":       5,
		"CREDIT_CARD_OFFER_FIELD_MASK_VALID_TILL":        6,
		"CREDIT_CARD_OFFER_FIELD_MASK_CARD_PROGRAM":      7,
		"CREDIT_CARD_OFFER_FIELD_MASK_CREATED_AT":        8,
		"CREDIT_CARD_OFFER_FIELD_MASK_UPDATED_AT":        9,
		"CREDIT_CARD_OFFER_FIELD_MASK_DELETED_AT":        10,
	}
)

func (x CreditCardOfferFieldMask) Enum() *CreditCardOfferFieldMask {
	p := new(CreditCardOfferFieldMask)
	*p = x
	return p
}

func (x CreditCardOfferFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreditCardOfferFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_v2_enums_enums_proto_enumTypes[8].Descriptor()
}

func (CreditCardOfferFieldMask) Type() protoreflect.EnumType {
	return &file_api_firefly_v2_enums_enums_proto_enumTypes[8]
}

func (x CreditCardOfferFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreditCardOfferFieldMask.Descriptor instead.
func (CreditCardOfferFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_v2_enums_enums_proto_rawDescGZIP(), []int{8}
}

type CardTrackingDeliveryState int32

const (
	CardTrackingDeliveryState_CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED CardTrackingDeliveryState = 0
	CardTrackingDeliveryState_IN_TRANSIT                               CardTrackingDeliveryState = 1
	CardTrackingDeliveryState_SHIPPED                                  CardTrackingDeliveryState = 2
	CardTrackingDeliveryState_OUT_FOR_DELIVERY                         CardTrackingDeliveryState = 3
	CardTrackingDeliveryState_DELIVERED                                CardTrackingDeliveryState = 4
	CardTrackingDeliveryState_RETURNED_TO_ORIGIN                       CardTrackingDeliveryState = 5
)

// Enum value maps for CardTrackingDeliveryState.
var (
	CardTrackingDeliveryState_name = map[int32]string{
		0: "CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED",
		1: "IN_TRANSIT",
		2: "SHIPPED",
		3: "OUT_FOR_DELIVERY",
		4: "DELIVERED",
		5: "RETURNED_TO_ORIGIN",
	}
	CardTrackingDeliveryState_value = map[string]int32{
		"CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED": 0,
		"IN_TRANSIT":         1,
		"SHIPPED":            2,
		"OUT_FOR_DELIVERY":   3,
		"DELIVERED":          4,
		"RETURNED_TO_ORIGIN": 5,
	}
)

func (x CardTrackingDeliveryState) Enum() *CardTrackingDeliveryState {
	p := new(CardTrackingDeliveryState)
	*p = x
	return p
}

func (x CardTrackingDeliveryState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardTrackingDeliveryState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_v2_enums_enums_proto_enumTypes[9].Descriptor()
}

func (CardTrackingDeliveryState) Type() protoreflect.EnumType {
	return &file_api_firefly_v2_enums_enums_proto_enumTypes[9]
}

func (x CardTrackingDeliveryState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardTrackingDeliveryState.Descriptor instead.
func (CardTrackingDeliveryState) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_v2_enums_enums_proto_rawDescGZIP(), []int{9}
}

type CreditCardApplicantType int32

const (
	CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_UNSPECIFIED             CreditCardApplicantType = 0
	CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_ETB_ONBOARDING          CreditCardApplicantType = 1
	CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_NTB_ONBOARDING          CreditCardApplicantType = 2
	CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_PRE_APPROVED_ONBOARDING CreditCardApplicantType = 3
)

// Enum value maps for CreditCardApplicantType.
var (
	CreditCardApplicantType_name = map[int32]string{
		0: "CREDIT_CARD_APPLICANT_TYPE_UNSPECIFIED",
		1: "CREDIT_CARD_APPLICANT_TYPE_ETB_ONBOARDING",
		2: "CREDIT_CARD_APPLICANT_TYPE_NTB_ONBOARDING",
		3: "CREDIT_CARD_APPLICANT_TYPE_PRE_APPROVED_ONBOARDING",
	}
	CreditCardApplicantType_value = map[string]int32{
		"CREDIT_CARD_APPLICANT_TYPE_UNSPECIFIED":             0,
		"CREDIT_CARD_APPLICANT_TYPE_ETB_ONBOARDING":          1,
		"CREDIT_CARD_APPLICANT_TYPE_NTB_ONBOARDING":          2,
		"CREDIT_CARD_APPLICANT_TYPE_PRE_APPROVED_ONBOARDING": 3,
	}
)

func (x CreditCardApplicantType) Enum() *CreditCardApplicantType {
	p := new(CreditCardApplicantType)
	*p = x
	return p
}

func (x CreditCardApplicantType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreditCardApplicantType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_v2_enums_enums_proto_enumTypes[10].Descriptor()
}

func (CreditCardApplicantType) Type() protoreflect.EnumType {
	return &file_api_firefly_v2_enums_enums_proto_enumTypes[10]
}

func (x CreditCardApplicantType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreditCardApplicantType.Descriptor instead.
func (CreditCardApplicantType) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_v2_enums_enums_proto_rawDescGZIP(), []int{10}
}

type CardTrackingInfoFieldMask int32

const (
	CardTrackingInfoFieldMask_CARD_TRACKING_INFO_FIELD_MASK_UNSPECIFIED     CardTrackingInfoFieldMask = 0
	CardTrackingInfoFieldMask_CARD_TRACKING_INFO_FIELD_MASK_ID              CardTrackingInfoFieldMask = 1
	CardTrackingInfoFieldMask_CARD_TRACKING_INFO_FIELD_MASK_AWB             CardTrackingInfoFieldMask = 2
	CardTrackingInfoFieldMask_CARD_TRACKING_INFO_FIELD_MASK_PRINTING_VENDOR CardTrackingInfoFieldMask = 3
	CardTrackingInfoFieldMask_CARD_TRACKING_INFO_FIELD_MASK_ACTOR_ID        CardTrackingInfoFieldMask = 4
	CardTrackingInfoFieldMask_CARD_TRACKING_INFO_FIELD_MASK_CREATED_AT      CardTrackingInfoFieldMask = 5
	CardTrackingInfoFieldMask_CARD_TRACKING_INFO_FIELD_MASK_UPDATED_AT      CardTrackingInfoFieldMask = 6
	CardTrackingInfoFieldMask_CARD_TRACKING_INFO_FIELD_MASK_DELETED_AT      CardTrackingInfoFieldMask = 7
	CardTrackingInfoFieldMask_CARD_TRACKING_INFO_FIELD_MASK_CARRIER         CardTrackingInfoFieldMask = 8
)

// Enum value maps for CardTrackingInfoFieldMask.
var (
	CardTrackingInfoFieldMask_name = map[int32]string{
		0: "CARD_TRACKING_INFO_FIELD_MASK_UNSPECIFIED",
		1: "CARD_TRACKING_INFO_FIELD_MASK_ID",
		2: "CARD_TRACKING_INFO_FIELD_MASK_AWB",
		3: "CARD_TRACKING_INFO_FIELD_MASK_PRINTING_VENDOR",
		4: "CARD_TRACKING_INFO_FIELD_MASK_ACTOR_ID",
		5: "CARD_TRACKING_INFO_FIELD_MASK_CREATED_AT",
		6: "CARD_TRACKING_INFO_FIELD_MASK_UPDATED_AT",
		7: "CARD_TRACKING_INFO_FIELD_MASK_DELETED_AT",
		8: "CARD_TRACKING_INFO_FIELD_MASK_CARRIER",
	}
	CardTrackingInfoFieldMask_value = map[string]int32{
		"CARD_TRACKING_INFO_FIELD_MASK_UNSPECIFIED":     0,
		"CARD_TRACKING_INFO_FIELD_MASK_ID":              1,
		"CARD_TRACKING_INFO_FIELD_MASK_AWB":             2,
		"CARD_TRACKING_INFO_FIELD_MASK_PRINTING_VENDOR": 3,
		"CARD_TRACKING_INFO_FIELD_MASK_ACTOR_ID":        4,
		"CARD_TRACKING_INFO_FIELD_MASK_CREATED_AT":      5,
		"CARD_TRACKING_INFO_FIELD_MASK_UPDATED_AT":      6,
		"CARD_TRACKING_INFO_FIELD_MASK_DELETED_AT":      7,
		"CARD_TRACKING_INFO_FIELD_MASK_CARRIER":         8,
	}
)

func (x CardTrackingInfoFieldMask) Enum() *CardTrackingInfoFieldMask {
	p := new(CardTrackingInfoFieldMask)
	*p = x
	return p
}

func (x CardTrackingInfoFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardTrackingInfoFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_v2_enums_enums_proto_enumTypes[11].Descriptor()
}

func (CardTrackingInfoFieldMask) Type() protoreflect.EnumType {
	return &file_api_firefly_v2_enums_enums_proto_enumTypes[11]
}

func (x CardTrackingInfoFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardTrackingInfoFieldMask.Descriptor instead.
func (CardTrackingInfoFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_v2_enums_enums_proto_rawDescGZIP(), []int{11}
}

var File_api_firefly_v2_enums_enums_proto protoreflect.FileDescriptor

var file_api_firefly_v2_enums_enums_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x76, 0x32,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x14, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e,
	0x76, 0x32, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a, 0x6d, 0x0a, 0x0f, 0x43, 0x61, 0x72, 0x64,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a,
	0x0a, 0x16, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x56, 0x49, 0x53, 0x41, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x52, 0x55, 0x50, 0x41, 0x59, 0x10, 0x02, 0x2a, 0x56, 0x0a, 0x09, 0x43, 0x61, 0x72, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x16, 0x0a, 0x12, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x03, 0x2a,
	0xb4, 0x01, 0x0a, 0x15, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10,
	0x02, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x2a, 0xa5, 0x0d, 0x0a, 0x10, 0x43, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x24, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x32, 0x0a,
	0x2e, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x45, 0x4c,
	0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10,
	0x02, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x45, 0x4b,
	0x59, 0x43, 0x10, 0x03, 0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x42, 0x52, 0x45, 0x10, 0x05, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x49, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10,
	0x06, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x56, 0x4b,
	0x59, 0x43, 0x10, 0x07, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x08, 0x12,
	0x28, 0x0a, 0x24, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x45, 0x4d, 0x42, 0x4f,
	0x53, 0x53, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x09, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x49, 0x46, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x49, 0x4e,
	0x47, 0x10, 0x0a, 0x12, 0x32, 0x0a, 0x2e, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0b, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x53, 0x49, 0x4d, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x0c, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45,
	0x54, 0x55, 0x50, 0x5f, 0x4d, 0x50, 0x49, 0x4e, 0x10, 0x0d, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x45, 0x4e, 0x4e, 0x59, 0x5f, 0x44, 0x52, 0x4f,
	0x50, 0x10, 0x0e, 0x12, 0x37, 0x0a, 0x33, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x0f, 0x12, 0x25, 0x0a, 0x21,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x10, 0x10, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e,
	0x44, 0x4f, 0x52, 0x10, 0x11, 0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45,
	0x43, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x41, 0x44, 0x44, 0x52,
	0x45, 0x53, 0x53, 0x10, 0x12, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x45, 0x52, 0x4d,
	0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x13, 0x12, 0x24, 0x0a, 0x20,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x53,
	0x10, 0x14, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f,
	0x4f, 0x54, 0x50, 0x10, 0x15, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4c, 0x49, 0x4d, 0x49,
	0x54, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x16, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x45, 0x54, 0x45, 0x44, 0x10, 0x17, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x4d, 0x53,
	0x5f, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x18, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x50,
	0x41, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b,
	0x10, 0x19, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x52,
	0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x1a, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x44, 0x10, 0x1b, 0x12, 0x29, 0x0a,
	0x25, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x50, 0x45, 0x4e, 0x4e, 0x59, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x50,
	0x4f, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x1c, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x41,
	0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x10, 0x1d, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e,
	0x41, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x1e, 0x12, 0x23, 0x0a, 0x1f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10,
	0x1f, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x20, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x10, 0x21, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x49, 0x43, 0x45, 0x5f,
	0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0x22, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x52, 0x45, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x23, 0x12, 0x32, 0x0a, 0x2e, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x10, 0x24, 0x12, 0x3b, 0x0a, 0x37,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x43,
	0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x41, 0x54, 0x5f,
	0x53, 0x48, 0x49, 0x50, 0x57, 0x41, 0x59, 0x10, 0x25, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x50, 0x4f, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0x26, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x45, 0x4e,
	0x4e, 0x59, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x10, 0x27, 0x2a, 0x7f,
	0x0a, 0x0f, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x4c, 0x49,
	0x56, 0x45, 0x52, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x2a,
	0x9e, 0x01, 0x0a, 0x11, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12,
	0x1e, 0x0a, 0x1a, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12,
	0x1f, 0x0a, 0x1b, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03,
	0x2a, 0xba, 0x03, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1d, 0x0a, 0x19, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12,
	0x23, 0x0a, 0x1f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f,
	0x49, 0x44, 0x10, 0x02, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4e,
	0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x03, 0x12, 0x2b, 0x0a,
	0x27, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x05, 0x12, 0x21, 0x0a, 0x1d,
	0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x06, 0x12,
	0x27, 0x0a, 0x23, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x10, 0x07, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x08, 0x12,
	0x25, 0x0a, 0x21, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x09, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0a, 0x2a, 0x98, 0x04,
	0x0a, 0x14, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x1e, 0x0a, 0x1a, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12,
	0x24, 0x0a, 0x20, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52,
	0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x04, 0x12, 0x2b, 0x0a, 0x27, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x05, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x10, 0x06, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56,
	0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x07, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x49, 0x44, 0x10, 0x08, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x09, 0x12, 0x26, 0x0a,
	0x22, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44,
	0x5f, 0x41, 0x54, 0x10, 0x0a, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0b, 0x12, 0x29, 0x0a,
	0x25, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f,
	0x52, 0x45, 0x51, 0x5f, 0x49, 0x44, 0x10, 0x0c, 0x2a, 0x86, 0x04, 0x0a, 0x18, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49,
	0x44, 0x10, 0x02, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x03, 0x12, 0x32, 0x0a, 0x2e,
	0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45,
	0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x54, 0x53, 0x10, 0x04,
	0x12, 0x2c, 0x0a, 0x28, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x49, 0x4e, 0x43, 0x45, 0x10, 0x05, 0x12, 0x2b,
	0x0a, 0x27, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x49, 0x4c, 0x4c, 0x10, 0x06, 0x12, 0x2d, 0x0a, 0x29, 0x43,
	0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x10, 0x07, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x08, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f,
	0x41, 0x54, 0x10, 0x09, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10,
	0x0a, 0x2a, 0xa3, 0x01, 0x0a, 0x19, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x2c, 0x0a, 0x28, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47,
	0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a,
	0x0a, 0x49, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x54, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x53, 0x48, 0x49, 0x50, 0x50, 0x45, 0x44, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x4f, 0x55,
	0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x10, 0x03,
	0x12, 0x0d, 0x0a, 0x09, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x04, 0x12,
	0x16, 0x0a, 0x12, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x4f,
	0x52, 0x49, 0x47, 0x49, 0x4e, 0x10, 0x05, 0x2a, 0xdb, 0x01, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x2d, 0x0a, 0x29, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41,
	0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x54,
	0x42, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x2d,
	0x0a, 0x29, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x50,
	0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x54, 0x42,
	0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x36, 0x0a,
	0x32, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x50, 0x50,
	0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x5f,
	0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x03, 0x2a, 0xab, 0x03, 0x0a, 0x19, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d,
	0x61, 0x73, 0x6b, 0x12, 0x2d, 0x0a, 0x29, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43,
	0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b,
	0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x57, 0x42, 0x10, 0x02, 0x12,
	0x31, 0x0a, 0x2d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47,
	0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x50, 0x52, 0x49, 0x4e, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52,
	0x10, 0x03, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b,
	0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x2c,
	0x0a, 0x28, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x05, 0x12, 0x2c, 0x0a, 0x28,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e,
	0x46, 0x4f, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x06, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x07, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x52, 0x52, 0x49, 0x45,
	0x52, 0x10, 0x08, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x76, 0x32, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x76, 0x32, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_firefly_v2_enums_enums_proto_rawDescOnce sync.Once
	file_api_firefly_v2_enums_enums_proto_rawDescData = file_api_firefly_v2_enums_enums_proto_rawDesc
)

func file_api_firefly_v2_enums_enums_proto_rawDescGZIP() []byte {
	file_api_firefly_v2_enums_enums_proto_rawDescOnce.Do(func() {
		file_api_firefly_v2_enums_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_firefly_v2_enums_enums_proto_rawDescData)
	})
	return file_api_firefly_v2_enums_enums_proto_rawDescData
}

var file_api_firefly_v2_enums_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 12)
var file_api_firefly_v2_enums_enums_proto_goTypes = []interface{}{
	(CardNetworkType)(0),           // 0: api.firefly.v2.enums.CardNetworkType
	(CardState)(0),                 // 1: api.firefly.v2.enums.CardState
	(CardRequestStageState)(0),     // 2: api.firefly.v2.enums.CardRequestStageState
	(CardRequestStage)(0),          // 3: api.firefly.v2.enums.CardRequestStage
	(CardRequestType)(0),           // 4: api.firefly.v2.enums.CardRequestType
	(CardRequestStatus)(0),         // 5: api.firefly.v2.enums.CardRequestStatus
	(CreditCardFieldMask)(0),       // 6: api.firefly.v2.enums.CreditCardFieldMask
	(CardRequestFieldMask)(0),      // 7: api.firefly.v2.enums.CardRequestFieldMask
	(CreditCardOfferFieldMask)(0),  // 8: api.firefly.v2.enums.CreditCardOfferFieldMask
	(CardTrackingDeliveryState)(0), // 9: api.firefly.v2.enums.CardTrackingDeliveryState
	(CreditCardApplicantType)(0),   // 10: api.firefly.v2.enums.CreditCardApplicantType
	(CardTrackingInfoFieldMask)(0), // 11: api.firefly.v2.enums.CardTrackingInfoFieldMask
}
var file_api_firefly_v2_enums_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_firefly_v2_enums_enums_proto_init() }
func file_api_firefly_v2_enums_enums_proto_init() {
	if File_api_firefly_v2_enums_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_firefly_v2_enums_enums_proto_rawDesc,
			NumEnums:      12,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_firefly_v2_enums_enums_proto_goTypes,
		DependencyIndexes: file_api_firefly_v2_enums_enums_proto_depIdxs,
		EnumInfos:         file_api_firefly_v2_enums_enums_proto_enumTypes,
	}.Build()
	File_api_firefly_v2_enums_enums_proto = out.File
	file_api_firefly_v2_enums_enums_proto_rawDesc = nil
	file_api_firefly_v2_enums_enums_proto_goTypes = nil
	file_api_firefly_v2_enums_enums_proto_depIdxs = nil
}
