// nolint: depguard,ineffassign
package worker

import (
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/pkg/storage/v2/usecase"

	"context"
	"fmt"
	"time"

	"github.com/rudderlabs/analytics-go"
	"go.uber.org/fx"
	gormV2 "gorm.io/gorm"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/aws/v2/sns"
	"github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	commonfx "github.com/epifi/be-common/pkg/epifitemporal/fx"

	celestialPb "github.com/epifi/be-common/api/celestial"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	authPb "github.com/epifi/gamma/api/auth/orchestrator"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	brePb "github.com/epifi/gamma/api/bre"
	cardPb "github.com/epifi/gamma/api/card/provisioning"
	commPb "github.com/epifi/gamma/api/comms"
	consentpb "github.com/epifi/gamma/api/consent"
	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	depositPb "github.com/epifi/gamma/api/deposit"
	docsPb "github.com/epifi/gamma/api/docs"
	employmentPb "github.com/epifi/gamma/api/employment"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	billingPb "github.com/epifi/gamma/api/firefly/billing"
	ffLmsPb "github.com/epifi/gamma/api/firefly/lms"
	ffPinotPb "github.com/epifi/gamma/api/firefly/pinot"
	kycPb "github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	nudgePb "github.com/epifi/gamma/api/nudge"
	orderPb "github.com/epifi/gamma/api/order"
	panPb "github.com/epifi/gamma/api/pan"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	productpb "github.com/epifi/gamma/api/product"
	questManagerPb "github.com/epifi/gamma/api/quest/manager"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	riskpb "github.com/epifi/gamma/api/risk"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	savingsPb "github.com/epifi/gamma/api/savings"
	segmentpb "github.com/epifi/gamma/api/segment"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	userPb "github.com/epifi/gamma/api/user"
	creditReportPb "github.com/epifi/gamma/api/user/credit_report"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/obfuscator"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/fennel"
	breVg "github.com/epifi/gamma/api/vendorgateway/lending/bre"
	ccVgPb "github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditline"
	merchantResolutionPb "github.com/epifi/gamma/api/vendorgateway/merchantresolution"
	panVgPb "github.com/epifi/gamma/api/vendorgateway/pan"
	profileValidationPb "github.com/epifi/gamma/api/vendorgateway/profilevalidation"
	vgScienapticPb "github.com/epifi/gamma/api/vendorgateway/scienaptic"
	vgShipwayPb "github.com/epifi/gamma/api/vendorgateway/shipway"
	ffAccWire "github.com/epifi/gamma/firefly/accounting/wire"
	fireflyAccWorkflow "github.com/epifi/gamma/firefly/accounting/workflow"
	fireflyworkerconfig "github.com/epifi/gamma/firefly/config/worker"
	fireflyworkergenconfig "github.com/epifi/gamma/firefly/config/worker/genconf"
	ffInterceptor "github.com/epifi/gamma/firefly/interceptor"
	wire2 "github.com/epifi/gamma/firefly/v2/wire"
	fireflyV2Workflow "github.com/epifi/gamma/firefly/v2/workflow"
	"github.com/epifi/gamma/firefly/wire"
	fireflyWorkflow "github.com/epifi/gamma/firefly/workflow"
	templatedWf "github.com/epifi/gamma/firefly/workflow/templated"
	fireflyWorkflowV2 "github.com/epifi/gamma/firefly/workflow/v2"
	v3 "github.com/epifi/gamma/firefly/workflow/v3"
	epifitemporalfx "github.com/epifi/gamma/pkg/epifitemporal/fx"
	palWorkerConf "github.com/epifi/gamma/preapprovedloan/config/worker"
	questSDKInit "github.com/epifi/gamma/quest/sdk/init"
)

const (
	httpPrefix = "http://"
)

func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	app := fx.New(
		fx.Supply(initNotifier),
		fx.Supply(cfg.FIREFLY_WORKER_SERVER),
		fx.Supply(cfg.FIREFLY_WORKER_SERVICE),
		fx.Supply(fireflyworkerconfig.Load),
		fx.Supply(fireflyworkergenconfig.NewConfig),
		fx.Provide(
			domainWorkflowProvider,
			domainActivitiesProvider,
		),
		commonfx.GetConfigProviderOption[*fireflyworkergenconfig.Config, *fireflyworkerconfig.Config](),
		epifitemporalfx.FxWorkerModule,
	)

	app.Run()
	return nil
}

type domainWorkflowProviderResult struct {
	fx.Out

	DomainWorkflows []any `name:"DomainWorkflows"`
}

func domainWorkflowProvider() domainWorkflowProviderResult {
	return domainWorkflowProviderResult{
		DomainWorkflows: []any{
			fireflyWorkflow.FreezeUnfreezeCard,
			v3.PerformCardOnboardingV1,
			fireflyWorkflow.ProcessReissueCard,
			fireflyWorkflow.SetCardLimits,
			fireflyWorkflow.SetCardUsage,
			fireflyWorkflow.ProcessViewCardDetails,
			fireflyWorkflow.IssuePhysicalCreditCard,
			fireflyWorkflow.ActivateCreditCard,
			fireflyWorkflow.ProcessDispute,
			fireflyWorkflowV2.PerformBillGenerationV1,
			fireflyWorkflow.ResetCardPin,
			fireflyWorkflow.PerformCreditCardPayment,
			fireflyAccWorkflow.ProcessCardTransaction,
			fireflyWorkflow.ExportCreditCardStatement,
			fireflyAccWorkflow.ReconcileCreditAccountAndTxn,
			fireflyAccWorkflow.TriggerReconcileWorkflows,
			fireflyWorkflow.ProcessWelcomeOffer,
			fireflyWorkflow.RealtimeCardEligibilityCheck,
			fireflyWorkflow.PerformUserCommunication,
			fireflyWorkflow.PerformBiometricRevalidation,
			templatedWf.PerformCardOnboardingV2,
			fireflyWorkflow.RealTimeProfileValidationCheck,
			fireflyWorkflow.FireflySyncProxy,
			templatedWf.ProcessAutoRepayment,
			templatedWf.RealtimeCardEligibilityCheckV2,
			templatedWf.PerformCreditCardPaymentFromTpap,
			fireflyV2Workflow.ProcessCreditCardsDispatchedFile,
			fireflyV2Workflow.PerformCreditCardDeliveryTracking,
		},
	}
}

type domainActivitiesProviderInput struct {
	fx.In

	Lc                       fx.Lifecycle
	Conf                     *fireflyworkerconfig.Config               `name:"Conf"`
	GenConf                  *fireflyworkergenconfig.Config            `name:"GenConf"`
	DbConnProvider           *storageV2.DBResourceProvider[*gormV2.DB] `name:"DbConnProvider"`
	AwsConfig                aws.Config                                `name:"AwsConfig"`
	DbConnProviderForUseCase *usecase.DBResourceProvider[*gormV2.DB]   `name:"DbConnProviderForUseCase"`
}

type domainActivitiesProviderResult struct {
	fx.Out

	DomainActivityProcessors []any `name:"DomainActivityProcessors"`
}

// nolint: funlen
func domainActivitiesProvider(p domainActivitiesProviderInput) (domainActivitiesProviderResult, error) {
	db, err := p.DbConnProvider.GetResourceForOwnership(commontypes.Ownership_EPIFI_TECH)
	if err != nil {
		return domainActivitiesProviderResult{}, fmt.Errorf("failed to get epifi db conn: %w", err)
	}
	storageV2.InitDefaultCRDBTransactionExecutor(db)

	ccDb, err := storageV2.NewCRDBWithConfig(p.Conf.CreditCardDb, false)
	if err != nil {
		return domainActivitiesProviderResult{}, fmt.Errorf("failed to create db connection: %w", err)
	}

	ccFederalPgdb, err := p.DbConnProviderForUseCase.GetResource(commontypes.Ownership_EPIFI_TECH, commontypes.UseCase_USE_CASE_CREDIT_CARD)
	if err != nil {
		return domainActivitiesProviderResult{}, fmt.Errorf("failed to create credit card federal db connection: %w", err)
	}

	var ccDbTxnExecutor storageV2.IdempotentTxnExecutor

	ccDbTxnExecutor = storageV2.NewCRDBIdempotentTxnExecutor(ccDb)
	if p.Conf.UsePgdbConnForCcDb {
		logger.InfoNoCtx("using pgdb connection for ff worker")
		ccDb, err = storageV2.NewPostgresDBWithConfig(p.Conf.CreditCardPgDb, true)
		if err != nil {
			return domainActivitiesProviderResult{}, fmt.Errorf("failed to create pgdb connection: %w", err)
		}
		ccDbTxnExecutor = storageV2.NewGormTxnExecutor(ccDb)
	}
	ctx := context.Background()
	ccTransactionEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, p.Conf.CCTransactionEventPublisher, p.AwsConfig,
		queue.NewDefaultMessage())
	if err != nil {
		return domainActivitiesProviderResult{}, fmt.Errorf("failed to initialize cc transaction event publisher: %w", err)
	}

	ccBillGenerationEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, p.Conf.CCBillGenerationEventPublisher, p.AwsConfig,
		queue.NewDefaultMessage())
	if err != nil {
		return domainActivitiesProviderResult{}, fmt.Errorf("failed to initialize cc bill generation event publisher: %w", err)
	}

	ccStageUpdateEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, p.Conf.CCStageUpdateEventPublisher, p.AwsConfig, queue.NewDefaultMessage())
	if err != nil {
		return domainActivitiesProviderResult{}, fmt.Errorf("failed to initialize cc stage update event publisher: %w", err)
	}

	sqsClient := sqs.InitSQSClient(p.AwsConfig)
	ccTransactionNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, p.GenConf.CCTransactionNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		return domainActivitiesProviderResult{}, fmt.Errorf("failed to initialize cc transaction notification publisher: %w", err)
	}

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		p.Conf.Secrets.Ids[palWorkerConf.RudderWriteKey], httpPrefix+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  p.Conf.RudderStack.IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: p.Conf.RudderStack.BatchSize,
			Verbose:   p.Conf.RudderStack.Verbose,
		})
	if err != nil {
		return domainActivitiesProviderResult{}, fmt.Errorf("failed to initialize rudder client: %w", err)
	}
	broker := events.NewRudderStackBroker(rudderClient)

	userConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	usersClient := userPb.NewUsersClient(userConn)
	userGroupClient := userGroupPb.NewGroupClient(userConn)
	creditReportClient := creditReportPb.NewCreditReportManagerClient(userConn)
	onboardingClient := onbPb.NewOnboardingClient(userConn)

	panConn := epifigrpc.NewConnByService(cfg.PAN_SERVICE)
	panClient := panPb.NewPanClient(panConn)

	breConn := epifigrpc.NewConnByService(cfg.BRE_SERVICE)
	breClient := brePb.NewBreClient(breConn)

	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	actorClient := actorPb.NewActorClient(actorConn)

	authConn := epifigrpc.NewConnByService(cfg.AUTH_SERVICE)
	authClient := authPb.NewOrchestratorClient(authConn)
	authBeClient := auth.NewAuthClient(authConn)

	dcConn := epifigrpc.NewConnByService(cfg.CARD_SERVICE)
	dcClient := cardPb.NewCardProvisioningClient(dcConn)

	localhostConn := epifigrpc.NewConnByService(cfg.FIREFLY_SERVICE)
	ffAccountingClient := ffAccPb.NewAccountingClient(localhostConn)
	ffClient := ffPb.NewFireflyClient(localhostConn)
	pinotClient := ffPinotPb.NewTxnAggregatesClient(localhostConn)
	fireflyLmsClient := ffLmsPb.NewLoanManagementSystemClient(localhostConn)

	creditLimitEstimatorConn := epifigrpc.NewConnByService(cfg.LIMIT_ESTIMATOR_SERVICE)
	creditLimitEstimatorClient := limitEstimatorPb.NewCreditLimitEstimatorClient(creditLimitEstimatorConn)

	kycConn := epifigrpc.NewConnByService(cfg.KYC_SERVICE)
	vkycClient := vkycPb.NewVKYCClient(kycConn)
	kycClient := kycPb.NewKycClient(kycConn)

	empConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	empClient := employmentPb.NewEmploymentClient(empConn)
	empFeClient := employmentPb.NewEmploymentFeClient(empConn)

	riskConn := epifigrpc.NewConnByService(cfg.RISK_SERVICE)
	riskClient := riskpb.NewRiskClient(riskConn)
	consentClient := consentpb.NewConsentClient(userConn)

	piConn := epifigrpc.NewConnByService(cfg.PAYMENT_INSTRUMENT_SERVICE)
	piClient := piPb.NewPiClient(piConn)

	billingConn := epifigrpc.NewConnByService(cfg.FIREFLY_SERVICE)
	billingClient := billingPb.NewBillingClient(billingConn)
	commsConn := epifigrpc.NewConnByService(cfg.COMMS_SERVICE)
	commsClient := commPb.NewCommsClient(commsConn)

	docsConn := epifigrpc.NewConnByService(cfg.DOCS_SERVICE)
	docsClient := docsPb.NewDocsClient(docsConn)

	celestialConn := epifigrpc.NewConnByService(cfg.CELESTIAL_SERVICE)
	celestialClient := celestialPb.NewCelestialClient(celestialConn)

	bankCustConn := epifigrpc.NewConnByService(cfg.BANK_CUSTOMER_SERVICE)
	bankCustClient := bankCustPb.NewBankCustomerServiceClient(bankCustConn)

	orderConn := epifigrpc.NewConnByService(cfg.ORDER_SERVICE)
	orderClient := orderPb.NewOrderServiceClient(orderConn)

	payConn := epifigrpc.NewConnByService(cfg.PAY_SERVICE)
	payClient := payPb.NewPayClient(payConn)

	rewardsConn := epifigrpc.NewConnByService(cfg.REWARD_SERVICE)
	rewardsClient := rewardsPb.NewRewardsGeneratorClient(rewardsConn)
	rewardsProjectionClient := rewardsProjectionPb.NewProjectorServiceClient(rewardsConn)

	creditReportConn := epifigrpc.NewConnByService(cfg.CREDIT_REPORT_SERVICE)
	creditReportV2Client := creditReportV2Pb.NewCreditReportManagerClient(creditReportConn)

	nudgeConn := epifigrpc.NewConnByService(cfg.NUDGE_SERVICE)
	nudgeClient := nudgePb.NewNudgeServiceClient(nudgeConn)

	depositConn := epifigrpc.NewConnByService(cfg.DEPOSIT_SERVICE)
	depositClient := depositPb.NewDepositClient(depositConn)
	obfuscatorClient := obfuscator.NewObfuscatorClient(userConn)

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE, ffInterceptor.NewVgTenantClientInterceptor(ffAccountingClient, ffClient))
	ccVgClient := ccVgPb.NewCreditCardClient(vgConn)
	vgShipwayClient := vgShipwayPb.NewShipwayClient(vgConn)
	vgProfileValidationClient := profileValidationPb.NewProfileValidationClient(vgConn)
	merchantResolutionVgClient := merchantResolutionPb.NewMerchantResolutionClient(vgConn)
	creditLineVgClient := creditline.NewCreditLineClient(vgConn)
	breVgClient := breVg.NewBusinessRuleEngineClient(vgConn)
	fennelVgClient := fennel.NewFennelFeatureStoreClient(vgConn)
	scienapticVgClient := vgScienapticPb.NewScienapticClient(vgConn)

	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	savingsClient := savingsPb.NewSavingsClient(savingsConn)
	creditCardDocsS3Client := s3.NewClient(p.AwsConfig, p.Conf.StatementPdfConfig.StatementDocumentsBucketName)

	questConn := epifigrpc.NewConnByService(cfg.QUEST_SERVICE)
	questManagerClient := questManagerPb.NewManagerClient(questConn)
	segmentConn := epifigrpc.NewConnByService(cfg.SEGMENT_SERVICE)
	segmentationClient := segmentpb.NewSegmentationServiceClient(segmentConn)
	profileClient := profilePb.NewProfileClient(riskConn)

	productConn := epifigrpc.NewConnByService(cfg.PRODUCT_SERVICE)
	productClient := productpb.NewProductClient(productConn)

	panVgClient := panVgPb.NewPANClient(vgConn)
	acPiClient := accPiPb.NewAccountPIRelationClient(piConn)

	// Register activities
	if !p.GenConf.QuestSdk().Disable() {
		questRedisCl := storage.NewRedisClientFromConfig(p.GenConf.QuestRedisOptions(), true)
		questCacheStorage := cache.NewRedisCacheStorage(questRedisCl)
		cacheStorage := cache.NewRedisCacheStorageWithHystrix(questCacheStorage, p.GenConf.QuestRedisOptions().HystrixCommand)
		questSDKInit.InitQuestSDK(p.GenConf.QuestSdk(), p.GenConf, questManagerClient, userGroupClient, usersClient,
			actorClient, segmentationClient, cacheStorage, broker)
	}

	// init redis client
	redisCacheClient := storage.NewRedisClientFromConfig(p.Conf.FireflyRedisStore, true)

	p.Lc.Append(fx.Hook{
		OnStop: func(context.Context) error {
			func() {
				sqlDB, _ := ccDb.DB()
				_ = sqlDB.Close()
			}()
			func() { _ = redisCacheClient.Close() }()
			epifigrpc.CloseConn(userConn)
			epifigrpc.CloseConn(panConn)
			epifigrpc.CloseConn(panConn)
			epifigrpc.CloseConn(actorConn)
			epifigrpc.CloseConn(authConn)
			epifigrpc.CloseConn(dcConn)
			epifigrpc.CloseConn(localhostConn)
			epifigrpc.CloseConn(kycConn)
			epifigrpc.CloseConn(empConn)
			epifigrpc.CloseConn(riskConn)
			epifigrpc.CloseConn(piConn)
			epifigrpc.CloseConn(billingConn)
			epifigrpc.CloseConn(commsConn)
			epifigrpc.CloseConn(docsConn)
			epifigrpc.CloseConn(celestialConn)
			epifigrpc.CloseConn(bankCustConn)
			epifigrpc.CloseConn(orderConn)
			epifigrpc.CloseConn(payConn)
			epifigrpc.CloseConn(rewardsConn)
			epifigrpc.CloseConn(creditReportConn)
			epifigrpc.CloseConn(nudgeConn)
			epifigrpc.CloseConn(depositConn)
			epifigrpc.CloseConn(vgConn)
			epifigrpc.CloseConn(savingsConn)
			epifigrpc.CloseConn(questConn)
			epifigrpc.CloseConn(creditLimitEstimatorConn)
			epifigrpc.CloseConn(segmentConn)
			broker.Close()
			return nil
		},
	})

	nextActionProviderFactory := wire.InitialiseNextActionProviderFactory(ffClient, usersClient, p.GenConf, dcClient, empFeClient, bankCustClient)
	registerCustomerProviderFactory := wire.InitialiseRegisterCustomerFactory(ccVgClient, actorClient, usersClient, celestialClient, bankCustClient, ffAccountingClient, billingClient, docsClient, payClient, orderClient, rewardsClient, pinotClient, fireflyLmsClient, ffClient, depositClient, savingsClient, rewardsProjectionClient, profileClient, onboardingClient)
	userCommunicationProcessorFactory := wire.InitialiseUserCommunicationFactory(p.Conf, ccDb, redisCacheClient, commsClient, depositClient, billingClient, ffAccountingClient, usersClient, ccVgClient, ffClient, actorClient, broker)
	cardAndAccountCreationFactory := wire.InitialiseCreateCardAndAccountFactory(ccDb, ccVgClient, actorClient, usersClient, celestialClient, bankCustClient, ffAccountingClient, billingClient, docsClient, payClient, orderClient, rewardsClient, pinotClient, fireflyLmsClient, ffClient, depositClient, savingsClient, rewardsProjectionClient, profileClient, p.Conf, redisCacheClient)
	actProcessor := wire.InitialiseActivityProcessor(ccDb, ccVgClient, usersClient, actorClient, ccDbTxnExecutor,
		authClient, p.Conf, vkycClient, empClient, p.Conf.Notification, p.Conf.NotificationSchedule, billingClient,
		ffAccountingClient, commsClient, docsClient, celestialClient, bankCustClient, vgShipwayClient, ffClient,
		piClient, vgProfileValidationClient, orderClient, payClient, creditLimitEstimatorClient, ccBillGenerationEventPublisher,
		ccStageUpdateEventPublisher, creditCardDocsS3Client, rewardsClient, broker, pinotClient, fireflyLmsClient, dcClient,
		creditReportV2Client, obfuscatorClient, creditLineVgClient, panClient, p.GenConf, userGroupClient,
		nudgeClient, depositClient, savingsClient, nextActionProviderFactory, registerCustomerProviderFactory,
		cardAndAccountCreationFactory, userCommunicationProcessorFactory, creditReportClient, consentClient, riskClient,
		authBeClient, onboardingClient, kycClient, breVgClient, rewardsProjectionClient, fennelVgClient, scienapticVgClient, profileClient, breClient,
		productClient, panVgClient, redisCacheClient, acPiClient)

	ffActProcessor, err := ffAccWire.InitialiseActivityProcessor(ffClient, ccDb, ccVgClient, actorClient, piClient,
		ccTransactionEventPublisher, billingClient, merchantResolutionVgClient, p.AwsConfig, rewardsClient, ffAccountingClient,
		p.Conf, broker, pinotClient, p.GenConf, segmentationClient, ccStageUpdateEventPublisher, ccTransactionNotificationPublisher,
		userGroupClient, usersClient)
	if err != nil {
		return domainActivitiesProviderResult{}, err
	}
	ffCommonProcessorV2 := wire2.InitialiseActivityProcessor(ccFederalPgdb, creditCardDocsS3Client, usersClient, actorClient, celestialClient, vgShipwayClient)

	ffCommonProcessor := wire.InitialiseCommonProcessor(ccDb, ccVgClient, usersClient, actorClient, celestialClient, ccDbTxnExecutor, billingClient, ffAccountingClient, commsClient, docsClient, bankCustClient, orderClient, payClient, rewardsClient, pinotClient, fireflyLmsClient, ffClient, depositClient, savingsClient, rewardsProjectionClient, profileClient, p.Conf, redisCacheClient, broker)

	return domainActivitiesProviderResult{
		DomainActivityProcessors: []any{
			actProcessor,
			ffActProcessor,
			ffCommonProcessor,
			ffCommonProcessorV2,
		},
	}, nil
}
