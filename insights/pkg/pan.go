package pkg

import (
	"context"
	"errors"
	"fmt"
	"regexp"

	"github.com/google/wire"
	"github.com/samber/lo"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"

	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	connectedAccountExternalPb "github.com/epifi/gamma/api/connected_account/external"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	userPb "github.com/epifi/gamma/api/user"
)

//go:generate mockgen -source=pan.go -destination=./mocks/mock_pan.go -package=mocks

var InsightsPanProcessorWireSet = wire.NewSet(NewUnverifiedPanProcessor, wire.Bind(new(IPanProcessor), new(*PanProcessor)))

var PanAlreadyVerifiedErr = fmt.Errorf("pan is already verified")

const PanValidationRegex = "^([a-zA-Z]){3}[p|P][a-zA-Z]([0-9]){4}([a-zA-Z])$"

// ToDo: Move this to a microservice its getting too big to justify its existence in insights pkg
type IPanProcessor interface {
	// GetVerifiedOrUnverifiedPan verified pan if available
	// otherwise it returns latest unverified pan submitted by user anywhere on app
	// if no pan is available, it returns not found error
	// caller should check IsVerified field to know if pan is verified or not
	GetVerifiedOrUnverifiedPan(ctx context.Context, actorId string) (*GetVerifiedOrUnverifiedPanResponse, error)
	StoreUnverifiedPan(ctx context.Context, actorId, pan string, verificationMethod userPb.VerificationMethod) error
}

type GetVerifiedOrUnverifiedPanResponse struct {
	Pan        string
	IsVerified bool
}

type PanProcessor struct {
	userClient             userPb.UsersClient
	creditReportClient     creditReportV2Pb.CreditReportManagerClient
	connectedAccountClient connectedAccountPb.ConnectedAccountClient
}

func NewUnverifiedPanProcessor(
	userClient userPb.UsersClient,
	creditReportClient creditReportV2Pb.CreditReportManagerClient,
	connectedAccountClient connectedAccountPb.ConnectedAccountClient) *PanProcessor {
	return &PanProcessor{
		userClient:             userClient,
		creditReportClient:     creditReportClient,
		connectedAccountClient: connectedAccountClient,
	}
}

func (u *PanProcessor) GetVerifiedOrUnverifiedPan(ctx context.Context, actorId string) (*GetVerifiedOrUnverifiedPanResponse, error) {
	user, err := u.getUser(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	pan := user.GetProfile().GetPAN()
	if pan != "" {
		return &GetVerifiedOrUnverifiedPanResponse{
			Pan:        pan,
			IsVerified: true,
		}, nil
	}
	/*	Priority order to get unverified pan
		1. Unverified pan submitted by user in past
		2. Pan from credit report
		3. Pan from any of the connected accounts
		These are intentionally kept in serial order to avoid unnecessary fanout
	*/
	unverifiedPan, err := u.extractLatestUnverifiedPan(user)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("failed to extract latest unverified pan: %w", err)
	}
	if unverifiedPan == "" {
		unverifiedPan, err = u.getPanFromCreditReport(ctx, actorId)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, fmt.Errorf("failed to get pan from credit report: %w", err)
		}
	}
	if unverifiedPan == "" {
		unverifiedPan, err = u.getPanFromAA(ctx, actorId)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, fmt.Errorf("failed to get pan from AA: %w", err)
		}
	}

	if unverifiedPan == "" {
		return nil, epifierrors.ErrRecordNotFound
	}

	return &GetVerifiedOrUnverifiedPanResponse{
		Pan:        unverifiedPan,
		IsVerified: false,
	}, nil
}

func (u *PanProcessor) getPanFromCreditReport(ctx context.Context, actorId string) (string, error) {
	res, err := u.creditReportClient.GetCreditReport(ctx, &creditReportV2Pb.GetCreditReportRequest{
		ActorId: actorId,
	})

	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil && !res.GetStatus().IsRecordNotFound() {
		return "", fmt.Errorf("failed to get credit report: %w", rpcErr)
	}
	if res.GetStatus().IsRecordNotFound() {
		return "", epifierrors.ErrRecordNotFound
	}

	return res.GetCreditReport().GetCreditReportData().GetPan(), nil
}

func (u *PanProcessor) getPanFromAA(ctx context.Context, actorId string) (string, error) {
	getAccountsRes, err := u.connectedAccountClient.GetAccounts(ctx, &connectedAccountPb.GetAccountsRequest{
		ActorId: actorId,
		AccountFilterList: []connectedAccountExternalPb.AccountFilter{
			connectedAccountExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
		},
	})
	if rpcErr := epifigrpc.RPCError(getAccountsRes, err); rpcErr != nil && !getAccountsRes.GetStatus().IsRecordNotFound() {
		return "", fmt.Errorf("failed to get connected accounts: %w", rpcErr)
	}
	if getAccountsRes.GetStatus().IsRecordNotFound() {
		return "", epifierrors.ErrRecordNotFound
	}

	accountIds := lo.Map(getAccountsRes.GetAccountDetailsList(), func(details *connectedAccountExternalPb.AccountDetails, _ int) string {
		return details.GetAccountId()
	})

	if len(accountIds) == 0 {
		return "", epifierrors.ErrRecordNotFound
	}

	accountDetailsBulkRes, err := u.connectedAccountClient.GetAccountDetailsBulk(ctx, &connectedAccountPb.GetAccountDetailsBulkRequest{
		AccountIdList:          accountIds,
		AccountDetailsMaskList: []connectedAccountExternalPb.AccountDetailsMask{connectedAccountExternalPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_PROFILE},
	})

	if rpcErr := epifigrpc.RPCError(accountDetailsBulkRes, err); rpcErr != nil {
		return "", fmt.Errorf("failed to get account details bulk: %w", rpcErr)
	}

	for _, accountDetails := range accountDetailsBulkRes.GetAccountDetailsMap() {
		holders, err := accountDetails.GetGenericHolderDetails()
		if err != nil {
			return "", fmt.Errorf("failed to get generic holder details: %w", err)
		}
		for _, holder := range holders {
			if holder.GetPan() != "" {
				return holder.GetPan(), nil
			}
		}
	}

	return "", epifierrors.ErrRecordNotFound
}

func (u *PanProcessor) extractLatestUnverifiedPan(user *userPb.User) (string, error) {
	if user.GetProfile().GetPAN() != "" {
		return "", PanAlreadyVerifiedErr
	}

	// find the latest pan data verification details
	dataVerificationDetails := user.GetDataVerificationDetails().GetDataVerificationDetails()
	for i := len(dataVerificationDetails) - 1; i >= 0; i-- {
		details := dataVerificationDetails[i]
		if details.GetDataType() == userPb.DataType_DATA_TYPE_PAN && details.GetPanNumber() != "" {
			return details.GetPanNumber(), nil
		}
	}
	return "", epifierrors.ErrRecordNotFound
}

func (u *PanProcessor) getUser(ctx context.Context, actorId string) (*userPb.User, error) {
	res, err := u.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{ActorId: actorId},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return nil, fmt.Errorf("failed to get user: %w", rpcErr)
	}

	return res.GetUser(), nil
}

func (u *PanProcessor) StoreUnverifiedPan(ctx context.Context, actorId, pan string, verificationMethod userPb.VerificationMethod) error {
	user, err := u.getUser(ctx, actorId)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	unverifiedPan, err := u.extractLatestUnverifiedPan(user)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return fmt.Errorf("failed to extract latest unverified pan: %w", err)
	}
	if unverifiedPan == pan {
		// skip adding this pan as it is already added in user data verification details
		return nil
	}

	matchString, err := regexp.MatchString(PanValidationRegex, pan)
	if err != nil {
		return fmt.Errorf("failed to match pan regex: %s", PanValidationRegex)
	}
	if !matchString {
		return fmt.Errorf("string data does not match regex: %s", PanValidationRegex)
	}

	userDataPan := &userPb.DataVerificationDetail{
		DataType: userPb.DataType_DATA_TYPE_PAN,
		DataValue: &userPb.DataVerificationDetail_PanNumber{
			PanNumber: pan,
		},
		VerificationEntity: commonvgpb.Vendor_VENDOR_UNSPECIFIED,
		VerificationMethod: verificationMethod,
	}
	if user.GetDataVerificationDetails() == nil {
		user.DataVerificationDetails = &userPb.DataVerificationDetails{}
	}
	user.GetDataVerificationDetails().DataVerificationDetails = []*userPb.DataVerificationDetail{userDataPan}

	res, err := u.userClient.UpdateUser(ctx, &userPb.UpdateUserRequest{
		User:       user,
		UpdateMask: []userPb.UserFieldMask{userPb.UserFieldMask_DATA_VERIFICATION_DETAILS},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return fmt.Errorf("failed to update user data verification details: %w", rpcErr)
	}

	return nil
}
