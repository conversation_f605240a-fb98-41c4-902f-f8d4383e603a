// nolint: gosec
package sort

import (
	"context"
	"fmt"
	"math/rand"
	"sort"
	"time"

	"github.com/mohae/deepcopy"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/hash"
	"github.com/epifi/be-common/pkg/logger"

	dePb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/dynamicelements/config/genconf"
	"github.com/epifi/gamma/dynamicelements/constant"
)

var softIntentCategoryToServiceMap = map[string]string{
	onbPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_INSTANT_LOANS.String():   types.ServiceName_PRE_APPROVED_LOAN_SERVICE.String(),
	onbPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_GLOBAL_SPENDING.String(): types.ServiceName_TIERING_SERVICE.String(),
	onbPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_INVEST_MONEY.String():    types.ServiceName_US_STOCKS_SERVICE.String(),
	onbPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_EVERYDAY_NEEDS.String():  types.ServiceName_TIERING_SERVICE.String(),
}

type SoftIntentSortingStrategy struct {
	onbClient onbPb.OnboardingClient
	dynConf   *genconf.Config
}

func NewSoftIntentSortingStrategy(onbClient onbPb.OnboardingClient, dynConf *genconf.Config) *SoftIntentSortingStrategy {
	return &SoftIntentSortingStrategy{
		onbClient: onbClient,
		dynConf:   dynConf,
	}
}

func (s *SoftIntentSortingStrategy) Sort(ctx context.Context, actorId string, screenName deeplinkPb.Screen, sessionId string, dynamicElements []*dePb.DynamicElement) ([]*dePb.DynamicElement, error) {
	// Read priority order conditions from config
	// 	->	The priority order conditions can be customized per individual screen
	// 	->	The priority order is based on two criteria
	// 		(i) ElementUtilityType -- whether the dynamic element is an alert, marketing info, insight etc.
	//			e.g: Alert type may have precedence over Marketing type
	// 		(ii) ServiceName -- The owner service of the dynamic element
	// 			e.g: Pay service may have the highest priority on Pay screen
	// 			By default comms service is given the highest priority.
	// Read the priority order conditions for this screen
	screenNameMap, ok := s.dynConf.PriorityOrder()[screenName.String()]
	if !ok {
		// use default map if the priority order for the given screen name is not found in config
		screenNameMap, ok = s.dynConf.PriorityOrder()[constant.Default]
		if !ok {
			logger.Error(ctx, "could not read priority order conditions for dynamic elements from config", zap.String(logger.SCREEN, screenName.String()))
			return nil, fmt.Errorf("could not read priority order conditions from config")
		}
	}

	// deep copy the screen name map and shuffle the service name priority order basis session id hash
	screenNameMap = deepcopy.Copy(screenNameMap).(map[string][]string)
	serviceNamePriorityOrder := screenNameMap[constant.ServiceName]
	rnd := rand.New(rand.NewSource(int64(hash.Hash(sessionId, 0))))
	rnd.Shuffle(len(serviceNamePriorityOrder), func(i, j int) {
		serviceNamePriorityOrder[i], serviceNamePriorityOrder[j] = serviceNamePriorityOrder[j], serviceNamePriorityOrder[i]
	})

	// get preferred soft intent category based on onboarding time
	getDetailsRes, getDetailsErr := s.onbClient.GetDetails(ctx, &onbPb.GetDetailsRequest{
		ActorId:    actorId,
		CachedData: true,
	})
	if rpcErr := epifigrpc.RPCError(getDetailsRes, getDetailsErr); rpcErr != nil {
		logger.Error(ctx, "error getting onboarding details", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, fmt.Errorf("GetDetails rpc failed, %w", rpcErr)
	}

	softIntentDerivedData := onbPb.GetSoftIntentDerivedData(getDetailsRes.GetDetails().GetStageMetadata().GetSoftIntentSelectionMetadata().GetSelection())
	if softIntentDerivedData == nil {
		logger.Error(ctx, "error getting derived data for soft intents")
		return nil, fmt.Errorf("error getting derived data for soft intents")
	}

	onboardingTime := getDetailsRes.GetDetails().GetStageDetails().GetStageMapping()[onbPb.OnboardingStage_ACCOUNT_CREATION.String()].GetStartedAt().AsTime()
	daysSinceOnboarding := time.Now().Sub(onboardingTime).Hours() / 24
	preferredSoftIntentCategory := ""
	switch {
	case daysSinceOnboarding < 7:
		preferredSoftIntentCategory = softIntentDerivedData.P0SoftIntentCategory
	case daysSinceOnboarding < 14:
		preferredSoftIntentCategory = softIntentDerivedData.P1SoftIntentCategory
	case daysSinceOnboarding < 21:
		preferredSoftIntentCategory = softIntentDerivedData.P2SoftIntentCategory
	case daysSinceOnboarding < 28:
		preferredSoftIntentCategory = softIntentDerivedData.P3SoftIntentCategory
	}

	// reordering the service name priority order based on preferred soft intent category
	prioritisedServiceName := softIntentCategoryToServiceMap[preferredSoftIntentCategory]
	_, idx, found := lo.FindIndexOf(serviceNamePriorityOrder, func(serviceName string) bool { return serviceName == prioritisedServiceName })
	if found && idx != 0 {
		screenNameMap[constant.ServiceName] = append([]string{prioritisedServiceName}, append(serviceNamePriorityOrder[:idx], serviceNamePriorityOrder[idx+1:]...)...)
	}

	// Return the required number of dynamic elements for this screen based on priority order.
	// Sort the dynamic elements according to the priority order criteria
	// in the decreasing order of precedence i.e. first element has the highest priority.
	// Stable sort to make sure the order given by a particular BE services is not changed.
	sort.SliceStable(dynamicElements, func(i, j int) bool {
		return comparePriorities(dynamicElements[i], dynamicElements[j],
			getPriorityOrder(screenNameMap, constant.ElementUtilityType), getPriorityOrder(screenNameMap, constant.ServiceName))
	})

	return dynamicElements, nil
}
