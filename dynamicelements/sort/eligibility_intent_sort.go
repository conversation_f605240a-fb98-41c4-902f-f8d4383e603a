package sort

import (
	"context"
	"fmt"
	"sort"

	types "github.com/epifi/gamma/api/typesv2"

	"github.com/samber/lo"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/be-common/pkg/epifigrpc"

	onboardingPb "github.com/epifi/gamma/api/user/onboarding"

	dePb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
)

// EligibilityIntentSortingStrategy sorts dynamic elements using their intent and eligibility
type EligibilityIntentSortingStrategy struct {
	onboardingClient onboardingPb.OnboardingClient
}

func NewEligibilityIntentSortingStrategy(onboardingClient onboardingPb.OnboardingClient) *EligibilityIntentSortingStrategy {
	return &EligibilityIntentSortingStrategy{onboardingClient: onboardingClient}
}

var categoryToServiceMap = map[onboardingPb.OnboardingSoftIntentCategory]types.ServiceName{
	onboardingPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_INSTANT_LOANS:   types.ServiceName_PRE_APPROVED_LOAN_SERVICE,
	onboardingPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_GLOBAL_SPENDING: types.ServiceName_SAVINGS_SERVICE,
	onboardingPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_INVEST_MONEY:    types.ServiceName_US_STOCKS_SERVICE,
	onboardingPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_EVERYDAY_NEEDS:  types.ServiceName_SAVINGS_SERVICE,
}

func (p *EligibilityIntentSortingStrategy) Sort(ctx context.Context, actorId string, screenName deeplinkPb.Screen, sessionId string, dynamicElements []*dePb.DynamicElement) ([]*dePb.DynamicElement, error) {
	featureLifecycleResp, featureLifecycleErr := p.onboardingClient.GetFeatureLifecycle(ctx, &onboardingPb.GetFeatureLifecycleRequest{
		ActorId: actorId,
		Features: []onboardingPb.Feature{
			onboardingPb.Feature_FEATURE_SA,
			onboardingPb.Feature_FEATURE_PL,
		},
		WantCachedData: true,
	})
	if rpcErr := epifigrpc.RPCError(featureLifecycleResp, featureLifecycleErr); rpcErr != nil {
		return nil, fmt.Errorf("GetFeatureLifecycle rpc failed, %w", rpcErr)
	}
	featureLifecycleMap := featureLifecycleResp.GetFeatureLifecycleMap()

	getDetailsResp, err := p.onboardingClient.GetDetails(ctx, &onboardingPb.GetDetailsRequest{ActorId: actorId, CachedData: true})
	if rpcErr := epifigrpc.RPCError(getDetailsResp, err); rpcErr != nil {
		logger.Error(ctx, "failed to get onboarding details", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, fmt.Errorf("GetDetails rpc failed, %w", rpcErr)
	}
	// if user selected no intent, we get a list with length 0
	userSelectedIntents := getDetailsResp.GetDetails().GetStageMetadata().GetSoftIntentSelectionMetadata().GetSelection()

	sortedIntentDataList := orderAllPossibleIntents(userSelectedIntents, featureLifecycleMap)
	orderedDynamicElements := make([]*dePb.DynamicElement, 0)
	for _, intent := range sortedIntentDataList {
		for _, dynamicElement := range dynamicElements {
			if dynamicElement.GetOwnerService() == intent.service {
				orderedDynamicElements = append(orderedDynamicElements, dynamicElement)
			}
		}
	}
	return orderedDynamicElements, nil
}

func orderAllPossibleIntents(userSelectedIntents []onboardingPb.OnboardingSoftIntent, featureLifecycleMap map[string]*onboardingPb.FeatureLifecycle) []*intentActivationEligibilityData {
	var allPossibleIntents []onboardingPb.OnboardingSoftIntent
	for intent, _ := range onboardingPb.SoftIntentToCategoryMap {
		allPossibleIntents = append(allPossibleIntents, intent)
	}

	var intentDataList []*intentActivationEligibilityData
	for _, intent := range allPossibleIntents {
		intentDataList = append(intentDataList, makeIntentData(intent, userSelectedIntents, featureLifecycleMap))
	}
	sort.SliceStable(intentDataList, func(a, b int) bool {
		return compareIntents(intentDataList[a], intentDataList[b])
	})
	return intentDataList
}

func compareIntents(intent1Data, intent2Data *intentActivationEligibilityData) bool {
	categoryPriorityOrder := []onboardingPb.OnboardingSoftIntentCategory{
		onboardingPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_EVERYDAY_NEEDS,
		onboardingPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_INSTANT_LOANS,
		onboardingPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_GLOBAL_SPENDING,
		onboardingPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_INVEST_MONEY,
	}

	eligibilityPriorityOrder := []onboardingPb.FeatureEligibility_Status{
		onboardingPb.FeatureEligibility_STATUS_PASSED,
		onboardingPb.FeatureEligibility_STATUS_UNKNOWN,
		onboardingPb.FeatureEligibility_STATUS_FAILED,
	}

	// prioritise for which intent has been received from user
	if intent1Data.hasShownIntent && !intent2Data.hasShownIntent {
		return true
	}
	if intent2Data.hasShownIntent && !intent1Data.hasShownIntent {
		return false
	}

	// when user has shown intent for both intents
	if intent1Data.hasShownIntent {
		// prioritise using category of the intent
		if lo.IndexOf(categoryPriorityOrder, intent1Data.category) < lo.IndexOf(categoryPriorityOrder, intent2Data.category) {
			return true
		}
		if lo.IndexOf(categoryPriorityOrder, intent2Data.category) < lo.IndexOf(categoryPriorityOrder, intent1Data.category) {
			return false
		}
	} else { // when user has not shown intent for either of the intents
		// prioritise Active features over non-active ones
		if intent1Data.featureActivationStatus == onboardingPb.FeatureStatus_FEATURE_STATUS_ACTIVE && intent2Data.featureActivationStatus != onboardingPb.FeatureStatus_FEATURE_STATUS_ACTIVE {
			return true
		}
		if intent2Data.featureActivationStatus == onboardingPb.FeatureStatus_FEATURE_STATUS_ACTIVE && intent1Data.featureActivationStatus != onboardingPb.FeatureStatus_FEATURE_STATUS_ACTIVE {
			return false
		}

		// prioritise using eligibility status
		if lo.IndexOf(eligibilityPriorityOrder, intent1Data.userEligibilityStatus) < lo.IndexOf(eligibilityPriorityOrder, intent2Data.userEligibilityStatus) {
			return true
		}
		if lo.IndexOf(eligibilityPriorityOrder, intent2Data.userEligibilityStatus) < lo.IndexOf(eligibilityPriorityOrder, intent1Data.userEligibilityStatus) {
			return false
		}

		// prioritise using category
		if lo.IndexOf(categoryPriorityOrder, intent1Data.category) < lo.IndexOf(categoryPriorityOrder, intent2Data.category) {
			return true
		}
		if lo.IndexOf(categoryPriorityOrder, intent2Data.category) < lo.IndexOf(categoryPriorityOrder, intent1Data.category) {
			return false
		}
	}
	return true
}

type intentActivationEligibilityData struct {
	softIntent              onboardingPb.OnboardingSoftIntent         // intent being considered
	category                onboardingPb.OnboardingSoftIntentCategory // category that the intent belongs to
	feature                 onboardingPb.Feature                      // feature that the category belongs to
	hasShownIntent          bool                                      // if user has shown intent in the soft intent screen
	selectedOrderIdx        int                                       // index of the order in which the intent was selected in soft intent screen
	featureActivationStatus onboardingPb.FeatureStatus                // if user is already activated the core feature associated with the intent
	userEligibilityStatus   onboardingPb.FeatureEligibility_Status    // user eligibility status for the feature
	service                 types.ServiceName
}

func makeIntentData(intent onboardingPb.OnboardingSoftIntent, userSelectedIntents []onboardingPb.OnboardingSoftIntent, featureLifeCycleMap map[string]*onboardingPb.FeatureLifecycle) *intentActivationEligibilityData {
	category := onboardingPb.SoftIntentToCategoryMap[intent]
	service := categoryToServiceMap[category]
	feature := onboardingPb.SoftIntentCategoryToFeatureMap[category]
	featureActivationStatus := featureLifeCycleMap[feature.String()].GetActivationStatus()
	return &intentActivationEligibilityData{
		softIntent:              intent,
		category:                category,
		feature:                 feature,
		hasShownIntent:          lo.Contains(userSelectedIntents, intent),
		selectedOrderIdx:        lo.IndexOf(userSelectedIntents, intent),
		featureActivationStatus: featureActivationStatus,
		userEligibilityStatus:   featureLifeCycleMap[feature.String()].GetEligibilityStatus().GetStatus(),
		service:                 service,
	}
}
