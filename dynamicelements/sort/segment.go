// nolint: gosec
package sort

import (
	"context"
	"fmt"
	"math/rand"
	"sort"

	"github.com/mohae/deepcopy"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/hash"
	"github.com/epifi/be-common/pkg/logger"
	dePb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/dynamicelements/config/genconf"
	"github.com/epifi/gamma/dynamicelements/constant"
)

// SegmentSortingStrategy sorts dynamic elements according to their area priorities
// owner service priority order is shuffled randomly basis session id hash
// owner service is given priority after randomisation if user belongs to given segment
type SegmentSortingStrategy struct {
	segmentClient segmentPb.SegmentationServiceClient
	dynConf       *genconf.Config
}

func NewSegmentSortingStrategy(segmentClient segmentPb.SegmentationServiceClient, dynConf *genconf.Config) *SegmentSortingStrategy {
	return &SegmentSortingStrategy{
		segmentClient: segmentClient,
		dynConf:       dynConf,
	}
}

func (r *SegmentSortingStrategy) Sort(ctx context.Context, actorId string, screenName deeplinkPb.Screen, sessionId string, dynamicElements []*dePb.DynamicElement) ([]*dePb.DynamicElement, error) {
	// Read priority order conditions from config
	// 	->	The priority order conditions can be customized per individual screen
	// 	->	The priority order is based on two criteria
	// 		(i) ElementUtilityType -- whether the dynamic element is an alert, marketing info, insight etc.
	//			e.g: Alert type may have precedence over Marketing type
	// 		(ii) ServiceName -- The owner service of the dynamic element
	// 			e.g: Pay service may have the highest priority on Pay screen
	// 			By default comms service is given the highest priority.
	// Read the priority order conditions for this screen
	screenNameMap, ok := r.dynConf.PriorityOrder()[screenName.String()]
	if !ok {
		// use default map if the priority order for the given screen name is not found in config
		screenNameMap, ok = r.dynConf.PriorityOrder()[constant.Default]
		if !ok {
			logger.Error(ctx, "could not read priority order conditions for dynamic elements from config", zap.String(logger.SCREEN, screenName.String()))
			return nil, fmt.Errorf("could not read priority order conditions from config")
		}
	}

	// deep copy the screen name map and shuffle the service name priority order basis session id hash
	screenNameMap = deepcopy.Copy(screenNameMap).(map[string][]string)
	serviceNamePriorityOrder := screenNameMap[constant.ServiceName]
	rnd := rand.New(rand.NewSource(int64(hash.Hash(sessionId, 0))))
	rnd.Shuffle(len(serviceNamePriorityOrder), func(i, j int) {
		serviceNamePriorityOrder[i], serviceNamePriorityOrder[j] = serviceNamePriorityOrder[j], serviceNamePriorityOrder[i]
	})

	// iterate over SegmentPriorityConfig map and check if user belongs to any segment
	// if found prioritise that segment for that user
	for _, value := range r.dynConf.SegmentPriorityConfig() {
		prioritisedServiceName := value.ServiceName
		prioritisedSegmentIdExp := value.SegmentIdExpression
		_, idx, found := lo.FindIndexOf(serviceNamePriorityOrder, func(serviceName string) bool { return serviceName == prioritisedServiceName })
		if found && idx != 0 {
			isMemberOfExpressionsRes, err := r.segmentClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
				ActorId:              actorId,
				SegmentIdExpressions: []string{prioritisedSegmentIdExp},
			})
			if rpcErr := epifigrpc.RPCError(isMemberOfExpressionsRes, err); rpcErr != nil {
				logger.Error(ctx, "error while checking if user belongs to segment id expression")
			} else if isMemberOfPrioritisedSegmentId(isMemberOfExpressionsRes) {
				screenNameMap[constant.ServiceName] = append([]string{prioritisedServiceName}, append(serviceNamePriorityOrder[:idx], serviceNamePriorityOrder[idx+1:]...)...)
				break
			}
		}
	}

	// Return the required number of dynamic elements for this screen based on priority order.
	// Sort the dynamic elements according to the priority order criteria
	// in the decreasing order of precedence i.e. first element has the highest priority.
	// Stable sort to make sure the order given by a particular BE services is not changed.
	sort.SliceStable(dynamicElements, func(i, j int) bool {
		return comparePriorities(dynamicElements[i], dynamicElements[j],
			getPriorityOrder(screenNameMap, constant.ElementUtilityType), getPriorityOrder(screenNameMap, constant.ServiceName))
	})

	return dynamicElements, nil
}

func isMemberOfPrioritisedSegmentId(isMemberOfExpressionsRes *segmentPb.IsMemberOfExpressionsResponse) bool {

	for _, segmentExpressionMembership := range isMemberOfExpressionsRes.GetSegmentExpressionMembershipMap() {
		if segmentExpressionMembership.GetIsActorMember() {
			return true
		}
	}
	return false
}
