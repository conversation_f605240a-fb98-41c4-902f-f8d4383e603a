package sort

import (
	"context"
	"fmt"
	"sort"

	"go.uber.org/zap"

	dePb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/dynamicelements/config/genconf"
	"github.com/epifi/gamma/dynamicelements/constant"
	"github.com/epifi/be-common/pkg/logger"
)

// PrioritySortingStrategy sorts dynamic elements according to their area priorities
type PrioritySortingStrategy struct {
	dynConf *genconf.Config
}

func NewPrioritySortingStrategy(dynConf *genconf.Config) *PrioritySortingStrategy {
	return &PrioritySortingStrategy{dynConf: dynConf}
}

func (p *PrioritySortingStrategy) Sort(ctx context.Context, actorId string, screenName deeplinkPb.Screen, sessionId string, dynamicElements []*dePb.DynamicElement) ([]*dePb.DynamicElement, error) {
	// Read priority order conditions from config
	// 	->	The priority order conditions can be customized per individual screen
	// 	->	The priority order is based on two criteria
	// 		(i) ElementUtilityType -- whether the dynamic element is an alert, marketing info, insight etc.
	//			e.g: Alert type may have precedence over Marketing type
	// 		(ii) ServiceName -- The owner service of the dynamic element
	// 			e.g: Pay service may have the highest priority on Pay screen
	// 			By default comms service is given the highest priority.
	// Read the priority order conditions for this screen
	screenNameMap, ok := p.dynConf.PriorityOrder()[screenName.String()]
	if !ok {
		// use default map if the priority order for the given screen name is not found in config
		screenNameMap, ok = p.dynConf.PriorityOrder()[constant.Default]
		if !ok {
			logger.Error(ctx, "could not read priority order conditions for dynamic elements from config", zap.String(logger.SCREEN, screenName.String()))
			return nil, fmt.Errorf("could not read priority order conditions from config")
		}
	}
	// Return the required number of dynamic elements for this screen based on priority order.
	// Sort the dynamic elements according to the priority order criteria
	// in the decreasing order of precedence i.e. first element has the highest priority.
	// Stable sort to make sure the order given by a particular BE services is not changed.
	sort.SliceStable(dynamicElements, func(i, j int) bool {
		return comparePriorities(dynamicElements[i], dynamicElements[j],
			getPriorityOrder(screenNameMap, constant.ElementUtilityType), getPriorityOrder(screenNameMap, constant.ServiceName))
	})

	return dynamicElements, nil
}
