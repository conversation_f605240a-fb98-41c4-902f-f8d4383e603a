package sort

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	dePb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/feature/release"
)

type SortingStrategyContext struct {
	IsSANewUser           bool
	IsFiLiteUser          bool
	IsWealthAnalyserUser  bool
	IsNonB2BBaseTierUser  bool
	IsUpiTpapUser         bool
	IsPLDropOffUser       bool
	IsSAOnbExpiredUser    bool
	IsSAForcedDropOffUser bool
}

type SortingStrategyFactory struct {
	randomSortingStrategy            *RandomSortingStrategy
	prioritySortingStrategy          *PrioritySortingStrategy
	segmentSortingStrategy           *SegmentSortingStrategy
	eligibilityIntentSortingStrategy *EligibilityIntentSortingStrategy
	softIntentSortingStrategy        *SoftIntentSortingStrategy
	crossAttachSortingStrategy       *CrossAttachSortingStrategy
	nonB2BBaseTierSortingStrategy    *NonB2BBaseTierSortingStrategy
	releaseEvaluator                 release.IEvaluator
}

func NewSortingStrategyFactory(randomSortingStrategy *RandomSortingStrategy, prioritySortingStrategy *PrioritySortingStrategy, segmentSortingStrategy *SegmentSortingStrategy, eligibilityIntentSortingStrategy *EligibilityIntentSortingStrategy, softIntentSortingStrategy *SoftIntentSortingStrategy, crossAttachSortingStrategy *CrossAttachSortingStrategy, nonB2BBaseTierSortingStrategy *NonB2BBaseTierSortingStrategy, releaseEvaluator release.IEvaluator) *SortingStrategyFactory {
	return &SortingStrategyFactory{
		randomSortingStrategy:            randomSortingStrategy,
		prioritySortingStrategy:          prioritySortingStrategy,
		segmentSortingStrategy:           segmentSortingStrategy,
		eligibilityIntentSortingStrategy: eligibilityIntentSortingStrategy,
		softIntentSortingStrategy:        softIntentSortingStrategy,
		crossAttachSortingStrategy:       crossAttachSortingStrategy,
		nonB2BBaseTierSortingStrategy:    nonB2BBaseTierSortingStrategy,
		releaseEvaluator:                 releaseEvaluator,
	}
}

func (s *SortingStrategyFactory) GetSortingStrategy(ctx context.Context, screenName deeplinkPb.Screen, clientContext *dePb.ClientContext, sortingStrategyContext *SortingStrategyContext, actorId string) ISortingStrategy {
	featurePitchWithDynamicElement, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_TIERING_DYNAMIC_ELEMENT_PROMO_WIDGET).WithActorId(actorId))
	// handling gracefully as error in evaluating feature flow should not end the flow
	if err != nil {
		logger.Error(ctx, "failed to evaluate if pitching using dynamic element for tiering is enabled or not. Handling gracefully", zap.Error(err))
	}
	switch {
	case screenName == deeplinkPb.Screen_HOME && (clientContext.GetHomeInfo().GetSection() == dePb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY || clientContext.GetHomeInfo().GetSection() == dePb.HomeScreenAdditionalInfo_SECTION_FEATURE_SECONDARY):
		switch {
		// Avoid fi-lite users being considered as non b2b base tier user
		case sortingStrategyContext.IsNonB2BBaseTierUser && featurePitchWithDynamicElement && !sortingStrategyContext.IsFiLiteUser:
			return s.nonB2BBaseTierSortingStrategy
		case sortingStrategyContext.IsSANewUser:
			return s.softIntentSortingStrategy
		case sortingStrategyContext.IsWealthAnalyserUser, sortingStrategyContext.IsUpiTpapUser,
			sortingStrategyContext.IsPLDropOffUser, sortingStrategyContext.IsSAOnbExpiredUser, sortingStrategyContext.IsSAForcedDropOffUser:
			return s.crossAttachSortingStrategy
		case sortingStrategyContext.IsFiLiteUser:
			return s.eligibilityIntentSortingStrategy
		default:
			return s.segmentSortingStrategy
		}
	default:
		return s.prioritySortingStrategy
	}
}
