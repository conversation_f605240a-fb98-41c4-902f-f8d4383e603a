package crossattach

import (
	lendabilityPb "github.com/epifi/gamma/api/preapprovedloan/lendability"
	productPb "github.com/epifi/gamma/api/product"
)

type evaluateProductsToPitchResponse struct {
	productsToPitch      []productPb.ProductType
	screenerCheckResult  bool
	pdCategory           lendabilityPb.PDCategory
	loanAffinityCategory lendabilityPb.LoanAffinityCategory
}

func (r *evaluateProductsToPitchResponse) GetProductsToPitch() []productPb.ProductType {
	if r != nil {
		return r.productsToPitch
	}
	return nil
}

func (r *evaluateProductsToPitchResponse) GetScreenerCheckResult() bool {
	if r != nil {
		return r.screenerCheckResult
	}
	return false
}

func (r *evaluateProductsToPitchResponse) GetPdCategory() lendabilityPb.PDCategory {
	if r != nil {
		return r.pdCategory
	}
	return lendabilityPb.PDCategory_PD_CATEGORY_UNSPECIFIED
}

func (r *evaluateProductsToPitchResponse) GetLoanAffinityCategory() lendabilityPb.LoanAffinityCategory {
	if r != nil {
		return r.loanAffinityCategory
	}
	return lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_UNSPECIFIED
}
