package crossattach

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/epifi/gamma/api/preapprovedloan/lendability"
	"github.com/epifi/gamma/api/product"
)

func TestGetProductToPitch(t *testing.T) {
	tests := []struct {
		name            string
		actorId         string
		passedScreener  bool
		affinity        lendability.LoanAffinityCategory
		pdCategory      lendability.PDCategory
		expectedProduct product.ProductType
	}{
		{
			name:            "unspecified categories return SA",
			actorId:         "test-1",
			passedScreener:  false,
			affinity:        lendability.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_UNSPECIFIED,
			pdCategory:      lendability.PDCategory_PD_CATEGORY_UNSPECIFIED,
			expectedProduct: product.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
		},
		{
			name:            "passed screener with high affinity medium pd returns loans",
			actorId:         "test-2",
			passedScreener:  true,
			affinity:        lendability.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_HIGH,
			pdCategory:      lendability.PDCategory_PD_CATEGORY_MEDIUM,
			expectedProduct: product.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
		},
		{
			name:            "passed screener with low affinity returns SA",
			actorId:         "test-3",
			passedScreener:  true,
			affinity:        lendability.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_LOW,
			pdCategory:      lendability.PDCategory_PD_CATEGORY_LOW,
			expectedProduct: product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
		},
		{
			name:            "failed screener with low pd always returns loans",
			actorId:         "test-4",
			passedScreener:  false,
			affinity:        lendability.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_HIGH,
			pdCategory:      lendability.PDCategory_PD_CATEGORY_LOW,
			expectedProduct: product.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, _ := getServiceWithMocks(t)
			got := s.getProductToPitch(context.Background(), tt.actorId, tt.passedScreener, tt.affinity, tt.pdCategory)
			assert.Equal(t, tt.expectedProduct, got)
		})
	}
}
