package crossattach

import (
	"flag"
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	mockEvent "github.com/epifi/be-common/pkg/events/mocks"

	"github.com/epifi/gamma/acquisition/config/genconf"
	"github.com/epifi/gamma/acquisition/crossattach/cohort"
	cohortMocks "github.com/epifi/gamma/acquisition/crossattach/cohort/mocks"
	"github.com/epifi/gamma/acquisition/crossattach/test"
	userAttributesDaoMocks "github.com/epifi/gamma/acquisition/crossattach/userattributes/dao/mocks"
	userattributesMocks "github.com/epifi/gamma/acquisition/crossattach/userattributes/mocks"
)

type mockClients struct {
	eventBroker                 *mockEvent.MockBroker
	userAttributesDao           *userAttributesDaoMocks.MockUserAttributesDao
	userAttributeHelper         *userattributesMocks.MockUserAttributesHelper
	cohortTypeToCohortHelperMap map[string]*cohortMocks.MockCohortHelper
}

var (
	gconf *genconf.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions,
// so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	_, dynConf, teardown := test.InitTestServerWithoutDB()
	gconf = dynConf
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func getServiceWithMocks(t *testing.T) (*Service, *mockClients) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	userAttributesDao := userAttributesDaoMocks.NewMockUserAttributesDao(ctrl)
	userAttributeHelper := userattributesMocks.NewMockUserAttributesHelper(ctrl)
	cohortTypeToCohortHelperMap := make(map[string]cohort.CohortHelper)
	cohortTypeToCohortHelperMapMocks := make(map[string]*cohortMocks.MockCohortHelper)
	cohortHelper := cohortMocks.NewMockCohortHelper(ctrl)
	for _, cohortType := range cohortTypeListByPriority {
		cohortTypeToCohortHelperMap[cohortType] = cohortHelper
		cohortTypeToCohortHelperMapMocks[cohortType] = cohortHelper
	}
	eventBroker := mockEvent.NewMockBroker(ctrl)

	return &Service{
			eventBroker:                 eventBroker,
			userAttributesDao:           userAttributesDao,
			userAttributesHelper:        userAttributeHelper,
			cohortTypeToCohortHelperMap: cohortTypeToCohortHelperMap,
		}, &mockClients{
			eventBroker:                 eventBroker,
			userAttributesDao:           userAttributesDao,
			userAttributeHelper:         userAttributeHelper,
			cohortTypeToCohortHelperMap: cohortTypeToCohortHelperMapMocks,
		}
}
