package crossattach

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/acquisition/crossattach/userattributes"
	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"
	lendabilityPb "github.com/epifi/gamma/api/preapprovedloan/lendability"
	productPb "github.com/epifi/gamma/api/product"
)

var defaultProductsToPitchOrder = []productPb.ProductType{
	productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
	productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
	productPb.ProductType_PRODUCT_TYPE_USSTOCKS,
}

// getProductToPitch returns product to pitch based on screener check, loan affinity category and pd category
// find the matrix here - https://docs.google.com/document/d/1Trr0rfbubPi-0S3v1IDP5YRWhXuotB8Etjx_sm5WTjQ/edit?tab=t.0#heading=h.hp1oqx940h9o
func (s *Service) getProductToPitch(_ context.Context, actorId string, passedScreenerCheck bool, affinity lendabilityPb.LoanAffinityCategory, pdCategory lendabilityPb.PDCategory) productPb.ProductType {
	// Handle unspecified cases
	if affinity == lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_UNSPECIFIED ||
		pdCategory == lendabilityPb.PDCategory_PD_CATEGORY_UNSPECIFIED {
		return productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS
	}

	if passedScreenerCheck {
		switch affinity {
		case lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_HIGH:
			switch pdCategory {
			case lendabilityPb.PDCategory_PD_CATEGORY_HIGH:
				return productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT
			case lendabilityPb.PDCategory_PD_CATEGORY_MEDIUM:
				return productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS
			case lendabilityPb.PDCategory_PD_CATEGORY_LOW:
				return productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS
			}
		case lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_MEDIUM:
			switch pdCategory {
			case lendabilityPb.PDCategory_PD_CATEGORY_HIGH:
				return productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT
			case lendabilityPb.PDCategory_PD_CATEGORY_MEDIUM:
				return productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT
			case lendabilityPb.PDCategory_PD_CATEGORY_LOW:
				return productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS
			}
		case lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_LOW:
			switch pdCategory {
			case lendabilityPb.PDCategory_PD_CATEGORY_HIGH:
				return productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT
			case lendabilityPb.PDCategory_PD_CATEGORY_MEDIUM:
				return productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT
			case lendabilityPb.PDCategory_PD_CATEGORY_LOW:
				return productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT
			}
		}
	}

	switch affinity {
	case lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_HIGH:
		switch pdCategory {
		case lendabilityPb.PDCategory_PD_CATEGORY_HIGH:
			return productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS
		case lendabilityPb.PDCategory_PD_CATEGORY_MEDIUM:
			return productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS
		case lendabilityPb.PDCategory_PD_CATEGORY_LOW:
			return productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS
		}
	case lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_MEDIUM:
		switch pdCategory {
		case lendabilityPb.PDCategory_PD_CATEGORY_HIGH:
			return productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS
		case lendabilityPb.PDCategory_PD_CATEGORY_MEDIUM:
			return productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS
		case lendabilityPb.PDCategory_PD_CATEGORY_LOW:
			return productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS
		}
	case lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_LOW:
		switch pdCategory {
		case lendabilityPb.PDCategory_PD_CATEGORY_HIGH:
			return productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS
		case lendabilityPb.PDCategory_PD_CATEGORY_MEDIUM:
			return productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS
		case lendabilityPb.PDCategory_PD_CATEGORY_LOW:
			return productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS
		}
	}

	return productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS
}

func (s *Service) evaluateProductsToPitch(ctx context.Context, actorId string) (*evaluateProductsToPitchResponse, error) {
	var (
		errGroup, gctx       = errgroup.WithContext(ctx)
		screenerCheckResult  = false
		pdCategory           = lendabilityPb.PDCategory_PD_CATEGORY_UNSPECIFIED
		loanAffinityCategory = lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_UNSPECIFIED
	)
	errGroup.Go(func() error {
		getUserAttributeRes, err := s.userAttributesHelper.GetUserAttribute(gctx, &userattributes.GetUserAttributeRequest{
			ActorId:       actorId,
			UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_SCREENER_CHECK_RESULT,
		})
		if err != nil {
			logger.Error(gctx, "failed to get user attribute", zap.Error(err))
			return err
		}
		screenerCheckResult = getUserAttributeRes.GetUserAttributeValue().GetScreenerCheckResult()
		return nil
	})
	errGroup.Go(func() error {
		getUserAttributeRes, err := s.userAttributesHelper.GetUserAttribute(gctx, &userattributes.GetUserAttributeRequest{
			ActorId:       actorId,
			UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_PD_CATEGORY,
		})
		if err != nil {
			logger.Error(gctx, "failed to get user attribute", zap.Error(err))
			return err
		}
		pdCategory = getUserAttributeRes.GetUserAttributeValue().GetPdCategory()
		return nil
	})
	errGroup.Go(func() error {
		getUserAttributeRes, err := s.userAttributesHelper.GetUserAttribute(gctx, &userattributes.GetUserAttributeRequest{
			ActorId:       actorId,
			UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_LOAN_AFFINITY_CATEGORY,
		})
		if err != nil {
			logger.Error(gctx, "failed to get user attribute", zap.Error(err))
			return err
		}
		loanAffinityCategory = getUserAttributeRes.GetUserAttributeValue().GetLoanAffinityCategory()
		return nil
	})

	if err := errGroup.Wait(); err != nil {
		logger.Error(ctx, "error fetching user attributes", zap.Error(err))
		// User attribute retrieval errors are non-critical, so logging the error and proceeding.
	}

	productToPitch := s.getProductToPitch(ctx, actorId, screenerCheckResult, loanAffinityCategory, pdCategory)
	productsToPitch := make([]productPb.ProductType, 0)
	productsToPitch = append(productsToPitch, productToPitch)
	// append rest of the products
	for _, productType := range defaultProductsToPitchOrder {
		if productType != productToPitch {
			productsToPitch = append(productsToPitch, productType)
		}
	}
	return &evaluateProductsToPitchResponse{
		productsToPitch:      productsToPitch,
		screenerCheckResult:  screenerCheckResult,
		pdCategory:           pdCategory,
		loanAffinityCategory: loanAffinityCategory,
	}, nil
}
