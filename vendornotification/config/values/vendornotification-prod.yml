Application:
  Environment: "prod"
  Name: "vendornotification"

KarzaEPANWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "*************"
  SoftBlock: false

# <PERSON><PERSON><PERSON> dont have outbound IPs segregated for UAT and PROD for vKYC service
# Same IPs are whitelisted in both UAT and Prod
KarzaVkycWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "*************,*************,**************"
  SoftBlock: false

AclSmsWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "**************,************,**************,*************"
  SoftBlock: false


KaleyraSmsWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "**************,*************,*************,**************,*************,************,************,**************,***********"
  SoftBlock: false

AclWhatsappWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "*************,*************,*************,*************,************,************"
  SoftBlock: false

GupshupWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "**************,*************"
  SoftBlock: false

NetCoreWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "***************"
  SoftBlock: false

AirtelWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "**************,************,**************"
  SoftBlock: false

PaisabazaarWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "************"
  SoftBlock: false

SetuWhiteList:
  EnableWhitelist: true
  WhitelistedIPs: "**************,***********,************"
  SoftBlock: false

DPandaWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "**************,**************,*************,**************"
  SoftBlock: false

PoshVineWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "**************,***********"
  SoftBlock: false

UPIWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "**************,**************,**************,*************,**************,**************,**********,*************,**************"
  SoftBlock: false

OzonetelWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "*************,**************,*************,**************,*************,*************,************,**********,***********,************,************,************,************,*************,*************,*************"
  SoftBlock: false

FreshchatWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "**************,**********,*************,**************,**************,**************,**************"
  SoftBlock: false

TssWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "**********,***********"
  SoftBlock: false

SmallcaseWhitelist:
  EnableWhitelist: true
  WhitelistedIps: "*************"
  SoftBlock: false


SenseforthWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "************,**************,***********,**************"
  SoftBlock: true

SprinklrWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "**************,***********,*************,**********"
  SoftBlock: false

RiskcovryWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "*************"
  SoftBlock: false

M2PWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "*************,************,************,*************,************,*************,*************"
  SoftBlock: true

IrisWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "***********"
  SoftBlock: false

FiftyfinWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "************,***********"
  SoftBlock: false

AbflWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "*************,*************,************"
  SoftBlock: false

MoneyviewWhiteList:
  EnableWhitelist: true
  WhitelistedIPs: "**************"
  SoftBlock: false

CredgenicsWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "**********,**************,***********,10.50.42.150"
  SoftBlock: false

FederalWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "*************,**************,**************,**********"
  SoftBlock: false

SavenWhiteList:
  EnableWhitelist: true
  WhitelistedIPs: "13.233.5.105,65.1.230.44,43.204.62.188"
  SoftBlock: false

LeadsWhiteList:
  EnableWhitelist: true
  # financebuddha IPs: 3.109.254.254,13.235.12.163
  # mymoneymantra IPs: 180.151.80.125,180.179.200.89,35.154.205.47
  # loantap IPs: 13.248.169.56,35.154.72.24
  # Keshva IPs: 13.235.129.236
  # cpadvisor IPs: 192.168.29.138
  # cashkuber IPs: 65.1.97.47,172.20.10.7
  # mymoneymantra b2c IPs: 180.151.80.125,180.179.200.89,35.154.205.47
  # gocredit IPS: 13.234.88.207,3.108.43.37
  # credit haat IPs: 3.108.186.193
  # fintifi IPs: 13.234.44.216
  # switchmyloan IPs: 43.205.204.167
  # creadmantra IPs: 3.110.189.254
  WhitelistedIPs: "3.109.254.254,13.235.12.163,180.151.80.125,180.179.200.89,35.154.205.47,13.248.169.56,35.154.72.24,13.235.129.236,192.168.29.138,65.1.97.47,172.20.10.7,13.234.88.207,3.108.43.37,3.108.186.193,13.234.44.216,43.205.204.167,3.110.189.254"
  SoftBlock: false

AuthWhiteList:
  EnableWhitelist: true
  # financebuddha IPs: 3.109.254.254,13.235.12.163
  # mymoneymantra IPs: 180.151.80.125,180.179.200.89,35.154.205.47
  # loantap IPs: 13.248.169.56,35.154.72.24
  # Keshva IPs: 13.235.129.236
  # cpadvisor IPs: 192.168.29.138
  # cashkuber IPs: 65.1.97.47,172.20.10.7
  # mymoneymantra b2c IPs: 180.151.80.125,180.179.200.89,35.154.205.47
  # gocredit IPS: 13.234.88.207,3.108.43.37
  # credit haat IPs: 3.108.186.193
  # fintifi IPs: 13.234.44.216
  # switchmyloan IPs: 43.205.204.167
  # creadmantra IPs: 3.110.189.254
  WhitelistedIPs: "3.109.254.254,13.235.12.163,180.151.80.125,180.179.200.89,35.154.205.47,13.248.169.56,35.154.72.24,13.235.129.236,192.168.29.138,65.1.97.47,172.20.10.7,13.234.88.207,3.108.43.37,3.108.186.193,13.234.44.216,43.205.204.167,3.110.189.254"
  SoftBlock: false

NuggetWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "13.235.161.38,3.6.13.44,65.1.72.91"
  SoftBlock: false

UpdateTransactionEventsPublisher:
  QueueName: "prod-payment-callback-update-queue"

InboundTxnPublisher:
  QueueName: "prod-inbound-txn-queue"

InboundUpiTxnPublisher:
  QueueName: "prod-inbound-upi-txn-queue"

InboundLoanTxnPublisher:
  QueueName: "prod-loan-inbound-transaction-queue"

CreateCardCallbackPublisher:
  QueueName: "prod-card-creation-callback-queue"

DispatchPhysicalCardCallbackPublisher:
  QueueName: "prod-card-dispatch-request-callback-queue"

CheckLivenessCallbackPublisher:
  QueueName: "prod-check-liveness-callback-queue"

UPIReqAuthEventPublisher:
  QueueName: "prod-upi-req-auth-processing-queue"

UPIReqAuthMandateEventPublisher:
  QueueName: "prod-upi-req-auth-mandate-processing-queue"

UPIReqAuthValCustEventPublisher:
  QueueName: "prod-upi-req-auth-val-cust-processing-queue"

UPIReqMandateConfirmationEventPublisher:
  QueueName: "prod-upi-req-mandate-confirmation-processing-queue"

UPIRespMandateEventPublisher:
  QueueName: "prod-upi-resp-mandate-processing-queue"

UPIRespPayEventPublisher:
  QueueName: "prod-upi-resp-pay-processing-queue"

UPIReqTxnConfirmationEventPublisher:
  QueueName: "prod-upi-req-txn-confirmation-processing-queue"

UPIReqValAddressEventPublisher:
  QueueName: "prod-upi-req-val-address-processing-queue"

UPIListPspKeysEventPublisher:
  QueueName: "prod-list-psp-keys-processing-queue"

UPIListVaePublisher:
  QueueName: "prod-list-vae-processing-queue"
  BucketName: "epifi-prod-extended-sqs"

CreateDepositCallbackPublisher:
  QueueName: "prod-create-deposit-callback-queue"

PreCloseDepositCallbackPublisher:
  QueueName: "prod-preclose-deposit-callback-queue"

FdAutoRenewCallbackPublisher:
  QueueName: "prod-deposit-maturity-action-callback-queue"

AclSmsCallbackPublisher:
  QueueName: "prod-vn-acl-sms-callback-queue"

KaleyraSmsCallbackPublisher:
  QueueName: "prod-vn-kaleyra-sms-callback-queue"

AclWhatsappCallbackPublisher:
  QueueName: "prod-vn-acl-whatsapp-callback-queue"

AclWhatsappReplyPublisher:
  QueueName: "prod-vn-acl-whatsapp-reply-queue"

GupshupWhatsappCallbackPublisher:
  QueueName: "prod-comms-gupshup-whatsapp-callback-queue"

GupshupRcsCallbackPublisher:
  QueueName: "prod-comms-gupshup-rcs-callback-queue"

NetCoreSmsCallbackPublisher:
  QueueName: "prod-comms-netcore-sms-callback-queue"

AirtelSmsCallbackPublisher:
  QueueName: "prod-comms-airtel-sms-callback-queue"

AirtelWhatsappCallbackPublisher:
  QueueName: "prod-comms-airtel-whatsapp-callback-queue"

DeviceReRegCallbackPublisher:
  QueueName: "prod-device-rereg-callback-queue"

DeviceRegSMSAckPublisher:
  QueueName: "prod-device-reg-sms-ack-queue"

CustomerCreationCallbackPublisher:
  QueueName: "prod-customer-creation-callback-queue"

BankCustCallbackPublisher:
  QueueName: "prod-bankcust-customer-creation-callback-queue"

AccountCreationCallbackPublisher:
  QueueName: "prod-savings-creation-callback-queue"

FederalVkycUpdatePublisher:
  QueueName: "prod-vn-federal-vkyc-update-queue"

UpdateShippingAddressCallbackPublisher:
  QueueName: "prod-shipping-address-update-callback-queue"

KarzaVkycAgentResponsePublisher:
  QueueName: "prod-vn-karza-vkyc-agent-response-queue"

KarzaVkycAuditorResponsePublisher:
  QueueName: "prod-vn-karza-vkyc-auditor-response-queue"

KarzaVkycCallEventPublisher:
  QueueName: "prod-vn-karza-vkyc-call-event-queue"

EmailCallbackPublisher:
  QueueName: "prod-vn-email-callback-queue"

ConsentCallbackPublisher:
  QueueName: "prod-vn-aa-consent-callback-queue"

FICallbackPublisher:
  QueueName: "prod-vn-aa-fi-callback-queue"

CardTrackingCallbackPublisher:
  QueueName: "prod-card-tracking-callback-queue"

AccountLinkStatusCallbackPublisher:
  QueueName: "prod-vn-aa-account-link-status-callback-queue"

UPIReqTxnConfirmationComplaintEventPublisher:
  QueueName: "prod-upi-req-txn-confirmation-complaint-processing-queue"

OzonetelCallDetailsPublisher:
  QueueName: "prod-vn-ozonetel-call-details-queue"

UPIRespComplaintEventPublisher:
  QueueName: "prod-upi-resp-complaint-queue"

FreshchatActionCallbackPublisher:
  QueueName: "prod-vn-freshchat-action-callback-queue"

NuggetEventCallbackPublisher:
  QueueName: "prod-nugget-event-callback-queue"

HealthInsurancePolicyIssuanceEventPublisher:
  QueueName: "prod-salaryprogram-healthinsurance-policy-issuance-completion-queue"

CCNonFinancialNotificationPublisher:
  QueueName: "prod-cc-non-financial-notification-queue"

SmallcaseProcessMFHoldingsWebhookPublisher:
  QueueName: "prod-vn-process-mf-holdings-webhook-extended-queue"
  BucketName: "epifi-wealth-prod-extended-sqs"

SignalWorkflowPublisher:
  QueueName: "prod-celestial-signal-workflow-queue"

UPIReqMapperConfirmationEventPublisher:
  QueueName: "prod-upi-req-mapper-confirmation-processing-queue"

LoansFiftyfinCallbackPublisher:
  QueueName: "prod-vn-loans-fiftyfin-callback-queue"

ProcrastinatorWorkflowPublisher:
  QueueName: "prod-celestial-initiate-procrastinator-workflow-queue"

FederalEscalationUpdateEventPublisher:
  QueueName : "prod-cx-escalation-update-queue"

CcOnboardingStateUpdateEventPublisher:
  QueueName: "prod-cc-onboarding-state-update-event-callback-queue"

VendorRewardFulfillmentPublisher:
  QueueName: "prod-vendor-reward-fulfillment-event-queue"

Server:
  Ports:
    GrpcPort: 8088
    GrpcSecurePort: 9524
    HttpPort: 9999
    HttpPProfPort: 9990

Aws:
  Region: "ap-south-1"

#json file path
PayFundTransferStatusCodeJson: "./mappingJson/fundTransferStatusCodes.json"
PayUpiStatusCodeJson: "./mappingJson/upiStatusCodes.json"
DepositResponseStatusCodeFilePath: "./mappingJson/depositResponseStatusCodes.json"
CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"

SyncRespHandler:
  SyncPublisher:
    Publisher:
      QueueName: "prod-vn-sync-wrapper-queue"

Flags:
  TrimDebugMessageFromStatus: false
  EnableCCTransactionProcessingViaTemporal: false
  EnableNewEndpointInboundNotification: true

Secrets:
  Ids:
    #Federal
    SenderCode: "prod/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "prod/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "prod/vg-vn-vgpci/federal-auth-service-access-code"
    SenderCodeLoans: "prod/vg-vn/federal-auth-loan-sender-code"
    #AA
    AaVgVnSecretsV1: "prod/vg-vn/aa-secrets-v1"
    SahamatiPublicKeyJwk: "prod/vn/sahamati-public-key"
    SenseforthAuthApiKey: "prod/vn/senseforth-auth-api-key"
    SprinklrAuthApiKey: "prod/vg-vn/sprinklr-auth-api-key"
    # external vendor for offers redemption
    DpandaAuthApiKey: "prod/vn/dpanda-auth-api-key"
    PoshvineAuthApiKey: "prod/vn/poshvine-auth-api-key"
    RazorpayAuthApiKey: "prod/vn/razorpay-auth-api-key"
    DpandaVnSecrets: "prod/vn/dpanda-secrets"
    PoshvineVnSecrets: "prod/vn/poshvine-secrets"
    RazorpayVnSecrets: "prod/vn/razorpay-secrets"

SavenRewardVnSecrets:
  Path: "prod/vn/saven-rewards-secrets"

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/vendornotification/secure.log"
  MaxSizeInMBs: 50
  MaxBackups: 20

FeatureFlags:
  AllowCustomerCallbackProcessing: true
  AllowAccountCallbackProcessing: true

# Prod environments have a ALB in its path. Both ALB and Grpc Gateway appends the client IPs to the header
NumberOfHopsThatAddXForwardedFor: 2
VpcCidrIPPrefix: "10.50"

AA:
  AaSecretsVersionToUse: "V1"
  EpifiAaKid: "38283ca6-0463-4dde-9f77-cce2c270c99d"
  FinvuCrId: "cookiejaraalive@finvu"
  IPWhiteListing:
    EnableWhitelist: true
    SoftBlock: false
  OneMoneyCrId: "onemoney"
  TokenIssuer: "https://tokens.sahamati.org.in/auth/realms/sahamati"
  VerifyApiKeyAndJws: true

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CCTransactionNotificationPublisher:
  QueueName: "prod-cc-txn-notification-queue"

CCAcsNotificationPublisher:
  QueueName: "prod-cc-acs-notification-queue"

TssWebhookCallBackPublisher:
  QueueName: "prod-tss-webhook-callback-queue"

CCStatementNotificationPublisher:
  QueueName: "prod-cc-statement-notification-queue"

Freshdesk:
  GroupEnumToGroupIdMapping:
    CALLBACK: 82000080154
    EPIFI_ESCALATION: 82000080123
    ESCALATED_CASES_CLOSURE: 82000080124
    FEDERAL_ESCALATIONS: 82000080120
    L1_SUPPORT: ***********
    L2_SUPPORT: 82000080153
    NON_SFTP_ESCALATIONS: ***********
    SFTP_ESCALATIONS: ***********
    SFTP_PENDING_GROUP: ***********
    FEDERAL_UPDATES: ***********
    L1_SUPPORT_WAITLIST: ***********
    GROUP_RISK_OPS: ***********
    GROUP_L1_SUPPORT_CALL: ***********
    GROUP_L1_SUPPORT_CHAT: ***********
    GROUP_L1_SUPPORT_EMAIL: ***********
    GROUP_L1_SUPPORT_SOCIAL_MEDIA: ***********
    GROUP_OUTBOUND_CALL_BACK: ***********
    GROUP_LOAN_OUTBOUND_CALL: ***********
  ProductCategoryEnumToValueMapping:
    TRANSACTION: "Transactions"
    ACCOUNTS: "Accounts"
    ONBOARDING: "Onboarding"
    SAVE: "Save"
    WAITLIST: "Waitlist"
    RE_ONBOARDING: "Re-onboarding"
    PRODUCT_CATEGORY_REWARDS: "Rewards"
    PRODUCT_CATEGORY_FIT: "FIT"
    PRODUCT_CATEGORY_DEBIT_CARD: "Debit Card"
    PRODUCT_CATEGORY_REFERRALS: "Referrals"
    PRODUCT_CATEGORY_CONNECTED_ACCOUNTS: "Connected Accounts"
    PRODUCT_CATEGORY_FRAUD_AND_RISK: "Fraud & Risk"
    PRODUCT_CATEGORY_JUMP_P2P: "Jump P2P"
    PRODUCT_CATEGORY_PROFILE: "Profile"
    PRODUCT_CATEGORY_SALARY_ACCOUNT: "Salary account"
    PRODUCT_CATEGORY_SEARCH: "Search"
    PRODUCT_CATEGORY_WEALTH_ONBOARDING: "Wealth Onboarding"
    PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS: "Wealth Mutual Funds"
    PRODUCT_CATEGORY_APP_CRASH: "App Crash"
    PRODUCT_CATEGORY_DATA_DELETION: "Data deletion"
    PRODUCT_CATEGORY_SCREENER: "Screener"
    PRODUCT_CATEGORY_GOOGLE_TOKEN_EXPIRED: "Google Token expired"
    PRODUCT_CATEGORY_LANGUAGE_CALLBACK: "Language callback"
    PRODUCT_CATEGORY_CATEGORY_NOT_FOUND: "Category not found"
    PRODUCT_CATEGORY_KYC_OUTCALL: "KYC Outcall"
    PRODUCT_CATEGORY_TRANSACTION_ISSUES: "Transaction Issues"
    PRODUCT_CATEGORY_REWARDS_NEW: "Rewards New"
    PRODUCT_CATEGORY_REFERRALS_NEW: "Referrals New"
    PRODUCT_CATEGORY_GENERAL_ENQUIRIES_ABOUT_FI: "General enquiries about Fi"
    PRODUCT_CATEGORY_NO_RESPONSE_OR_BLANK_CHAT: "No response/ Blank chat"
    PRODUCT_CATEGORY_CALL_DROP_OR_DISCONNECTED: "Call drop/ disconnected"
    PRODUCT_CATEGORY_INSTANT_LOANS: "Instant Loans"
    PRODUCT_CATEGORY_TIERING_PLANS: "Tiering plans"
    PRODUCT_CATEGORY_CREDIT_CARD: "Credit Card"
    PRODUCT_CATEGORY_US_STOCKS: "US stocks"
    PRODUCT_CATEGORY_DEVICE: "Device"
    PRODUCT_CATEGORY_RISK: "Risk"
    PRODUCT_CATEGORY_ON_APP_TRANSACTIONS: "In-App Transactions"
    PRODUCT_CATEGORY_OFF_APP_TRANSACTIONS: "Off-App Transactions"
    PRODUCT_CATEGORY_INSTANT_SALARY: "Instant Salary"
    PRODUCT_CATEGORY_SIMPLIFI_CREDIT_CARD: "SimpliFi Credit Card"
    PRODUCT_CATEGORY_LAMF: "LAMF"
    PRODUCT_CATEGORY_MAGNIFI_CREDIT_CARD: "MagniFi Credit Card"
    PRODUCT_CATEGORY_SALARY_LITE: "Salary Lite"
    PRODUCT_CATEGORY_FI_STORE: "Fi-Store"
    PRODUCT_CATEGORY_GENERAL_ENQUIRY: "General Enquiry"
    PRODUCT_CATEGORY_APP_RELATED: "App Related"
    PRODUCT_CATEGORY_DEPOSITS_AND_INVESTMENTS: "Deposits & Investments"
    PRODUCT_CATEGORY_INCOMPLETE_CONVERSATION: "Incomplete Conversation"
    PRODUCT_CATEGORY_LOANS: "Loans"
    PRODUCT_CATEGORY_NET_WORTH: "Net Worth"
    PRODUCT_CATEGORY_SERVICE_REQUESTS: "Service Requests"
  TransactionTypeEnumToValueMapping:
    DEBIT_CARD: "Debit Card"
    IMPS: "IMPS"
    NEFT: "NEFT"
    RTGS: "RTGS"
    UPI: "UPI"
    ECOM: "Unknown"
    POS_ATM: "Unknown"
    INTRA_BANK: "Intra Bank"
    TRANSACTION_TYPE_UNKNOWN: "Unknown"
  DisputeStatusEnumToValueMapping:
    ACCEPTED: "Accepted"
    REJECTED: "Rejected"
  StatusEnumToValueMapping:
    STATUS_UNSPECIFIED: 0
    OPEN: 2
    PENDING: 3
    RESOLVED: 4
    CLOSED: 5
    WAITING_ON_THIRD_PARTY: 7
    ESCALATED_TO_L2: 8
    ESCALATED_TO_FEDERAL: 11
    STATUS_SEND_TO_PRODUCT: 24
    STATUS_WAITING_ON_PRODUCT: 25
    STATUS_REOPEN: 26
    STATUS_NEEDS_CLARIFICATION_FROM_CX: 27
    STATUS_WAITING_ON_CUSTOMER: 12
  ProductCategoryDetailsEnumToValueMapping:
    MANUAL_WHITELISTING: "Manual Whitelisting"
    APP_DOWNLOAD_ISSUE: "App download issue"
    DEVICE_CHECK_FAILURE: "Device check failure"
    PHONE_NUMBER_OTP: "Phone number OTP"
    EMAIL_SELECTION_FAILURE: "Email selection failure"
    MOTHER_FATHER_NAME: "Mother Father Name"
    PAN_NAME_VALIDATION_FAILURE: "PAN Name validation failure"
    EXISTING_FEDERAL_ACCOUNT: "Existing Federal account"
    KYC: "KYC"
    LIVENESS: "Liveness"
    FACEMATCH_FAIL: "Face-match fail"
    UN_NAME_CHECK: "UN Name check"
    CONFIRM_CARD_MAILING_ADDRESS: "Confirm Card Mailing address"
    UPI_CONSENT_FAILURE: "UPI Consent failure"
    DEVICE_REGISTRATION_FAILURE: "Device Registration Failure"
    CUSTOMER_CREATION_FAILURE: "Customer creation failure"
    ACCOUNT_OPENING_DELAYED: "Account opening delayed"
    CARD_CREATION_FAILURE: "Card creation failure"
    CARD_PIN_SET_FAILURE: "Card PIN set failure"
    UPI_SETUP_FAILURE: "UPI setup failure"
    VKYC: "VKYC"
    REONBOARDING: "Re-onboarding"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_PIN: "PIN"
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_ACTIVATION: "Activation"
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_DELIVERY: "Delivery"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP: "Debited via Fi app  but"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP: "Debited from FI account (via Other App) but"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY: "Min KYC expiry"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_CARDS_ATM: "Cards - ATM"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT: "UPI - Unable to transact"
    PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL: "Investment Transaction Successful"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CLOSURE_REQUEST: "Account Closure Request"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_OPENING_ISSUES: "Account Opening Issues"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_UPGRADE_DOWNGRADE: "Account Upgrade/Downgrade"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_BALANCE_TRANSFER: "Balance Transfer"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CATEGORY_NOT_FOUND: "Category Not Found"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CHEQUEBOOK_RELATED: "Chequebook Related"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_DORMANT: "Dormant"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_FEES_AND_CHARGES: "Fees & Charges"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_KYC_RELATED: "KYC Related"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_LIEN_ON_ACCOUNT: "Lien On Account"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_RE_LOGIN_ISSUES: "Re Login Issues"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_SALARY_PROGRAM: "Salary Program"
    PRODUCT_CATEGORY_DETAILS_APP_RELATED_ISSUES: "App Related Issues"
    PRODUCT_CATEGORY_DETAILS_BANK_INCOMING: "Bank Incoming"
    PRODUCT_CATEGORY_DETAILS_CX_INCOMING: "CX Incoming"
    PRODUCT_CATEGORY_DETAILS_BLOCK_PERMANENTLY: "Block Permanently"
    PRODUCT_CATEGORY_DETAILS_CARD_REQUEST: "Card Request"
    PRODUCT_CATEGORY_DETAILS_CARD_SETTINGS: "Card Settings"
    PRODUCT_CATEGORY_DETAILS_FIT_RULES: "FIT Rules"
    PRODUCT_CATEGORY_DETAILS_JUMP: "Jump"
    PRODUCT_CATEGORY_DETAILS_MF_INVESTMENTS: "MF Investments"
    PRODUCT_CATEGORY_DETAILS_MF_ONBOARDING: "MF Onboarding"
    PRODUCT_CATEGORY_DETAILS_MF_WITHDRAWALS: "MF Withdrawals"
    PRODUCT_CATEGORY_DETAILS_US_STOCKS: "US Stocks"
    PRODUCT_CATEGORY_DETAILS_US_STOCKS_WALLET_ISSUES: "US Stocks Wallet Issues"
    PRODUCT_CATEGORY_DETAILS_DEPRECATED_PRODUCT: "Deprecated Product"
    PRODUCT_CATEGORY_DETAILS_BLANK_CHAT: "Blank Chat"
    PRODUCT_CATEGORY_DETAILS_CALL_DROP_DISCONNECTED: "Call Drop/Disconnected"
    PRODUCT_CATEGORY_DETAILS_INCOMPLETE_EMAIL: "Incomplete Email"
    PRODUCT_CATEGORY_DETAILS_LOAN_APPLICATION_DISBURSAL: "Application/Disbursal Issue"
    PRODUCT_CATEGORY_DETAILS_LOAN_BUREAU_CIBIL: "Bureau/Cibil"
    PRODUCT_CATEGORY_DETAILS_LOAN_COLLECTIONS: "Collections"
    PRODUCT_CATEGORY_DETAILS_LOAN_EMI_RELATED: "EMI Related Issues"
    PRODUCT_CATEGORY_DETAILS_LOAN_LAMF_SHORTFALL: "LAMF Shortfall"
    PRODUCT_CATEGORY_DETAILS_LOAN_CLOSURE_REQUEST: "Loan Closure Request/Issues"
    PRODUCT_CATEGORY_DETAILS_LOAN_OUTCALLING: "Outcalling"
    PRODUCT_CATEGORY_DETAILS_LOAN_PERSONAL_DETAILS: "Personal Details Updatation"
    PRODUCT_CATEGORY_DETAILS_LOAN_REFUND_WAIVER: "Refund/Waiver Request"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_CONNECT: "Unable To Connect"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_DISCONNECT: "Unable to Disconnect"
    PRODUCT_CATEGORY_DETAILS_REWARDS_FI_POINTS_NOT_REFUNDED: "Fi Points Not Refunded"
    PRODUCT_CATEGORY_DETAILS_REWARDS_GIFT_CARDS: "Gift Cards"
    PRODUCT_CATEGORY_DETAILS_REWARDS_INCORRECT_REWARD: "Incorrect Reward Received"
    PRODUCT_CATEGORY_DETAILS_REWARDS_NOT_RECEIVED: "Reward Not Received"
    PRODUCT_CATEGORY_DETAILS_BANK_INITIATED_FREEZE: "Bank Initated Freeze"
    PRODUCT_CATEGORY_DETAILS_INVESTMENT_WITHDRAWALS: "Investment Withdrawals"
    PRODUCT_CATEGORY_DETAILS_LEA_NPCI_COMPLAINT: "LEA/NPCI Complaint"
    PRODUCT_CATEGORY_DETAILS_CALLBACK_REQUEST: "Callback Request"
    PRODUCT_CATEGORY_DETAILS_DATA_DELETION: "Data Deletion"
    PRODUCT_CATEGORY_DETAILS_NACH_AND_MANDATES: "Nach & Mandates"
    PRODUCT_CATEGORY_DETAILS_REVOKE_APP_ACCESS: "Revoke App Access"
    PRODUCT_CATEGORY_DETAILS_STOP_SERVICES: "Stop Services"
    PRODUCT_CATEGORY_DETAILS_AMOUNT_DEBITED: "Amount Debited"
    PRODUCT_CATEGORY_DETAILS_AMOUNT_DEBITED_NOT_CREDITED: "Amount Debited But Not Credited"
    PRODUCT_CATEGORY_DETAILS_AUTOMATED_PAYMENTS: "Automated Payments"
    PRODUCT_CATEGORY_DETAILS_UNAUTHORISED_FRAUD_TRANSACTIONS: "Unauthorised/Fraud Transactions"
    PRODUCT_CATEGORY_DETAILS_CHEQUE_TRANSACTION: "Cheque Transaction"
    PRODUCT_CATEGORY_DETAILS_DATA_NOT_REFRESHED: "Data Not Refreshed"
    PRODUCT_CATEGORY_DETAILS_BUYING_US_STOCKS: "Buying US Stocks"
    PRODUCT_CATEGORY_DETAILS_BUSINESS_COLLABORATION: "Business Collaboration"
    PRODUCT_CATEGORY_DETAILS_NET_BANKING: "Net Banking"
    PRODUCT_CATEGORY_DETAILS_UNREGISTERED_USER: "Unregistered User"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_PAY: "Unable To Pay"
    PRODUCT_CATEGORY_DETAILS_CREDIT_PENDING_TO_FI: "Credit Pending To Fi"
    PRODUCT_CATEGORY_DETAILS_DOCUMENT_REQUEST: "Document Request"
  SubCategoryDetailsEnumToValueMapping:
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_NEW: "New"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_APPROVED: "Approved"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_REJECTED: "Rejected"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_ON_HOLD: "On-Hold"
    SUB_CATEGORY_PIN_UPI_PIN: "UPI PIN"
    SUB_CATEGORY_PIN_DEVICE_PIN: "Device PIN"
    SUB_CATEGORY_PIN_APP_PIN: "App PIN"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_QR_CODE_NOT_WORKING: "QR code not working"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_UNABLE_TO_SET_PIN: "Unable to set PIN"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_POS: "Cannot enable POS"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_CONTACTLESS: "Cannot enable Contactless"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_ATM_WITHDRAWAL: "Cannot enable ATM withdrawal"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_OTP_NOT_RECEIVED: "OTP not received"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_HOW_TO_ACTIVATE: "How to activate"
    SUB_CATEGORY_DEBIT_CARD_DELIVERY_TRACKING: "Tracking"
    SUB_CATEGORY_DEBIT_CARD_DELIVERY_DID_NOT_RECEIVED_CARD: "Did not receive card"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT: "Not credited to merchant"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: "Not credited to beneficiary"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_MERCHANT: "Not credited to merchant"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: "Not credited to beneficiary"
    SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND: "Balance refund"
    SUB_CATEGORY_TRANSACTIONS_CARDS_ATM_DEBITED_BUT_NOT_DISPENSED_AT_MACHINE: "Debited but not dispensed at machine"
    SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UPI_PIN_TRIES_EXCEEDED: "UPI pin tries exceeded"
    SUB_CATEGORY_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED: "Units not allotted"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_FREEZE_ON_ACCOUNT: "Freeze On Account"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_IN_APP_REQUEST_RECEIVED: "In App Request Received (Auto ID)"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_MANUAL_REQUEST: "Manual Request"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_NOT_CLOSABLE_DUE_TO_PENDING_CHARGES: "Not Closable Due To Pending Charges"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_REDIRECTED_TO_APP: "Redirected To App"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_FULL_KYC_ACCOUNT_CLOSED: "Full Kyc Account Closed"
    SUB_CATEGORY_ACCOUNTS_OPENING_ADD_FUNDS_ON_APP: "Add Funds On App"
    SUB_CATEGORY_ACCOUNTS_OPENING_APP_DOWNLOAD: "App Download"
    SUB_CATEGORY_ACCOUNTS_OPENING_DEVICE_REGISTRATION_FAILURE: "Device Registration Failure"
    SUB_CATEGORY_ACCOUNTS_OPENING_CARD_CREATION_PIN_SETUP_FAILURE: "Card Creation & Pin Setup Failure"
    SUB_CATEGORY_ACCOUNTS_OPENING_CONSENT_RELATED: "Consent Related"
    SUB_CATEGORY_ACCOUNTS_OPENING_CUSTOMER_CREATION: "Customer Creation"
    SUB_CATEGORY_ACCOUNTS_OPENING_EXISTING_FEDERAL_ACCOUNT: "Existing Federal Account"
    SUB_CATEGORY_ACCOUNTS_OPENING_KYC_RELATED: "KYC Related"
    SUB_CATEGORY_ACCOUNTS_OPENING_LIVENESS_FACEMATCH_ISSUE: "Liveness & Facematch Issue"
    SUB_CATEGORY_ACCOUNTS_OPENING_APP_SCREENING: "App Screening"
    SUB_CATEGORY_ACCOUNTS_OPENING_REFUND_FOR_ADD_FUNDS: "Refund For Add Funds"
    SUB_CATEGORY_ACCOUNTS_OPENING_REOPEN_CLOSED_ACCOUNT: "Reopen Closed Account"
    SUB_CATEGORY_ACCOUNTS_OPENING_STUCK_AT_EMAIL_VERIFICATION: "Stuck At Email Verfication"
    SUB_CATEGORY_ACCOUNTS_OPENING_VKYC_ISSUES: "Vkyc Issues"
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_FUNDS_ADDED_BUT_ACCOUNT_NOT_UPGRADED: "Funds Added But A/C Not Upgraded"
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_TIER_DOWNGRADE: "Tier Downgrade"
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_WITHIN_COOL_OFF_PERIOD: "Within Cool Off Period"
    SUB_CATEGORY_ACCOUNTS_KYC_MIN_KYC_EXPIRED: "Min Kyc Expired"
    SUB_CATEGORY_ACCOUNTS_KYC_UNABLE_TO_SUBMIT_FORM: "Unable To Submit Form"
    SUB_CATEGORY_ACCOUNTS_KYC_NOT_UPDATED: "KYC Not Updated"
    SUB_CATEGORY_ACCOUNTS_KYC_UPDATED_BUT_ACCOUNT_NOT_ACTIVATED: "KYC Updated but A/C Not Activated"
    SUB_CATEGORY_ACCOUNTS_KYC_SIGNATURE_NOT_UPDATED: "Signature Not Updated"
    SUB_CATEGORY_CARD_REQUEST_CARD_REPLACEMENT: "Card Replacement"
    SUB_CATEGORY_CARD_REQUEST_CARD_UPGRADE: "Card Upgrade"
    SUB_CATEGORY_CARD_REQUEST_CARD_VARIANT_CHANGE: "Card Variant Change"
    SUB_CATEGORY_CARD_REQUEST_NEW_CARD: "New Card"
    SUB_CATEGORY_CARD_REQUEST_VIRTUAL_CARD: "Virtual Card"
    SUB_CATEGORY_CARD_SETTINGS_CARD_ACTIVATION: "Card Activation"
    SUB_CATEGORY_CARD_SETTINGS_CARD_BLOCK: "Card Block"
    SUB_CATEGORY_CARD_SETTINGS_CARD_DELIVERY: "Card Delivery"
    SUB_CATEGORY_CARD_SETTINGS_CARD_LIMIT: "Card Limit"
    SUB_CATEGORY_CARD_SETTINGS_CARD_PIN: "Card Pin"
    SUB_CATEGORY_CARD_SETTINGS_CARD_UNBLOCK: "Card Unblock"
    SUB_CATEGORY_CARD_SETTINGS_INTERNATIONAL_TRANSACTIONS: "International Transactions"
    SUB_CATEGORY_CARD_CHARGES_ANNUAL_CHARGES: "Annual Charges"
    SUB_CATEGORY_CARD_CHARGES_CARD_REPLACEMENT_CHARGES: "Card Replacement Charges"
    SUB_CATEGORY_CARD_CHARGES_FOREIGN_MARKUP_FEE: "Foreign Markup Fee"
    SUB_CATEGORY_CARD_CHARGES_JOINING_FEE: "Joining Fee"
    SUB_CATEGORY_CARD_CHARGES_LATE_PAYMENT_CHARGES: "Late Payment Charges"
    SUB_CATEGORY_CARD_CHARGES_OVERLIMIT_CHARGES: "Overlimit Charges"
    SUB_CATEGORY_CARD_CHARGES_PROCESSING_FEE: "Processing Fee"
    SUB_CATEGORY_CARD_INFO_CARD_BENEFITS: "Card Benefits"
    SUB_CATEGORY_CARD_INFO_CARD_ELIGIBILITY: "Card Eligibility"
    SUB_CATEGORY_CARD_INFO_CARD_FEATURES: "Card Features"
    SUB_CATEGORY_CARD_INFO_CARD_OFFERS: "Card Offers"
    SUB_CATEGORY_CARD_INFO_CARD_REWARDS: "Card Rewards"
    SUB_CATEGORY_CARD_INFO_CARD_TYPES: "Card Types"
    SUB_CATEGORY_ATM_TRANSACTION_AMOUNT_DEBITED_NOT_DISPENSED: "Amount Debited Not Dispensed"
    SUB_CATEGORY_ATM_TRANSACTION_AMOUNT_DISPENSED_NOT_DEBITED: "Amount Dispensed Not Debited"
    SUB_CATEGORY_ATM_TRANSACTION_CARD_BLOCKED: "Card Blocked"
    SUB_CATEGORY_ATM_TRANSACTION_CARD_CAPTURED: "Card Captured"
    SUB_CATEGORY_ATM_TRANSACTION_INCORRECT_AMOUNT_DISPENSED: "Incorrect Amount Dispensed"
    SUB_CATEGORY_ATM_TRANSACTION_LIMIT_EXCEEDED: "Limit Exceeded"
    SUB_CATEGORY_ATM_TRANSACTION_PIN_BLOCKED: "Pin Blocked"
    SUB_CATEGORY_ATM_TRANSACTION_TRANSACTION_DECLINED: "Transaction Declined"
    SUB_CATEGORY_TRANSACTION_ISSUES_AMOUNT_DEBITED_NOT_CREDITED: "Amount Debited Not Credited"
    SUB_CATEGORY_TRANSACTION_ISSUES_AMOUNT_CREDITED_NOT_DEBITED: "Amount Credited Not Debited"
    SUB_CATEGORY_TRANSACTION_ISSUES_DUPLICATE_TRANSACTION: "Duplicate Transaction"
    SUB_CATEGORY_TRANSACTION_ISSUES_FAILED_TRANSACTION: "Failed Transaction"
    SUB_CATEGORY_TRANSACTION_ISSUES_INCORRECT_AMOUNT: "Incorrect Amount"
    SUB_CATEGORY_TRANSACTION_ISSUES_MERCHANT_DISPUTE: "Merchant Dispute"
    SUB_CATEGORY_TRANSACTION_ISSUES_REFUND_NOT_RECEIVED: "Refund Not Received"
    SUB_CATEGORY_TRANSACTION_ISSUES_UNAUTHORIZED_TRANSACTION: "Unauthorized Transaction"
    SUB_CATEGORY_FIXED_DEPOSIT_ACCOUNT_OPENING: "Account Opening"
    SUB_CATEGORY_FIXED_DEPOSIT_INTEREST_PAYOUT: "Interest Payout"
    SUB_CATEGORY_FIXED_DEPOSIT_MATURITY_AMOUNT: "Maturity Amount"
    SUB_CATEGORY_FIXED_DEPOSIT_PREMATURE_WITHDRAWAL: "Premature Withdrawal"
    SUB_CATEGORY_FIXED_DEPOSIT_RENEWAL: "Renewal"
    SUB_CATEGORY_FIXED_DEPOSIT_TDS: "TDS"
    SUB_CATEGORY_SMART_DEPOSIT_ACCOUNT_OPENING: "Account Opening"
    SUB_CATEGORY_SMART_DEPOSIT_INTEREST_PAYOUT: "Interest Payout"
    SUB_CATEGORY_SMART_DEPOSIT_MATURITY_AMOUNT: "Maturity Amount"
    SUB_CATEGORY_SMART_DEPOSIT_PREMATURE_WITHDRAWAL: "Premature Withdrawal"
    SUB_CATEGORY_SMART_DEPOSIT_RENEWAL: "Renewal"
    SUB_CATEGORY_SMART_DEPOSIT_TDS: "TDS"
    SUB_CATEGORY_FIT_RULES_CREATION: "Creation"
    SUB_CATEGORY_FIT_RULES_DELETION: "Deletion"
    SUB_CATEGORY_FIT_RULES_EXECUTION: "Execution"
    SUB_CATEGORY_FIT_RULES_MODIFICATION: "Modification"
    SUB_CATEGORY_JUMP_ELIGIBILITY: "Eligibility"
    SUB_CATEGORY_JUMP_EXECUTION: "Execution"
    SUB_CATEGORY_JUMP_LIMIT: "Limit"
    SUB_CATEGORY_JUMP_SETTLEMENT: "Settlement"
    SUB_CATEGORY_MUTUAL_FUNDS_ACCOUNT_OPENING: "Account Opening"
    SUB_CATEGORY_MUTUAL_FUNDS_INVESTMENT: "Investment"
    SUB_CATEGORY_MUTUAL_FUNDS_KYC: "KYC"
    SUB_CATEGORY_MUTUAL_FUNDS_REDEMPTION: "Redemption"
    SUB_CATEGORY_MUTUAL_FUNDS_SIP: "SIP"
    SUB_CATEGORY_MUTUAL_FUNDS_STATEMENT: "Statement"
    SUB_CATEGORY_MUTUAL_FUNDS_SWITCH: "Switch"
    SUB_CATEGORY_MUTUAL_FUNDS_TAX: "Tax"
    SUB_CATEGORY_US_STOCKS_ACCOUNT_OPENING: "Account Opening"
    SUB_CATEGORY_US_STOCKS_BUYING: "Buying"
    SUB_CATEGORY_US_STOCKS_KYC: "KYC"
    SUB_CATEGORY_US_STOCKS_SELLING: "Selling"
    SUB_CATEGORY_US_STOCKS_STATEMENT: "Statement"
    SUB_CATEGORY_US_STOCKS_TAX: "Tax"
    SUB_CATEGORY_US_STOCKS_TRANSFER: "Transfer"
    SUB_CATEGORY_US_STOCKS_WALLET: "Wallet"
    SUB_CATEGORY_FI_STORE_ACTIVATION: "Activation"
    SUB_CATEGORY_FI_STORE_CANCELLATION: "Cancellation"
    SUB_CATEGORY_FI_STORE_DELIVERY: "Delivery"
    SUB_CATEGORY_FI_STORE_PAYMENT: "Payment"
    SUB_CATEGORY_FI_STORE_REFUND: "Refund"
    SUB_CATEGORY_FI_STORE_RETURN: "Return"
    SUB_CATEGORY_SALARY_PROGRAMS_ACTIVATION: "Activation"
    SUB_CATEGORY_SALARY_PROGRAMS_BENEFITS: "Benefits"
    SUB_CATEGORY_SALARY_PROGRAMS_ELIGIBILITY: "Eligibility"
    SUB_CATEGORY_SALARY_PROGRAMS_EMPLOYER: "Employer"
    SUB_CATEGORY_SALARY_PROGRAMS_SALARY_CREDIT: "Salary Credit"
    SUB_CATEGORY_LOANS_APPLICATION: "Application"
    SUB_CATEGORY_LOANS_DISBURSEMENT: "Disbursement"
    SUB_CATEGORY_LOANS_DOCUMENTATION: "Documentation"
    SUB_CATEGORY_LOANS_EMI: "EMI"
    SUB_CATEGORY_LOANS_FORECLOSURE: "Foreclosure"
    SUB_CATEGORY_LOANS_INTEREST: "Interest"
    SUB_CATEGORY_LOANS_PREPAYMENT: "Prepayment"
    SUB_CATEGORY_LOANS_STATEMENT: "Statement"
    SUB_CATEGORY_ASSETS_BALANCE: "Balance"
    SUB_CATEGORY_ASSETS_CATEGORIZATION: "Categorization"
    SUB_CATEGORY_ASSETS_LINKING: "Linking"
    SUB_CATEGORY_ASSETS_REFRESH: "Refresh"
    SUB_CATEGORY_ASSETS_SYNC: "Sync"
    SUB_CATEGORY_ASSETS_UNLINKING: "Unlinking"
    SUB_CATEGORY_REWARDS_CASHBACK: "Cashback"
    SUB_CATEGORY_REWARDS_FI_COINS: "Fi Coins"
    SUB_CATEGORY_REWARDS_GIFT_CARDS: "Gift Cards"
    SUB_CATEGORY_REWARDS_OFFERS: "Offers"
    SUB_CATEGORY_REWARDS_POINTS: "Points"
    SUB_CATEGORY_REWARDS_VOUCHERS: "Vouchers"
    SUB_CATEGORY_ACCOUNT_SECURITY_BIOMETRIC: "Biometric"
    SUB_CATEGORY_ACCOUNT_SECURITY_DEVICE: "Device"
    SUB_CATEGORY_ACCOUNT_SECURITY_EMAIL: "Email"
    SUB_CATEGORY_ACCOUNT_SECURITY_MOBILE: "Mobile"
    SUB_CATEGORY_ACCOUNT_SECURITY_PASSWORD: "Password"
    SUB_CATEGORY_ACCOUNT_SECURITY_PIN: "PIN"
    SUB_CATEGORY_LANGUAGE_SUPPORT_APP: "App"
    SUB_CATEGORY_LANGUAGE_SUPPORT_COMMUNICATION: "Communication"
    SUB_CATEGORY_LANGUAGE_SUPPORT_DOCUMENTS: "Documents"
    SUB_CATEGORY_LANGUAGE_SUPPORT_SUPPORT: "Support"
    SUB_CATEGORY_DATA_STATEMENTS_ACCOUNT_STATEMENT: "Account Statement"
    SUB_CATEGORY_DATA_STATEMENTS_INTEREST_CERTIFICATE: "Interest Certificate"
    SUB_CATEGORY_DATA_STATEMENTS_LOAN_STATEMENT: "Loan Statement"
    SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT: "Tax Statement"
    SUB_CATEGORY_DATA_STATEMENTS_TDS_CERTIFICATE: "TDS Certificate"
    SUB_CATEGORY_MANDATES_ACTIVATION: "Activation"
    SUB_CATEGORY_MANDATES_CANCELLATION: "Cancellation"
    SUB_CATEGORY_MANDATES_MODIFICATION: "Modification"
    SUB_CATEGORY_MANDATES_REGISTRATION: "Registration"
    SUB_CATEGORY_MANDATES_STATUS: "Status"
    SUB_CATEGORY_PROFILE_UPDATES_ADDRESS: "Address"
    SUB_CATEGORY_PROFILE_UPDATES_DOB: "DOB"
    SUB_CATEGORY_PROFILE_UPDATES_EMAIL: "Email"
    SUB_CATEGORY_PROFILE_UPDATES_MOBILE: "Mobile"
    SUB_CATEGORY_PROFILE_UPDATES_NAME: "Name"
    SUB_CATEGORY_PROFILE_UPDATES_PAN: "PAN"
    SUB_CATEGORY_DEVICE_ISSUES_APP_CRASH: "App Crash"
    SUB_CATEGORY_DEVICE_ISSUES_APP_HANG: "App Hang"
    SUB_CATEGORY_DEVICE_ISSUES_BIOMETRIC: "Biometric"
    SUB_CATEGORY_DEVICE_ISSUES_CAMERA: "Camera"
    SUB_CATEGORY_DEVICE_ISSUES_LOCATION: "Location"
    SUB_CATEGORY_DEVICE_ISSUES_NOTIFICATION: "Notification"
    SUB_CATEGORY_TRANSACTION_TYPES_ATM: "ATM"
    SUB_CATEGORY_TRANSACTION_TYPES_BILL_PAYMENT: "Bill Payment"
    SUB_CATEGORY_TRANSACTION_TYPES_CARD_PAYMENT: "Card Payment"
    SUB_CATEGORY_TRANSACTION_TYPES_CASH_DEPOSIT: "Cash Deposit"
    SUB_CATEGORY_TRANSACTION_TYPES_CASH_WITHDRAWAL: "Cash Withdrawal"
    SUB_CATEGORY_TRANSACTION_TYPES_CHEQUE: "Cheque"
    SUB_CATEGORY_TRANSACTION_TYPES_FD: "FD"
    SUB_CATEGORY_TRANSACTION_TYPES_IMPS: "IMPS"
    SUB_CATEGORY_TRANSACTION_TYPES_NEFT: "NEFT"
    SUB_CATEGORY_TRANSACTION_TYPES_RTGS: "RTGS"
    SUB_CATEGORY_TRANSACTION_TYPES_UPI: "UPI"
    SUB_CATEGORY_UPI_ISSUES_ACTIVATION: "Activation"
    SUB_CATEGORY_UPI_ISSUES_DEACTIVATION: "Deactivation"
    SUB_CATEGORY_UPI_ISSUES_LIMIT: "Limit"
    SUB_CATEGORY_UPI_ISSUES_LINKING: "Linking"
    SUB_CATEGORY_UPI_ISSUES_PIN: "PIN"
    SUB_CATEGORY_UPI_ISSUES_QR: "QR"
    SUB_CATEGORY_UPI_ISSUES_TRANSACTION: "Transaction"
    SUB_CATEGORY_UPI_ISSUES_VPA: "VPA"
  OsTypeEnumToValueMapping:
    ANDROID: "Android"
    IOS: "iOS"
  ResolutionModeEnumToValueMapping:
    RESOLUTION_MODE_AUTO_RESOLUTION: "Auto Resolution"
    RESOLUTION_MODE_BULK_RESOLUTION: "Bulk Resolution"
    RESOLUTION_MODE_MANUAL_RESOLUTION: "Manual Resolution"
    RESOLUTION_MODE_WATSON_RESOLUTION: "Watson Resolution"
  SavingsAccountBalanceEnumToValueMapping:
    SAVINGS_ACCOUNT_BALANCE_LESS_THAN_1: "Less than 1"
    SAVINGS_ACCOUNT_BALANCE_OTHER: "Other"
  MonorailRaisedEnumToValueMapping:
    MONORAIL_RAISED_YES: "Yes"
    MONORAIL_RAISED_NO: "No"
  BooleanEnumToYesNoMapping:
    TRUE: "Yes"
    FALSE: "No"
  DefaultPageSize: 100

CardSwitchFinancialNotificationPublisher:
  QueueName: "prod-card-switch-financial-notification-queue"

CardSwitchNonFinancialNotificationPublisher:
  QueueName: "prod-card-switch-non-financial-notification-queue"

Ozonetel:
  IsPriorityRoutingEnabled: true

AccountStatusCallbackPublisher:
  QueueName: "prod-account-status-callback-queue"

EnachRegistrationAuthorisationCallbackPublisher:
  QueueName: "prod-recurringpayment-creation-auth-vendor-callback-queue"

QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-12231.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:12231"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/growth-infra/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: vendor-notification-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 10000
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 50
      SleepWindow: 15s
      FallbackMaxConcurrency: 10000

KycStatusUpdatePublisher:
  QueueName: "prod-kyc-v2-update-status-queue"

CcSwitchNotificationsBucketName: "epifi-prod-cc-switch-notifications"
CcRawSwitchNotificationsBucketName: "epifi-raw"
EpanCallbackBucketName: "epifi-prod-pan"
M2pFederalSwitchNotificationFilePath: "m2p/federal/%s/%s-switchTxnNotifications.csv"
RawBucketM2pFederalSwitchNotificationFilePath: "vendor/federal_cc_reports/switch_transaction_notifications/%s/%s-switchTxnNotifications.csv"

CredgenicsCallbackStreamProducer:
  EmailStream:
    StreamName: "prod-credgenics-webhook-generic-event-publish-stream"
  SmsStream:
    StreamName: "prod-credgenics-webhook-generic-event-publish-stream"
  WhatsappStream:
    StreamName: "prod-credgenics-webhook-generic-event-publish-stream"
  CallingStream:
    StreamName: "prod-credgenics-webhook-calling-event-publish-stream"
  VoiceMessageStream:
    StreamName: "prod-credgenics-webhook-generic-event-publish-stream"

FederalBankCustKycStateChangePublisher:
  QueueName: "prod-bankcust-kyc-state-change-event-consumer-queue"

FederalResidentialStatusUpdatePublisher:
  QueueName: "prod-bank-customer-residential-status-update-consumer-queue"

FederalMobileNumberUpdatePublisher:
  QueueName: "prod-bank-customer-mobile-number-update-consumer-queue"

PgRazorpayInboundEventPublisher:
  QueueName: "prod-pg-razorpay-inbound-event-queue"

Auth:
  JwtEncryption:
    RSAPrivateKeyPEMPath: "prod/vn/auth/jwt-encryption-private-key"
  ClientCredentials:
    IdToSecretPath: "prod/vn/auth/client-credentials"

Reward:
  DisableDummyApiResponseFlow: true

Nugget:
  NuggetAccountFreezeDummyDetails:
    U1:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: NO_FREEZE_CODE
      FreezeType: FREEZE_STATUS_UNSPECIFIED
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U2:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: NO_FREEZE_CODE
      FreezeType: ACCOUNT_FREEZE_STATUS_UNFROZEN
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U3:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: '[{"id":"***********","date":"2024-11-01T00:00:00Z","contact":{"grievanceOfficerDetails":{"name":"Sh Anup Kuruvilla John, IPS,ADGP","email":"<EMAIL>","rawData":"Sh Anup Kuruvilla John, IPS,ADGP,0471-2300042,<EMAIL>","phoneNumber":"0471-2300042"},"nodalCyberCellOfficerDetails":{"name":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT)","email":"<EMAIL>","rawData":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT),<EMAIL>"}},"state":"Kerala"}]'
    U4:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U5:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U6:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED_MID_LAYER
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: '[{"id":"***********","date":"2024-11-01T00:00:00Z","contact":{"grievanceOfficerDetails":{"name":"Sh Anup Kuruvilla John, IPS,ADGP","email":"<EMAIL>","rawData":"Sh Anup Kuruvilla John, IPS,ADGP,0471-2300042,<EMAIL>","phoneNumber":"0471-2300042"},"nodalCyberCellOfficerDetails":{"name":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT)","email":"<EMAIL>","rawData":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT),<EMAIL>"}},"state":"Kerala"}]'
    U7:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED_MID_LAYER
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U8:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED_MID_LAYER
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U9:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: VKYC_ISSUE
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U10:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: VKYC_ISSUE
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U11:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: VKYC_ISSUE
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U12:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: REKYC_OVERDUE
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U13:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: REKYC_OVERDUE
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U14:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: REKYC_OVERDUE
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U15:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U16:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U17:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U18:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: UNCATEGORIZED_FREEZE
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U19:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: UNCATEGORIZED_FREEZE
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U20:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: UNCATEGORIZED_FREEZE
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U21:
      AccountStatus: OPERATIONAL_STATUS_DORMANT
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U22:
      AccountStatus: OPERATIONAL_STATUS_DORMANT
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U23:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "FORM123"
      FormStatus: STATUS_CREATED
      FormExpiryDate: 28-02-2026
      LeaComplaintDetails: "-"
    U24:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "FORM124"
      FormStatus: STATUS_SUBMITTED
      FormExpiryDate: 28-02-2026
      LeaComplaintDetails: "-"
    U25:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "FORM125"
      FormStatus: STATUS_CANCELLED
      FormExpiryDate: 28-02-2026
      LeaComplaintDetails: "re"
  NuggetTransactionDummyDetails:
    T1:
      CreatedAt: 2025-08-12 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-12 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DEBIT_CARD_CHARGES
      TransactionAmount: 500.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 12 August 2025 at 3:04 PM
    T2:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ECS_ENACH_CHARGES
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T3:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: AMB_CHARGE
      TransactionAmount: 100.46
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T4:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ANYWHERE_BANKING_CHARGE
      TransactionAmount: 4.37
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T5:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DC_DCC_FEE_CHARGE
      TransactionAmount: ********.25
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T6:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DEBIT_CARD_AMC_CHARGE
      TransactionAmount: 90.67
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T7:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DC_FOREX_MARKUP_CHARGE
      TransactionAmount: 111.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T8:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DC_TCS_FEE_CHARGE
      TransactionAmount: 123.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T9:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: FIRST_CARD_ORDER_FEE
      TransactionAmount: 1234.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T10:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: CHEQUE_BOOK_CHARGES
      TransactionAmount: 12345.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T11:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ECOM_POS_DECLINE_CHARGE
      TransactionAmount: 98.24
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T12:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ATM_DECLINE_CHARGE
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T13:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: OTHER_BANK_ATM_CHARGE
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T14:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DUPLICATE_CARD_FEE
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T15:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: ""
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T16:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: ""
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: EXTERNAL
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 1.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T17:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: ""
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: POS
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 2.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T18:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: ""
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: ATM
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 3.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T19:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: INTRA_BANK
      Tags: ""
      TransactionAmount: 4.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T20:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: ECOMM
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 5.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T21:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: RTGS
      Tags: ""
      TransactionAmount: 6.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T22:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: INTRA_BANK
      Tags: ""
      TransactionAmount: 7.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T23:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 10.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T24:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: POS
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 200.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T25:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: NEFT
      Tags: ""
      TransactionAmount: 20000.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T26:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: IMPS
      Tags: ""
      TransactionAmount: 100000.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T27:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: FI_POS_26
      ExecutedAt: ""
      P2P_P2M: P2M
      Provenance: POS
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T28:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: FI_ATM_7
      ExecutedAt: ""
      P2P_P2M: P2M
      Provenance: ATM
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T29:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: UPI1215
      ExecutedAt: ""
      P2P_P2M: P2P
      Provenance: EXTERNAL
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T30:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: FI_POS_6
      ExecutedAt: ""
      P2P_P2M: P2M
      Provenance: POS
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T31:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: UPI130
      ExecutedAt: ""
      P2P_P2M: P2M
      Provenance: USER_APP
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T32:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: UPI129
      ExecutedAt: ""
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T33:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: UPI1095
      ExecutedAt: ""
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM


EnableIndianStocksContentApi: false
