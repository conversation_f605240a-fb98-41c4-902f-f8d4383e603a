// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enableindianstockscontentapi":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableIndianStocksContentApi\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableIndianStocksContentApi, nil
	case "dummyquestvariable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DummyQuestVariable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DummyQuestVariable, nil
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "featureflags":
		return obj.FeatureFlags.Get(dynamicFieldPath[1:])
	case "aa":
		return obj.AA.Get(dynamicFieldPath[1:])
	case "questsdk":
		return obj.QuestSdk.Get(dynamicFieldPath[1:])
	case "reward":
		return obj.Reward.Get(dynamicFieldPath[1:])
	case "nugget":
		return obj.Nugget.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Flags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablecctransactionprocessingviatemporal":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableCCTransactionProcessingViaTemporal\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableCCTransactionProcessingViaTemporal, nil
	case "enablenewendpointinboundnotification":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableNewEndpointInboundNotification\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableNewEndpointInboundNotification, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Flags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FeatureFlags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "allowcustomercallbackprocessing":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AllowCustomerCallbackProcessing\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AllowCustomerCallbackProcessing, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FeatureFlags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AA) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "verifyapikeyandjws":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VerifyApiKeyAndJws\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VerifyApiKeyAndJws, nil
	case "tokenissuer":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TokenIssuer\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TokenIssuer, nil
	case "onemoneycrid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OneMoneyCrId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OneMoneyCrId, nil
	case "finvucrid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FinvuCrId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FinvuCrId, nil
	case "epifiaakid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EpifiAaKid\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EpifiAaKid, nil
	case "aasecretsversiontouse":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AaSecretsVersionToUse\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AaSecretsVersionToUse, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AA", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Reward) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "disabledummyapiresponseflow":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableDummyApiResponseFlow\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableDummyApiResponseFlow, nil
	case "rewardtypetomaxrewardvaluemap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.RewardTypeToMaxRewardValueMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"RewardTypeToMaxRewardValueMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.RewardTypeToMaxRewardValueMap[dynamicFieldPath[1]], nil

		}
		return obj.RewardTypeToMaxRewardValueMap, nil
	case "dummyrewardprocessingstatus":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DummyRewardProcessingStatus\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DummyRewardProcessingStatus, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Reward", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Nugget) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "fistoreredemptionslimit":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FiStoreRedemptionsLimit\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FiStoreRedemptionsLimit, nil
	case "maxdepositstoprocess":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxDepositsToProcess\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxDepositsToProcess, nil
	case "powerupredemptionslimit":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PowerUpRedemptionsLimit\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PowerUpRedemptionsLimit, nil
	case "nuggetaccountfreezedummydetails":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.NuggetAccountFreezeDummyDetails, nil
		case len(dynamicFieldPath) > 1:

			return obj.NuggetAccountFreezeDummyDetails[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.NuggetAccountFreezeDummyDetails, nil
	case "nuggettransactiondummydetails":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.NuggetTransactionDummyDetails, nil
		case len(dynamicFieldPath) > 1:

			return obj.NuggetTransactionDummyDetails[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.NuggetTransactionDummyDetails, nil
	case "nuggetusermockapiprotojsonresp":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.NuggetUserMockAPIProtoJsonResp, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"NuggetUserMockAPIProtoJsonResp\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.NuggetUserMockAPIProtoJsonResp[dynamicFieldPath[1]], nil

		}
		return obj.NuggetUserMockAPIProtoJsonResp, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Nugget", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *NuggetAccountFreezeDummyDetails) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "accountstatus":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AccountStatus\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AccountStatus, nil
	case "processedfreezereason":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ProcessedFreezeReason\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ProcessedFreezeReason, nil
	case "freezetype":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FreezeType\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FreezeType, nil
	case "formid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FormId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FormId, nil
	case "formstatus":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FormStatus\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FormStatus, nil
	case "formexpirydate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FormExpiryDate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FormExpiryDate, nil
	case "leacomplaintdetails":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LeaComplaintDetails\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LeaComplaintDetails, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for NuggetAccountFreezeDummyDetails", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *NuggetTransactionDummyDetails) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "createdat":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CreatedAt\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CreatedAt, nil
	case "errorcode":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ErrorCode\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ErrorCode, nil
	case "executedat":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ExecutedAt\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ExecutedAt, nil
	case "p2p_p2m":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"P2P_P2M\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.P2P_P2M, nil
	case "paymentprotocol":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PaymentProtocol\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PaymentProtocol, nil
	case "provenance":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Provenance\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Provenance, nil
	case "tags":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Tags\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Tags, nil
	case "transactionamount":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TransactionAmount\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TransactionAmount, nil
	case "transactionstatus":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TransactionStatus\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TransactionStatus, nil
	case "createdatreadabletime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CreatedAtReadableTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CreatedAtReadableTime, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for NuggetTransactionDummyDetails", strings.Join(dynamicFieldPath, "."))
	}
}
