package nugget

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/epifi/be-common/pkg/queue"

	"github.com/golang-jwt/jwt"

	commonVgPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	epifiprotojson "github.com/epifi/be-common/pkg/proto/json"

	"github.com/epifi/gamma/api/auth"
	cxChatConsumerPb "github.com/epifi/gamma/api/cx/chat/consumer"
	authPb "github.com/epifi/gamma/api/cx/customer_auth"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/api/vendornotification/cx/chatbot/nugget"
	redactorConf "github.com/epifi/gamma/api/vendors/redactor"
	genConf "github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector"
	dataCollectorOptions "github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

const (
	userId                      = "cp_user_id"
	txnId                       = "TXN_ID"
	afuKey                      = "AFU"
	fiStoreRedemptionCategory   = "REWARD_CATEGORY"
	nuggetKey                   = "sub"
	savingsAccountFreezeDetails = "SAVINGS_ACCOUNT_FREEZE_DETAILS"
	transactionDetails          = "TRANSACTION_DETAILS"
	tier                        = "TIER"
	depositType                 = "DEPOSIT_TYPE"
)

var (
	ErrUnknownField       = status.Error(codes.InvalidArgument, "unknown field requested")
	errInvalidAccessToken = &nugget.FetchDataResponse{
		Status: http.StatusUnauthorized,
		Error:  "invalid access token",
	}
)

type NuggetService struct {
	conf                                 *genConf.Config
	dataCollectorFactory                 data_collector.DataCollectorFactory
	chatbotRequestedDataFieldNameMapping map[string]typesv2.ChatbotRequestedDataField
	authClient                           auth.AuthClient
	vendorMappingClient                  vendormapping.VendorMappingServiceClient
	eventBroker                          events.Broker
	nuggetEventPub                       queue.Publisher
	customerAuthClient                   authPb.CustomerAuthenticationClient
}

func NewNuggetService(
	conf *genConf.Config,
	dataCollectorFactory data_collector.DataCollectorFactory,
	authClient auth.AuthClient,
	vendorMappingClient vendormapping.VendorMappingServiceClient,
	eventBroker events.Broker,
	nuggetEventPub NuggetEventCallbackPublisher,
	customerAuthClient authPb.CustomerAuthenticationClient,
) *NuggetService {
	chatbotRequestedDataFieldNameMapping := make(map[string]typesv2.ChatbotRequestedDataField)
	for enumVal, str := range typesv2.ChatbotRequestedDataField_name {
		if typesv2.ChatbotRequestedDataField(enumVal) == typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_UNSPECIFIED {
			continue
		}
		chatbotRequestedDataFieldNameMapping[strings.TrimPrefix(str, "CHATBOT_USER_DATA_FIELD_")] = typesv2.ChatbotRequestedDataField(enumVal)
	}
	return &NuggetService{
		conf:                                 conf,
		dataCollectorFactory:                 dataCollectorFactory,
		authClient:                           authClient,
		chatbotRequestedDataFieldNameMapping: chatbotRequestedDataFieldNameMapping,
		vendorMappingClient:                  vendorMappingClient,
		eventBroker:                          eventBroker,
		nuggetEventPub:                       nuggetEventPub,
		customerAuthClient:                   customerAuthClient,
	}
}

type NuggetEventCallbackPublisher queue.Publisher

var _ nugget.ChatbotNuggetServiceServer = &NuggetService{}

const (
	NuggetFetchDataRequest           = "NuggetFetchDataRequest"
	NuggetFetchDataResponse          = "NuggetFetchDataResponse"
	NuggetHandleCallbackEventRequest = "NuggetHandleCallbackEventRequest"
	PersistAuthForUserRequest        = "PersistAuthForUserRequest"
)

func (e *NuggetService) FetchData(ctx context.Context, req *nugget.FetchDataRequest) (*nugget.FetchDataResponse, error) {
	redactor.LogCallbackRequestData(ctx, req, NuggetFetchDataRequest, "", redactorConf.Config)

	// IP Whitelist check
	if err := security.CheckWhiteList(ctx, e.conf.NuggetWhiteList(), e.conf.NumberOfHopsThatAddXForwardedFor(), e.conf.VpcCidrIPPrefix()); err != nil {
		logger.Error(ctx, "error while checking IP whitelisting", zap.Error(err))
		return &nugget.FetchDataResponse{
			Status: http.StatusForbidden,
			Error:  "IP not whitelisted",
		}, nil
	}

	if val1, exists1 := e.conf.Nugget().NuggetAccountFreezeDummyDetails().Load(req.GetUserId()); exists1 {
		data := getKeyValuePairForAccountFreezeDummyResponse(val1)
		keyValuePairs, err := convertToKeyValuePairs(ctx, data)
		if err != nil {
			logger.Error(ctx, "failed to convert data for dummy user id", zap.Error(err))
		} else {
			return &nugget.FetchDataResponse{
				Status: http.StatusOK,
				Data:   keyValuePairs,
			}, nil
		}
	} else if val2, exists2 := e.conf.Nugget().NuggetTransactionDummyDetails().Load(req.GetUserId()); exists2 {
		data := getKeyValuePairForTransactionDummyResponse(val2)
		keyValuePairs, err := convertToKeyValuePairs(ctx, data)
		if err != nil {
			logger.Error(ctx, "failed to convert data for dummy user id", zap.Error(err))
		} else {
			return &nugget.FetchDataResponse{
				Status: http.StatusOK,
				Data:   keyValuePairs,
			}, nil
		}
	} else if mockResp, exists3 := e.handleGenericMockUserResponse(ctx, req.GetUserId()); exists3 {
		return mockResp, nil
	}

	if len(req.GetAccessToken()) == 0 {
		return &nugget.FetchDataResponse{
			Status: http.StatusBadRequest,
			Error:  "empty token",
		}, nil
	}

	// Token validation
	validateTokenRes, err := e.authClient.ValidateToken(ctx, &auth.ValidateTokenRequest{
		Token:     req.GetAccessToken(),
		TokenType: auth.TokenType_NUGGET_CHATBOT_ACCESS_TOKEN,
	})

	if rpcErr := epifigrpc.RPCError(validateTokenRes, err); rpcErr != nil {
		switch validateTokenRes.GetStatus().GetCode() {
		case uint32(auth.ValidateTokenResponse_TOKEN_EXPIRY):
			logger.Debug(ctx, "token expired", zap.Error(rpcErr))
			return &nugget.FetchDataResponse{
				Status: http.StatusUnauthorized,
				Error:  "token expired",
			}, nil
		case uint32(auth.ValidateTokenResponse_TOKEN_INVALID):
			logger.Debug(ctx, "token invalid", zap.Error(rpcErr))
			return &nugget.FetchDataResponse{
				Status: http.StatusUnauthorized,
				Error:  "invalid token",
			}, nil
		default:
			logger.Error(ctx, "failed to validate token", zap.Error(rpcErr))
			return &nugget.FetchDataResponse{
				Status: http.StatusInternalServerError,
				Error:  "failed to validate token",
			}, nil
		}
	}

	// Request validation
	if err := e.isRequestValid(req); err != nil {
		logger.Error(ctx, "invalid request", zap.Error(err))
		return &nugget.FetchDataResponse{
			Status: http.StatusBadRequest,
			Error:  fmt.Sprintf("invalid request : %v", err),
		}, nil
	}

	resp, actorId := e.collectDataAndRespond(ctx, req)
	e.logFetchDataResp(ctx, resp, actorId)
	return resp, err
}

func (e *NuggetService) HandleCallbackEvent(ctx context.Context, req *nugget.HandleCallbackEventRequest) (*nugget.HandleCallbackEventResponse, error) {
	if err := security.CheckWhiteList(ctx, e.conf.NuggetWhiteList(), e.conf.NumberOfHopsThatAddXForwardedFor(), e.conf.VpcCidrIPPrefix()); err != nil {
		return nil, err
	}

	redactor.LogCallbackRequestData(ctx, req, NuggetHandleCallbackEventRequest, "", redactorConf.Config)

	if err := e.validateNuggetEventCallbackRequest(req); err != nil {
		logger.Error(ctx, "nugget callback event validation failed",
			zap.Error(err),
			zap.String("event_name", req.GetEventName()),
			zap.String("sub_event_name", req.GetSubEventName()))
		return &nugget.HandleCallbackEventResponse{
			Status: http.StatusBadRequest,
			Error:  fmt.Sprintf("invalid request : %v", err),
		}, nil
	}
	event := &cxChatConsumerPb.ProcessNuggetEventRequest{
		Entity:       req.GetEntity(),
		EventName:    req.GetEventName(),
		SubEventName: req.GetSubEventName(),
		EventData:    req.GetEventData(),
	}
	sqsMsgId, err := e.nuggetEventPub.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "failed to publish nugget callback event",
			zap.Error(err),
			zap.String("event_name", req.GetEventName()),
			zap.String("sub_event_name", req.GetSubEventName()))
		return &nugget.HandleCallbackEventResponse{
			Status: http.StatusInternalServerError,
			Error:  fmt.Sprintf("failed to publish nugget callback event : %v", err),
		}, nil
	}
	logger.Info(ctx, "nugget callback event processed successfully",
		zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId),
		zap.String("event_name", req.GetEventName()),
		zap.String("sub_event_name", req.GetSubEventName()))

	return &nugget.HandleCallbackEventResponse{
		Status: http.StatusOK,
	}, nil
}

func (e *NuggetService) PersistAuthForUser(ctx context.Context, req *nugget.PersistAuthForUserRequest) (*nugget.PersistAuthForUserResponse, error) {
	if err := security.CheckWhiteList(ctx, e.conf.NuggetWhiteList(), e.conf.NumberOfHopsThatAddXForwardedFor(), e.conf.VpcCidrIPPrefix()); err != nil {
		return nil, err
	}

	redactor.LogCallbackRequestData(ctx, req, PersistAuthForUserRequest, "", redactorConf.Config)

	vendorIdResp, err := e.vendorMappingClient.GetInputIdByVendor(ctx, &vendormapping.GetInputIdByVendorRequest{
		Id:     req.GetUserId(),
		Vendor: commonVgPb.Vendor_ZOMATO,
	})
	if rpcErr := epifigrpc.RPCError(vendorIdResp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching input id from vendor id", zap.String("zomato uid", req.GetUserId()), zap.Error(rpcErr))
		return &nugget.PersistAuthForUserResponse{
			Status: http.StatusInternalServerError,
		}, nil
	}
	actorId := vendorIdResp.GetInputId()

	authFactorResp, authFactorErr := e.customerAuthClient.PersistAuthFactor(ctx, &authPb.PersistAuthFactorRequest{
		ActorId:    actorId,
		AuthFactor: authPb.AuthFactor_MOBILE_PROMPT,
	})
	if rpcErr := epifigrpc.RPCError(authFactorResp, authFactorErr); rpcErr != nil {
		logger.Error(ctx, "failed to persist auth factor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return &nugget.PersistAuthForUserResponse{
			Status: http.StatusInternalServerError,
		}, nil
	}

	return &nugget.PersistAuthForUserResponse{
		Status: http.StatusOK,
	}, nil
}

func (e *NuggetService) validateNuggetEventCallbackRequest(req *nugget.HandleCallbackEventRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.GetEventName() == "" {
		return fmt.Errorf("event name is required and cannot be empty")
	}
	if req.GetSubEventName() == "" {
		return fmt.Errorf("sub-event name is required and cannot be empty")
	}
	if req.GetEventData() == nil {
		return fmt.Errorf("event data is required and cannot be nil")
	}
	return nil
}

func (e *NuggetService) logFetchDataResp(ctx context.Context, resp *nugget.FetchDataResponse, actorId string) {
	var fields []zap.Field

	if actorId != "" {
		fields = append(fields, zap.String(logger.ACTOR_ID_V2, actorId))
	}

	redactor.LogPayload(ctx, NuggetFetchDataResponse, resp, redactorConf.Config, fields...)
}

func (e *NuggetService) collectDataAndRespond(ctx context.Context, req *nugget.FetchDataRequest) (resp *nugget.FetchDataResponse, actorId string) {
	dataMap := map[string]any{}
	accessToken := req.GetAccessToken()

	claims := jwt.MapClaims{}
	_, _, err := new(jwt.Parser).ParseUnverified(accessToken, claims)

	if err != nil {
		logger.Error(ctx, "failed to parse access token required in data collector resource API", zap.Error(err))
		return errInvalidAccessToken, actorId
	}

	zomatoId, exist := claims[nuggetKey].(string)
	if !exist {
		logger.Error(ctx, "missing zomato vendormapping ID in access token")
		return errInvalidAccessToken, actorId
	}

	vendorIdResp, err := e.vendorMappingClient.GetInputIdByVendor(ctx, &vendormapping.GetInputIdByVendorRequest{
		Id:     zomatoId,
		Vendor: commonVgPb.Vendor_ZOMATO,
	})

	if rpcErr := epifigrpc.RPCError(vendorIdResp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching input id from vendor id", zap.String("Nugget uid", zomatoId), zap.Error(rpcErr))
		return &nugget.FetchDataResponse{
			Status: http.StatusInternalServerError,
			Error:  "internal server error",
		}, actorId
	}
	actorId = vendorIdResp.GetInputId()

	inputParams := dataCollectorOptions.DataCollectorParams{
		ZomatoId: zomatoId,
		ActorId:  actorId,
	}

	for _, field := range req.GetRequestedFields() {
		fieldEnum, exist := e.chatbotRequestedDataFieldNameMapping[field.GetFieldName()]
		if !exist {
			return &nugget.FetchDataResponse{
				Status: http.StatusBadRequest,
				Error:  fmt.Sprintf("unknown field %s", field.GetFieldName()),
			}, actorId
		}

		collector, err := e.dataCollectorFactory.GetImpl(fieldEnum)
		if err != nil {
			logger.Error(ctx, "failed to get collector", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err), zap.String("field", field.GetFieldName()))
			return &nugget.FetchDataResponse{
				Status: http.StatusInternalServerError,
				Error:  "internal server error",
			}, actorId
		}

		for _, v := range field.GetInputParameters() {
			if v.GetKey() == txnId {
				inputParams.TransactionId = v.GetValue().GetStringValue()
			}
			if v.GetKey() == afuKey {
				inputParams.AFU = v.GetValue().GetStringValue()
			}
			if v.GetKey() == tier {
				inputParams.QueriedTier = v.GetValue().GetStringValue()
			}
			if v.GetKey() == depositType {
				inputParams.DepositType = v.GetValue().GetStringValue()
			}
			if v.GetKey() == fiStoreRedemptionCategory {
				inputParams.RewardCategory = v.GetValue().GetStringValue()
			}
		}

		data, err := collector.CollectData(ctx, inputParams)
		if err != nil {
			logger.Error(ctx, "failed to collect data", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err), zap.String("field", field.GetFieldName()))
			return &nugget.FetchDataResponse{
				Status: http.StatusInternalServerError,
				Error:  fmt.Sprintf("failed to collect data for field %q", field.GetFieldName()),
			}, actorId
		}
		dataMap[field.GetFieldName()] = data
	}

	keyValuePairs, err := convertToKeyValuePairs(ctx, dataMap)
	if err != nil {
		logger.Error(ctx, "failed to convert data", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return &nugget.FetchDataResponse{
			Status: http.StatusInternalServerError,
			Error:  "failed to process data",
		}, actorId
	}

	return &nugget.FetchDataResponse{
		Status: http.StatusOK,
		Data:   keyValuePairs,
	}, actorId
}

func convertToKeyValuePairs(ctx context.Context, data map[string]any) ([]*nugget.KeyValuePair, error) {
	var keyValuePairs []*nugget.KeyValuePair

	for key, val := range data {
		var jsonCompatible interface{}

		// Marshal your custom type to JSON
		bytes, err := epifiprotojson.Marshal(val)
		if err != nil {
			logger.Error(ctx, "failed to marshal value to JSON", zap.String("key", key), zap.Any("value", val), zap.Error(err))
			return nil, err
		}

		// Unmarshal JSON into map/interface to ensure it's compatible with structpb
		if err := json.Unmarshal(bytes, &jsonCompatible); err != nil {
			logger.Error(ctx, "failed to unmarshal JSON to interface{}", zap.String("key", key), zap.ByteString("json", bytes), zap.Error(err))
			return nil, err
		}

		protoVal, err := structpb.NewValue(jsonCompatible)
		if err != nil {
			logger.Error(ctx, "failed to convert value to proto", zap.String("key", key), zap.Any("value", jsonCompatible), zap.Error(err))
			return nil, err
		}

		keyValuePairs = append(keyValuePairs, &nugget.KeyValuePair{
			Key:   key,
			Value: protoVal,
		})
	}

	return keyValuePairs, nil
}

func (e *NuggetService) isRequestValid(req *nugget.FetchDataRequest) error {
	var validations []string

	if len(req.GetRequestedFields()) == 0 {
		validations = append(validations, "at least one request field is required e.g (ACCOUNT_FREEZE_DETAILS, TRANSACTION_DETAILS)")
	}

	if len(validations) > 0 {
		return fmt.Errorf("please try again after fixing these: %s", strings.Join(validations, ", "))
	}

	return nil
}

func getKeyValuePairForAccountFreezeDummyResponse(val *genConf.NuggetAccountFreezeDummyDetails) map[string]any {
	return map[string]any{
		savingsAccountFreezeDetails: map[string]string{
			"account_status":          val.AccountStatus(),
			"form_status":             val.FormStatus(),
			"freeze_type":             val.FreezeType(),
			"form_id":                 val.FormId(),
			"processed_freeze_reason": val.ProcessedFreezeReason(),
			"form_expiry_date":        val.FormExpiryDate(),
			"lea_complaint_details":   val.LeaComplaintDetails(),
		},
	}
}

func getKeyValuePairForTransactionDummyResponse(val *genConf.NuggetTransactionDummyDetails) map[string]any {
	return map[string]any{
		transactionDetails: map[string]any{
			"created_at":       val.CreatedAt(),
			"error_code":       val.ErrorCode(),
			"executed_at":      val.ExecutedAt(),
			"p2p_p2m":          val.P2P_P2M(),
			"payment_protocol": val.PaymentProtocol(),
			"provenance":       val.Provenance(),
			"tags": func() []string {
				tags := val.Tags()
				if tags == "" {
					return []string{}
				}
				return strings.Split(tags, ",")
			}(),
			"transaction_amount":       val.TransactionAmount(),
			"transaction_status":       val.TransactionStatus(),
			"created_at_readable_time": val.CreatedAtReadableTime(),
		},
	}

}

// handleGenericMockUserResponse handles the unified mock response for different actors
func (e *NuggetService) handleGenericMockUserResponse(ctx context.Context, userId string) (resp *nugget.FetchDataResponse, mockExists bool) {

	mockDataStr := e.conf.Nugget().NuggetUserMockAPIProtoJsonResp().Get(userId)
	if mockDataStr == "" {
		return nil, false
	}

	// Parse the mock data JSON directly into KeyValuePair format
	var keyValuePairs []*nugget.KeyValuePair

	if err := json.Unmarshal([]byte(mockDataStr), &keyValuePairs); err != nil {
		logger.Error(ctx, "failed to unmarshal unified mock data", zap.String("userId", userId), zap.Error(err))
		return nil, false
	}

	logger.Info(ctx, "using unified mock response", zap.String("userId", userId), zap.Int("fields", len(keyValuePairs)))
	return &nugget.FetchDataResponse{
		Status: http.StatusOK,
		Data:   keyValuePairs,
	}, true
}
