package options

type DataCollectorParams struct {
	ActorId        string
	TransactionId  string
	ZomatoId       string
	QueriedTier    string
	AFU            string
	RewardCategory string
	DepositType    string
}

func (s *DataCollectorParams) GetTransactionId() string {
	if s != nil {
		return s.TransactionId
	}
	return ""
}

func (s *DataCollectorParams) GetActorId() string {
	if s != nil {
		return s.ActorId
	}
	return ""
}

func (s *DataCollectorParams) GetZomatoId() string {
	if s != nil {
		return s.ZomatoId
	}
	return ""
}

func (s *DataCollectorParams) GetQueriedTier() string {
	if s != nil {
		return s.QueriedTier
	}
	return ""
}

func (s *DataCollectorParams) GetAFU() string {
	if s != nil {
		return s.AFU
	}
	return ""
}

func (s *DataCollectorParams) GetRewardCategory() string {
	if s != nil {
		return s.RewardCategory
	}
	return ""
}

func (s *DataCollectorParams) GetDepositType() string {
	if s != nil {
		return s.DepositType
	}
	return ""
}
