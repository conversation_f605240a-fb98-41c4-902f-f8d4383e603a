package collectors

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	cardPb "github.com/epifi/gamma/api/card"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	cpMocks "github.com/epifi/gamma/api/card/provisioning/mocks"
	typesv2Pb "github.com/epifi/gamma/api/typesv2"

	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

const (
	testDebitCardActorId = "test-actor-123"
	testDebitCardId      = "test-card-456"
)

type mockDebitCardDependencies struct {
	mockCardProvisioningClient *cpMocks.MockCardProvisioningClient
}

func newDebitCardDataCollectorWithMocks(t *testing.T) (*DebitCardDeliveryDataCollector, *mockDebitCardDependencies) {
	ctrl := gomock.NewController(t)

	md := &mockDebitCardDependencies{
		mockCardProvisioningClient: cpMocks.NewMockCardProvisioningClient(ctrl),
	}

	dataCollector := NewDebitCardDeliveryDataCollector(md.mockCardProvisioningClient)

	t.Cleanup(func() {
		ctrl.Finish()
	})

	return dataCollector, md
}

func TestDebitCardDeliveryDataCollector_CollectData(t *testing.T) {
	// Initialize logger for testing
	logger.Init(cfg.TestEnv)

	type args struct {
		ctx         context.Context
		inputParams options.DataCollectorParams
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(md *mockDebitCardDependencies)
		want    *DebitCardDeliveryData
		wantErr error
	}{
		{
			name: "error: empty actor id",
			args: args{
				ctx:         context.Background(),
				inputParams: options.DataCollectorParams{},
			},
			mocks:   func(md *mockDebitCardDependencies) {},
			want:    nil,
			wantErr: errors.New("actorId is required"),
		},
		{
			name: "error: FetchCards RPC failure",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testDebitCardActorId,
				},
			},
			mocks: func(md *mockDebitCardDependencies) {
				md.mockCardProvisioningClient.EXPECT().FetchCards(
					context.Background(),
					&cpPb.FetchCardsRequest{
						Actor:     &typesv2Pb.Actor{Id: testDebitCardActorId},
						CardTypes: []cardPb.CardType{cardPb.CardType_DEBIT},
						Limit:     1,
						SortedBy:  cardPb.CardFieldMask_CARD_CREATED_AT,
					},
				).Return(nil, errors.New("rpc error"))
			},
			want:    nil,
			wantErr: errors.New("failed to fetch cards: rpc error"),
		},
		{
			name: "error: no debit cards found for actor",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testDebitCardActorId,
				},
			},
			mocks: func(md *mockDebitCardDependencies) {
				md.mockCardProvisioningClient.EXPECT().FetchCards(
					context.Background(),
					&cpPb.FetchCardsRequest{
						Actor:     &typesv2Pb.Actor{Id: testDebitCardActorId},
						CardTypes: []cardPb.CardType{cardPb.CardType_DEBIT},
						Limit:     1,
						SortedBy:  cardPb.CardFieldMask_CARD_CREATED_AT,
					},
				).Return(&cpPb.FetchCardsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			want:    nil,
			wantErr: errors.New("failed to fetch cards"),
		},

		{
			name: "success: basic card with no dispatch request",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testDebitCardActorId,
				},
			},
			mocks: func(md *mockDebitCardDependencies) {
				createdAt := &timestamppb.Timestamp{Seconds: 1609459200} // 2021-01-01 00:00:00 UTC

				// Mock FetchCards
				md.mockCardProvisioningClient.EXPECT().FetchCards(
					context.Background(),
					&cpPb.FetchCardsRequest{
						Actor:     &typesv2Pb.Actor{Id: testDebitCardActorId},
						CardTypes: []cardPb.CardType{cardPb.CardType_DEBIT},
						Limit:     1,
						SortedBy:  cardPb.CardFieldMask_CARD_CREATED_AT,
					},
				).Return(&cpPb.FetchCardsResponse{
					Status: rpc.StatusOk(),
					Cards: []*cardPb.Card{
						{
							Id:        testDebitCardId,
							Type:      cardPb.CardType_DEBIT,
							Form:      cardPb.CardForm_DIGITAL,
							State:     cardPb.CardState_ACTIVATED,
							CreatedAt: createdAt,
						},
					},
				}, nil)

				// Mock FetchPhysicalCardDispatchRequests - no dispatch requests
				md.mockCardProvisioningClient.EXPECT().FetchPhysicalCardDispatchRequests(
					context.Background(),
					&cpPb.FetchPhysicalCardDispatchRequestsRequest{
						CardIdentifier: &cpPb.CardIdentifier{
							Identifier: &cpPb.CardIdentifier_CardId{CardId: testDebitCardId},
						},
					},
				).Return(&cpPb.FetchPhysicalCardDispatchRequestsResponse{
					Status:                       rpc.StatusOk(),
					PhysicalCardDispatchRequests: []*cpPb.PhysicalCardDispatchRequest{},
				}, nil)

				// Mock GetCardShipmentTrackingDetails - record not found
				md.mockCardProvisioningClient.EXPECT().GetCardShipmentTrackingDetails(
					context.Background(),
					&cpPb.GetCardShipmentTrackingDetailsRequest{
						CardId: testDebitCardId,
					},
				).Return(&cpPb.GetCardShipmentTrackingDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			want: &DebitCardDeliveryData{
				OrderedAt:     "", // empty when no dispatch request
				PaymentStatus: "", // empty when no dispatch request
				CardForm:      "DIGITAL",
				AwbNumber:     "",
				DeliveryState: "",
				Carrier:       "",
				CardState:     "ACTIVATED",
			},
			wantErr: nil,
		},
		{
			name: "success: payment pending based on dispatch request state",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testDebitCardActorId,
				},
			},
			mocks: func(md *mockDebitCardDependencies) {
				createdAt := &timestamppb.Timestamp{Seconds: 1609459200}         // 2021-01-01 00:00:00 UTC
				dispatchCreatedAt := &timestamppb.Timestamp{Seconds: 1612137600} // 2021-02-01 00:00:00 UTC

				// Mock FetchCards
				md.mockCardProvisioningClient.EXPECT().FetchCards(
					context.Background(),
					&cpPb.FetchCardsRequest{
						Actor:     &typesv2Pb.Actor{Id: testDebitCardActorId},
						CardTypes: []cardPb.CardType{cardPb.CardType_DEBIT},
						Limit:     1,
						SortedBy:  cardPb.CardFieldMask_CARD_CREATED_AT,
					},
				).Return(&cpPb.FetchCardsResponse{
					Status: rpc.StatusOk(),
					Cards: []*cardPb.Card{
						{
							Id:        testDebitCardId,
							Type:      cardPb.CardType_DEBIT,
							Form:      cardPb.CardForm_PHYSICAL,
							State:     cardPb.CardState_CREATED,
							CreatedAt: createdAt,
						},
					},
				}, nil)

				// Mock FetchPhysicalCardDispatchRequests - with pending payment
				md.mockCardProvisioningClient.EXPECT().FetchPhysicalCardDispatchRequests(
					context.Background(),
					&cpPb.FetchPhysicalCardDispatchRequestsRequest{
						CardIdentifier: &cpPb.CardIdentifier{
							Identifier: &cpPb.CardIdentifier_CardId{CardId: testDebitCardId},
						},
					},
				).Return(&cpPb.FetchPhysicalCardDispatchRequestsResponse{
					Status: rpc.StatusOk(),
					PhysicalCardDispatchRequests: []*cpPb.PhysicalCardDispatchRequest{
						{
							Id:           "dispatch-123",
							CardId:       testDebitCardId,
							State:        cpPb.RequestState_QUEUED,
							CurrentStage: cpPb.DCRequestStage_DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS,
							CreatedAt:    dispatchCreatedAt,
						},
					},
				}, nil)

				// Mock GetCardShipmentTrackingDetails
				md.mockCardProvisioningClient.EXPECT().GetCardShipmentTrackingDetails(
					context.Background(),
					&cpPb.GetCardShipmentTrackingDetailsRequest{
						CardId: testDebitCardId,
					},
				).Return(&cpPb.GetCardShipmentTrackingDetailsResponse{
					Status: rpc.StatusOk(),
					TrackingDetails: &cpPb.CardTrackingRequest{
						CardId:        testDebitCardId,
						Awb:           "AT056256126IN",
						Carrier:       "DELHIVERY",
						DeliveryState: cpPb.CardTrackingDeliveryState_IN_TRANSIT,
						UploadedAt:    &timestamppb.Timestamp{Seconds: 1614729600}, // 2021-03-03 00:00:00 UTC
					},
				}, nil)
			},
			want: &DebitCardDeliveryData{
				OrderedAt:     "1612137600", // dispatch CreatedAt timestamp
				PaymentStatus: "PENDING",
				CardForm:      "PHYSICAL",
				AwbNumber:     "AT056256126IN",
				DeliveryState: "IN_TRANSIT",
				Carrier:       "DELHIVERY",
				CardState:     "CREATED",
			},
			wantErr: nil,
		},
		{
			name: "success: payment success with manual intervention but different stage",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testDebitCardActorId,
				},
			},
			mocks: func(md *mockDebitCardDependencies) {
				createdAt := &timestamppb.Timestamp{Seconds: 1609459200}         // 2021-01-01 00:00:00 UTC
				dispatchCreatedAt := &timestamppb.Timestamp{Seconds: 1612137600} // 2021-02-01 00:00:00 UTC

				// Mock FetchCards
				md.mockCardProvisioningClient.EXPECT().FetchCards(
					context.Background(),
					&cpPb.FetchCardsRequest{
						Actor:     &typesv2Pb.Actor{Id: testDebitCardActorId},
						CardTypes: []cardPb.CardType{cardPb.CardType_DEBIT},
						Limit:     1,
						SortedBy:  cardPb.CardFieldMask_CARD_CREATED_AT,
					},
				).Return(&cpPb.FetchCardsResponse{
					Status: rpc.StatusOk(),
					Cards: []*cardPb.Card{
						{
							Id:        testDebitCardId,
							Type:      cardPb.CardType_DEBIT,
							Form:      cardPb.CardForm_PHYSICAL,
							State:     cardPb.CardState_ACTIVATED,
							CreatedAt: createdAt,
						},
					},
				}, nil)

				// Mock FetchPhysicalCardDispatchRequests - manual intervention but different stage
				md.mockCardProvisioningClient.EXPECT().FetchPhysicalCardDispatchRequests(
					context.Background(),
					&cpPb.FetchPhysicalCardDispatchRequestsRequest{
						CardIdentifier: &cpPb.CardIdentifier{
							Identifier: &cpPb.CardIdentifier_CardId{CardId: testDebitCardId},
						},
					},
				).Return(&cpPb.FetchPhysicalCardDispatchRequestsResponse{
					Status: rpc.StatusOk(),
					PhysicalCardDispatchRequests: []*cpPb.PhysicalCardDispatchRequest{
						{
							Id:           "dispatch-123",
							CardId:       testDebitCardId,
							State:        cpPb.RequestState_MANUAL_INTERVENTION,
							CurrentStage: cpPb.DCRequestStage_DC_REQUEST_STAGE_DISPATCH_PHYSICAL_CARD,
							CreatedAt:    dispatchCreatedAt,
						},
					},
				}, nil)

				// Mock GetCardShipmentTrackingDetails
				md.mockCardProvisioningClient.EXPECT().GetCardShipmentTrackingDetails(
					context.Background(),
					&cpPb.GetCardShipmentTrackingDetailsRequest{
						CardId: testDebitCardId,
					},
				).Return(&cpPb.GetCardShipmentTrackingDetailsResponse{
					Status: rpc.StatusOk(),
					TrackingDetails: &cpPb.CardTrackingRequest{
						CardId:        testDebitCardId,
						Awb:           "BD123456789IN",
						Carrier:       "BLUEDART",
						DeliveryState: cpPb.CardTrackingDeliveryState_DELIVERED,
						UploadedAt:    &timestamppb.Timestamp{Seconds: 1614729600}, // 2021-03-03 00:00:00 UTC
					},
				}, nil)
			},
			want: &DebitCardDeliveryData{
				OrderedAt:     "1612137600", // dispatch CreatedAt timestamp
				PaymentStatus: "SUCCESS",
				CardForm:      "PHYSICAL",
				AwbNumber:     "BD123456789IN",
				DeliveryState: "DELIVERED",
				Carrier:       "BLUEDART",
				CardState:     "ACTIVATED",
			},
			wantErr: nil,
		},
		{
			name: "success: FetchPhysicalCardDispatchRequests record not found",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testDebitCardActorId,
				},
			},
			mocks: func(md *mockDebitCardDependencies) {
				createdAt := &timestamppb.Timestamp{Seconds: 1609459200} // 2021-01-01 00:00:00 UTC

				// Mock FetchCards
				md.mockCardProvisioningClient.EXPECT().FetchCards(
					context.Background(),
					gomock.Any(),
				).Return(&cpPb.FetchCardsResponse{
					Status: rpc.StatusOk(),
					Cards: []*cardPb.Card{
						{
							Id:        testDebitCardId,
							Type:      cardPb.CardType_DEBIT,
							Form:      cardPb.CardForm_DIGITAL,
							State:     cardPb.CardState_ACTIVATED,
							CreatedAt: createdAt,
						},
					},
				}, nil)

				// Mock FetchPhysicalCardDispatchRequests - record not found
				md.mockCardProvisioningClient.EXPECT().FetchPhysicalCardDispatchRequests(
					context.Background(),
					gomock.Any(),
				).Return(&cpPb.FetchPhysicalCardDispatchRequestsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				// Mock GetCardShipmentTrackingDetails - record not found
				md.mockCardProvisioningClient.EXPECT().GetCardShipmentTrackingDetails(
					context.Background(),
					gomock.Any(),
				).Return(&cpPb.GetCardShipmentTrackingDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			want: &DebitCardDeliveryData{
				OrderedAt:     "", // empty when record not found
				PaymentStatus: "", // empty when record not found
				CardForm:      "DIGITAL",
				AwbNumber:     "",
				DeliveryState: "",
				Carrier:       "",
				CardState:     "ACTIVATED",
			},
			wantErr: nil,
		},
		{
			name: "success: FetchPhysicalCardDispatchRequests actual error but handles gracefully",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testDebitCardActorId,
				},
			},
			mocks: func(md *mockDebitCardDependencies) {
				createdAt := &timestamppb.Timestamp{Seconds: 1609459200} // 2021-01-01 00:00:00 UTC

				// Mock FetchCards
				md.mockCardProvisioningClient.EXPECT().FetchCards(
					context.Background(),
					gomock.Any(),
				).Return(&cpPb.FetchCardsResponse{
					Status: rpc.StatusOk(),
					Cards: []*cardPb.Card{
						{
							Id:        testDebitCardId,
							Type:      cardPb.CardType_DEBIT,
							Form:      cardPb.CardForm_DIGITAL,
							State:     cardPb.CardState_ACTIVATED,
							CreatedAt: createdAt,
						},
					},
				}, nil)

				// Mock FetchPhysicalCardDispatchRequests - actual error
				md.mockCardProvisioningClient.EXPECT().FetchPhysicalCardDispatchRequests(
					context.Background(),
					gomock.Any(),
				).Return(nil, errors.New("dispatch rpc error"))

				// Mock GetCardShipmentTrackingDetails - record not found
				md.mockCardProvisioningClient.EXPECT().GetCardShipmentTrackingDetails(
					context.Background(),
					gomock.Any(),
				).Return(&cpPb.GetCardShipmentTrackingDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			want: &DebitCardDeliveryData{
				OrderedAt:     "", // empty when actual error
				PaymentStatus: "", // empty when actual error
				CardForm:      "DIGITAL",
				AwbNumber:     "",
				DeliveryState: "",
				Carrier:       "",
				CardState:     "ACTIVATED",
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dataCollector, md := newDebitCardDataCollectorWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(md)
			}
			got, err := dataCollector.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.wantErr != nil {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr.Error())
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				require.NotNil(t, got)

				// Cast to the expected type
				gotData, ok := got.(*DebitCardDeliveryData)
				require.True(t, ok, "Expected *DebitCardDeliveryData type")

				// Compare the entire response
				require.Equal(t, tt.want, gotData)
			}
		})
	}
}
