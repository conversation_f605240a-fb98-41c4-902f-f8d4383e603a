package collectors

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type TierDataCollector struct {
	tieringClient tieringPb.TieringClient
}

func NewTierDataCollector(
	tieringClient tieringPb.TieringClient,
) *TierDataCollector {
	return &TierDataCollector{
		tieringClient: tieringClient,
	}
}

func (c *TierDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	resp, err := c.tieringClient.GetTieringPitchV2(ctx, &tieringPb.GetTieringPitchV2Request{
		ActorId: actorId,
	})

	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error calling tiering service", zap.String(logger.ACTOR_ID, actorId), zap.Error(te))
		return nil, te
	}

	currentTier := resp.GetCurrentTier()

	// Check if tier is unspecified
	if currentTier == external.Tier_TIER_UNSPECIFIED {
		return nil, errors.New("invalid current tier")
	}

	tierData := &TierBotData{
		CurrentTier: currentTier.String(),
	}

	// Determine if user was downgraded
	// If lastDowngradeDetails is populated and the toTier matches current tier,
	// then user came to current tier from a downgrade
	lastDowngradeDetails := resp.GetLastDowngradeDetails()

	if lastDowngradeDetails != nil && lastDowngradeDetails.GetToTier() == currentTier {
		criteriaType := lastDowngradeDetails.GetLatestCriteriaOptionType()

		// Check if criteria option type is unspecified
		if criteriaType == enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED {
			return nil, errors.New("invalid downgrade reason")
		}

		tierData.WasUserDowngraded = "true"
		tierData.ReasonForDowngrade = criteriaType.String()
	} else {
		tierData.WasUserDowngraded = "false"
	}

	return tierData, nil
}
