package collectors

import (
	"context"
	"fmt"
	"strconv"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type ChequebookDataCollector struct {
	alfredClient alfredPb.AlfredClient
}

func NewChequebookDataCollector(
	alfredClient alfredPb.AlfredClient,
) *ChequebookDataCollector {
	return &ChequebookDataCollector{
		alfredClient: alfredClient,
	}
}

func (c *ChequebookDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	// Call GetAllRequestStatusDetails with page size 1 to get the most recent chequebook request
	resp, err := c.alfredClient.GetAllRequestStatusDetails(ctx, &alfredPb.GetAllRequestStatusDetailsRequest{
		Filters: &alfredPb.Filters{
			ActorId:      actorId,
			RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK},
			StatusList: []alfredPb.Status{
				alfredPb.Status_STATUS_IN_PROGRESS,
				alfredPb.Status_STATUS_SUCCESS,
				alfredPb.Status_STATUS_FAILED,
				alfredPb.Status_STATUS_STUCK,
				alfredPb.Status_STATUS_CREATED,
			},
		},
		PageContext: &rpcPb.PageContextRequest{
			PageSize: 1, // Only get the most recent request
		},
		SortOrder: alfredPb.SortOrder_SORT_ORDER_DESC, // Most recent first
	})

	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error calling GetAllRequestStatusDetails", zap.String(logger.ACTOR_ID, actorId), zap.Error(rpcErr))
		return nil, fmt.Errorf("failed to get chequebook request status details for actor %s: %w", actorId, rpcErr)
	}

	serviceRequests := resp.GetServiceRequestList()
	if len(serviceRequests) == 0 {
		return &ChequebookRequestData{
			HasOrdered: "false",
		}, nil
	}

	// Process the most recent request (zeroth element)
	mostRecentRequest := serviceRequests[0]

	chequebookData := &ChequebookRequestData{
		HasOrdered: "true",
	}

	// Extract order details from chequebook metadata
	if details := mostRecentRequest.GetDetails(); details != nil {
		if chequebookMetadata := details.GetChequebookMetadata(); chequebookMetadata != nil {
			// Extract ordered_at from metadata
			if orderedAt := chequebookMetadata.GetOrderedAt(); orderedAt != nil {
				chequebookData.OrderedAt = strconv.FormatInt(orderedAt.GetSeconds(), 10)
			}

			// Extract tracking ID as AWB number
			chequebookData.AwbNumber = chequebookMetadata.GetTrackingId()
		}
	}

	chequebookData.OrderStatus = mostRecentRequest.GetStatus().String()

	return chequebookData, nil
}
