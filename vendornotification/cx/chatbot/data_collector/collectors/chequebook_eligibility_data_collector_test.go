package collectors

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	alfredPb "github.com/epifi/gamma/api/alfred"
	alfredMocks "github.com/epifi/gamma/api/alfred/mocks"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

const (
	testChequeBookActorId = "test-actor-123"
)

type chequeBookMockDependencies struct {
	mockAlfredClient *alfredMocks.MockAlfredClient
}

func newChequeBookEligibilityDataCollectorWithMocks(t *testing.T) (*ChequeBookEligibilityDataCollector, *chequeBookMockDependencies) {
	ctrl := gomock.NewController(t)

	md := &chequeBookMockDependencies{
		mockAlfredClient: alfredMocks.NewMockAlfredClient(ctrl),
	}

	dataCollector := NewChequeBookEligibilityDataCollector(md.mockAlfredClient)

	t.Cleanup(func() {
		ctrl.Finish()
	})

	return dataCollector, md
}

func TestChequeBookEligibilityDataCollector_CollectData(t *testing.T) {
	// Initialize logger for testing
	logger.Init(cfg.TestEnv)

	type args struct {
		ctx         context.Context
		inputParams options.DataCollectorParams
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(md *chequeBookMockDependencies)
		want    *ChequeBookEligibilityData
		wantErr error
	}{
		{
			name: "error: empty actor id",
			args: args{
				ctx:         context.Background(),
				inputParams: options.DataCollectorParams{},
			},
			mocks:   func(md *chequeBookMockDependencies) {},
			want:    nil,
			wantErr: errors.New("actorId is required"),
		},
		{
			name: "error: rpc call failed",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testChequeBookActorId,
				},
			},
			mocks: func(md *chequeBookMockDependencies) {
				md.mockAlfredClient.EXPECT().IsEligibleForRequest(
					context.Background(),
					&alfredPb.IsEligibleForRequestRequest{
						ActorId:     testChequeBookActorId,
						RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					},
				).Return(nil, errors.New("rpc error"))
			},
			want:    nil,
			wantErr: errors.New("rpc error"),
		},
		{
			name: "error: rpc response status error",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testChequeBookActorId,
				},
			},
			mocks: func(md *chequeBookMockDependencies) {
				md.mockAlfredClient.EXPECT().IsEligibleForRequest(
					context.Background(),
					&alfredPb.IsEligibleForRequestRequest{
						ActorId:     testChequeBookActorId,
						RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					},
				).Return(&alfredPb.IsEligibleForRequestResponse{
					Status: &rpc.Status{
						Code:         400,
						ShortMessage: "bad request",
					},
					IsEligible: false,
				}, nil)
			},
			want:    nil,
			wantErr: errors.New("bad request"),
		},
		{
			name: "success: actor is eligible for chequebook order",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testChequeBookActorId,
				},
			},
			mocks: func(md *chequeBookMockDependencies) {
				md.mockAlfredClient.EXPECT().IsEligibleForRequest(
					context.Background(),
					&alfredPb.IsEligibleForRequestRequest{
						ActorId:     testChequeBookActorId,
						RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					},
				).Return(&alfredPb.IsEligibleForRequestResponse{
					Status: &rpc.Status{
						Code:         0, // 0 is success code
						ShortMessage: "Success",
					},
					IsEligible: true,
				}, nil)
			},
			want: &ChequeBookEligibilityData{
				IsEligible: "true",
			},
			wantErr: nil,
		},
		{
			name: "success: actor is not eligible with full kyc missing reason",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testChequeBookActorId,
				},
			},
			mocks: func(md *chequeBookMockDependencies) {
				md.mockAlfredClient.EXPECT().IsEligibleForRequest(
					context.Background(),
					&alfredPb.IsEligibleForRequestRequest{
						ActorId:     testChequeBookActorId,
						RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					},
				).Return(&alfredPb.IsEligibleForRequestResponse{
					Status: &rpc.Status{
						Code:         0, // 0 is success code
						ShortMessage: "Success",
					},
					IsEligible:    false,
					FailureReason: alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_FULL_KYC_MISSING,
				}, nil)
			},
			want: &ChequeBookEligibilityData{
				IsEligible:             "false",
				ReasonForIneligibility: "ELIGIBILITY_FAILURE_REASON_FULL_KYC_MISSING",
			},
			wantErr: nil,
		},
		{
			name: "success: actor is not eligible with insufficient balance reason",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testChequeBookActorId,
				},
			},
			mocks: func(md *chequeBookMockDependencies) {
				md.mockAlfredClient.EXPECT().IsEligibleForRequest(
					context.Background(),
					&alfredPb.IsEligibleForRequestRequest{
						ActorId:     testChequeBookActorId,
						RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					},
				).Return(&alfredPb.IsEligibleForRequestResponse{
					Status: &rpc.Status{
						Code:         0, // 0 is success code
						ShortMessage: "Success",
					},
					IsEligible:    false,
					FailureReason: alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_INSUFFICIENT_BALANCE,
				}, nil)
			},
			want: &ChequeBookEligibilityData{
				IsEligible:             "false",
				ReasonForIneligibility: "ELIGIBILITY_FAILURE_REASON_INSUFFICIENT_BALANCE",
			},
			wantErr: nil,
		},
		{
			name: "success: actor is not eligible with missing signature reason",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testChequeBookActorId,
				},
			},
			mocks: func(md *chequeBookMockDependencies) {
				md.mockAlfredClient.EXPECT().IsEligibleForRequest(
					context.Background(),
					&alfredPb.IsEligibleForRequestRequest{
						ActorId:     testChequeBookActorId,
						RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					},
				).Return(&alfredPb.IsEligibleForRequestResponse{
					Status: &rpc.Status{
						Code:         0, // 0 is success code
						ShortMessage: "Success",
					},
					IsEligible:    false,
					FailureReason: alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_MISSING_SIGNATURE,
				}, nil)
			},
			want: &ChequeBookEligibilityData{
				IsEligible:             "false",
				ReasonForIneligibility: "ELIGIBILITY_FAILURE_REASON_MISSING_SIGNATURE",
			},
			wantErr: nil,
		},
		{
			name: "success: actor is not eligible with unspecified reason",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testChequeBookActorId,
				},
			},
			mocks: func(md *chequeBookMockDependencies) {
				md.mockAlfredClient.EXPECT().IsEligibleForRequest(
					context.Background(),
					&alfredPb.IsEligibleForRequestRequest{
						ActorId:     testChequeBookActorId,
						RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					},
				).Return(&alfredPb.IsEligibleForRequestResponse{
					Status: &rpc.Status{
						Code:         0, // 0 is success code
						ShortMessage: "Success",
					},
					IsEligible:    false,
					FailureReason: alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED,
				}, nil)
			},
			want: &ChequeBookEligibilityData{
				IsEligible:             "false",
				ReasonForIneligibility: "ELIGIBILITY_FAILURE_REASON_UNSPECIFIED",
			},
			wantErr: nil,
		},
		{
			name: "success: actor is not eligible but no failure reason is provided (defaults to empty string)",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testChequeBookActorId,
				},
			},
			mocks: func(md *chequeBookMockDependencies) {
				md.mockAlfredClient.EXPECT().IsEligibleForRequest(
					context.Background(),
					&alfredPb.IsEligibleForRequestRequest{
						ActorId:     testChequeBookActorId,
						RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					},
				).Return(&alfredPb.IsEligibleForRequestResponse{
					Status: &rpc.Status{
						Code:         0, // 0 is success code
						ShortMessage: "Success",
					},
					IsEligible: false,
					// FailureReason is not set, defaults to UNSPECIFIED
				}, nil)
			},
			want: &ChequeBookEligibilityData{
				IsEligible:             "false",
				ReasonForIneligibility: "ELIGIBILITY_FAILURE_REASON_UNSPECIFIED",
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dataCollector, md := newChequeBookEligibilityDataCollectorWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(md)
			}
			got, err := dataCollector.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.wantErr != nil {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr.Error())
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.want, got)
			}
		})
	}
}
