package collectors

import (
	"context"
	"strconv"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/pkg/tiering"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type TieringEligibilityDataCollector struct {
	tieringClient tieringPb.TieringClient
}

func NewTieringEligibilityDataCollector(
	tieringClient tieringPb.TieringClient,
) *TieringEligibilityDataCollector {
	return &TieringEligibilityDataCollector{
		tieringClient: tieringClient,
	}
}

// TieringEligibilityData represents the response structure for tiering eligibility
type TieringEligibilityData struct {
	IsEligible   string `json:"is_eligible"`
	IsInCooldown string `json:"is_in_cooldown"`
}

var (
	// Map from string tier name to protobuf tier enum
	stringToTierEnumMap = map[string]tieringExtPb.Tier{
		"Plus":     tieringExtPb.Tier_TIER_FI_PLUS,
		"Infinite": tieringExtPb.Tier_TIER_FI_INFINITE,
		"Prime":    tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3,
		// Evasive mapping to avoid errors , the real meaning of request being 'Salary' is not basically the salary tier
		// but instead is querying for eligibility for any salary tier ( Salary or Salary_Basic ) . Same is handled in the logic as well .
		"Salary": tieringExtPb.Tier_TIER_FI_SALARY,
	}
)

// CollectData
// IMPORTANT: This data collector checks tiering eligibility for a specific tier.
// The tier parameter is passed via input_parameters with key "TIER" and possible values: "Plus", "Infinite", "Prime", "Salary"
// It uses the tiering service's EvaluateTierForActor method to get the user's evaluated tier and compares it with the requested tier.
// User is eligible if the requested tier value is smaller than or equal to the evaluated tier value.
//
// SPECIAL HANDLING FOR SALARY TIER:
// - When the requested tier is "Salary" : check is the user eligible for any salary tier ( Salary or Salary Basic )
func (c *TieringEligibilityDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	// Extract tier parameter from input params
	tier := inputParams.GetQueriedTier()
	if tier == "" {
		return nil, errors.New("TIER parameter is required. Valid tiers are: Plus, Infinite, Prime, Salary")
	}

	requestedTierEnum, exists := stringToTierEnumMap[tier]
	if !exists {
		return nil, errors.Errorf("invalid tier requested: %s. Valid tiers are: Plus, Infinite, Prime, Salary", tier)
	}

	// Get tiering pitch details to check for cooldown status
	pitchResp, err := c.tieringClient.GetTieringPitchV2(ctx, &tieringPb.GetTieringPitchV2Request{
		ActorId: actorId,
	})

	if rErr := epifigrpc.RPCError(pitchResp, err); rErr != nil {
		logger.Error(ctx, "error calling GetTieringPitchV2",
			zap.String(logger.ACTOR_ID, actorId),
			zap.Error(rErr))
		return nil, errors.Wrap(rErr, "failed to get tiering pitch details")
	}

	currentTier := pitchResp.GetCurrentTier()
	movementDetailsList := pitchResp.GetMovementDetailsList()

	// Check if user is in cooldown by examining movement details
	isInCooldown, err := tiering.IsUserInCoolOff(currentTier, movementDetailsList)
	if err != nil {
		return nil, errors.Wrap(err, "error checking if user is in-cooldown")
	}

	// Cooldown eligibility check: if user is in cooldown and requested tier is not current tier, not eligible
	if isInCooldown && requestedTierEnum != currentTier {
		tieringEligibilityData := &TieringEligibilityData{
			IsEligible:   "false",
			IsInCooldown: strconv.FormatBool(isInCooldown),
		}
		return tieringEligibilityData, nil
	}

	// Evaluate user's tier using current active criteria
	evaluateResp, err := c.tieringClient.EvaluateTierForActor(ctx, &tieringPb.EvaluateTierForActorRequest{
		ActorId: actorId,
	})

	if rErr := epifigrpc.RPCError(evaluateResp, err); rErr != nil {
		logger.Error(ctx, "error calling EvaluateTierForActor",
			zap.String(logger.ACTOR_ID, actorId),
			zap.Error(rErr))
		return nil, errors.Wrap(rErr, "failed to evaluate user tier")
	}

	evaluatedTier := evaluateResp.GetEvaluatedTier()
	if evaluatedTier == tieringExtPb.Tier_TIER_UNSPECIFIED {
		return nil, errors.New("user has unspecified evaluated tier")
	}

	// Special handling for Salary tier requests: check if evaluated tier is any salary tier
	var isEligible bool
	if tier == "Salary" {
		isEligible = isEligibleForSalaryTier(evaluatedTier)
	} else {
		// Check eligibility: user is eligible if requested tier is not higher than evaluated tier
		// If requested tier is higher than evaluated tier, user is not eligible
		isEligible = tiering.IsTierLowerThanOrEqualToCurrentEvaluatedTier(requestedTierEnum, evaluatedTier)
	}

	tieringEligibilityData := &TieringEligibilityData{
		IsEligible:   strconv.FormatBool(isEligible),
		IsInCooldown: strconv.FormatBool(isInCooldown),
	}

	return tieringEligibilityData, nil
}

// isEligibleForSalaryTier checks if a user with evaluatedTier is eligible for "Salary" tier request
// Special logic: Salary tier request is eligible for both TIER_FI_SALARY and TIER_FI_SALARY_BASIC
func isEligibleForSalaryTier(evaluatedTier tieringExtPb.Tier) bool {
	return evaluatedTier.IsSalaryTier()
}
