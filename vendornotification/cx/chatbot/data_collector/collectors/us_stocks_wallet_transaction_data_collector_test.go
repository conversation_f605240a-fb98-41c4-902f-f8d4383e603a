package collectors

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	googleMoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	beMoney "github.com/epifi/be-common/pkg/money"

	usstocksPb "github.com/epifi/gamma/api/usstocks"
	ussOrderPb "github.com/epifi/gamma/api/usstocks/order"
	ussOrderMocks "github.com/epifi/gamma/api/usstocks/order/mocks"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

const testActorIdWalletTxn = "test-actor-id-us-stocks-wallet-txn"

type mockDependenciesWalletTxn struct {
	orderClient *ussOrderMocks.MockOrderManagerClient
}

func newWalletTxnCollectorWithMocks(t *testing.T) (*UsStocksWalletTransactionDataCollector, *mockDependenciesWalletTxn) {
	ctrl := gomock.NewController(t)
	orderClient := ussOrderMocks.NewMockOrderManagerClient(ctrl)

	collector := NewUsStocksWalletTransactionDataCollector(orderClient)

	return collector, &mockDependenciesWalletTxn{
		orderClient: orderClient,
	}
}

func createWalletOrderResponse(actorId string, orderType usstocksPb.WalletOrderType, amount *beMoney.Money, status usstocksPb.WalletOrderStatus, createdAt time.Time) *ussOrderPb.GetWalletOrdersResponse {
	return &ussOrderPb.GetWalletOrdersResponse{
		Status: rpc.StatusOk(),
		Orders: []*ussOrderPb.WalletOrder{
			{
				Id:              "order-123",
				ActorId:         actorId,
				OrderType:       orderType,
				Status:          status,
				AmountRequested: amount.GetPb(),
				CreatedAt:       timestamppb.New(createdAt),
			},
		},
	}
}

func TestUsStocksWalletTransactionDataCollector_CollectData(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name string
		args struct {
			ctx         context.Context
			inputParams options.DataCollectorParams
		}
		mocks   func(md *mockDependenciesWalletTxn)
		want    *UsStocksWalletTransactionData
		wantErr bool
	}{
		{
			name: "success: both add and withdrawal funds",
			args: struct {
				ctx         context.Context
				inputParams options.DataCollectorParams
			}{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdWalletTxn,
				},
			},
			mocks: func(md *mockDependenciesWalletTxn) {
				md.orderClient.EXPECT().GetWalletOrders(
					context.Background(),
					&ussOrderPb.GetWalletOrdersRequest{
						ActorId: testActorIdWalletTxn,
						OrderType: []usstocksPb.WalletOrderType{
							usstocksPb.WalletOrderType_WALLET_ORDER_TYPE_ADD_FUNDS,
						},
						PageContext: &rpc.PageContextRequest{
							PageSize: 1,
						},
					},
				).Return(createWalletOrderResponse(
					testActorIdWalletTxn,
					usstocksPb.WalletOrderType_WALLET_ORDER_TYPE_ADD_FUNDS,
					beMoney.NewMoney(&googleMoney.Money{CurrencyCode: "USD", Units: 100, Nanos: 0}),
					usstocksPb.WalletOrderStatus_WALLET_ORDER_STATUS_SUCCESS,
					time.Date(2024, 1, 15, 10, 30, 0, 0, time.UTC),
				), nil)

				md.orderClient.EXPECT().GetWalletOrders(
					context.Background(),
					&ussOrderPb.GetWalletOrdersRequest{
						ActorId: testActorIdWalletTxn,
						OrderType: []usstocksPb.WalletOrderType{
							usstocksPb.WalletOrderType_WALLET_ORDER_TYPE_WITHDRAW_FUNDS,
						},
						PageContext: &rpc.PageContextRequest{
							PageSize: 1,
						},
					},
				).Return(createWalletOrderResponse(
					testActorIdWalletTxn,
					usstocksPb.WalletOrderType_WALLET_ORDER_TYPE_WITHDRAW_FUNDS,
					beMoney.NewMoney(&googleMoney.Money{CurrencyCode: "USD", Units: 50, Nanos: 0}),
					usstocksPb.WalletOrderStatus_WALLET_ORDER_STATUS_SUCCESS,
					time.Date(2024, 1, 14, 14, 20, 0, 0, time.UTC),
				), nil)
			},
			want: &UsStocksWalletTransactionData{
				AddFunds: &WalletOrderInfo{
					Status:    "WALLET_ORDER_STATUS_SUCCESS",
					Amount:    "$100.00",
					CreatedAt: "2024-01-15T10:30:00Z",
				},
				WithdrawFunds: &WalletOrderInfo{
					Status:    "WALLET_ORDER_STATUS_SUCCESS",
					Amount:    "$50.00",
					CreatedAt: "2024-01-14T14:20:00Z",
				},
			},
			wantErr: false,
		},
		{
			name: "success: no orders found",
			args: struct {
				ctx         context.Context
				inputParams options.DataCollectorParams
			}{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdWalletTxn,
				},
			},
			mocks: func(md *mockDependenciesWalletTxn) {
				md.orderClient.EXPECT().GetWalletOrders(
					context.Background(),
					&ussOrderPb.GetWalletOrdersRequest{
						ActorId: testActorIdWalletTxn,
						OrderType: []usstocksPb.WalletOrderType{
							usstocksPb.WalletOrderType_WALLET_ORDER_TYPE_ADD_FUNDS,
						},
						PageContext: &rpc.PageContextRequest{
							PageSize: 1,
						},
					},
				).Return(&ussOrderPb.GetWalletOrdersResponse{
					Status: rpc.StatusOk(),
					Orders: []*ussOrderPb.WalletOrder{},
				}, nil)

				md.orderClient.EXPECT().GetWalletOrders(
					context.Background(),
					&ussOrderPb.GetWalletOrdersRequest{
						ActorId: testActorIdWalletTxn,
						OrderType: []usstocksPb.WalletOrderType{
							usstocksPb.WalletOrderType_WALLET_ORDER_TYPE_WITHDRAW_FUNDS,
						},
						PageContext: &rpc.PageContextRequest{
							PageSize: 1,
						},
					},
				).Return(&ussOrderPb.GetWalletOrdersResponse{
					Status: rpc.StatusOk(),
					Orders: []*ussOrderPb.WalletOrder{},
				}, nil)
			},
			want: &UsStocksWalletTransactionData{
				AddFunds:      nil,
				WithdrawFunds: nil,
			},
			wantErr: false,
		},
		{
			name: "error: missing actorId",
			args: struct {
				ctx         context.Context
				inputParams options.DataCollectorParams
			}{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "",
				},
			},
			mocks:   func(md *mockDependenciesWalletTxn) {},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			collector, md := newWalletTxnCollectorWithMocks(t)
			tt.mocks(md)

			got, err := collector.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.wantErr {
				require.Error(t, err)
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.want, got)
			}
		})
	}
}
