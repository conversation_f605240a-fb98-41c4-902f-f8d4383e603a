package collectors

import (
	"context"
	"strconv"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	cardPb "github.com/epifi/gamma/api/card"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	typesv2Pb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type DebitCardDeliveryDataCollector struct {
	cardProvisioningClient cpPb.CardProvisioningClient
}

func NewDebitCardDeliveryDataCollector(
	cardProvisioningClient cpPb.CardProvisioningClient,
) *DebitCardDeliveryDataCollector {
	return &DebitCardDeliveryDataCollector{
		cardProvisioningClient: cardProvisioningClient,
	}
}

// CollectData retrieves debit card delivery status information for the given actor
func (c *DebitCardDeliveryDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	// Step 1: Get the current card for the actor
	fetchCardsResp, err := c.cardProvisioningClient.FetchCards(ctx, &cpPb.FetchCardsRequest{
		Actor:     &typesv2Pb.Actor{Id: actorId},
		CardTypes: []cardPb.CardType{cardPb.CardType_DEBIT},
		Limit:     1,
		SortedBy:  cardPb.CardFieldMask_CARD_CREATED_AT,
	})

	if rErr := epifigrpc.RPCError(fetchCardsResp, err); rErr != nil {
		logger.Error(ctx, "error calling FetchCards", zap.String(logger.ACTOR_ID, actorId), zap.Error(rErr))
		return nil, errors.Wrap(rErr, "failed to fetch cards")
	}

	card := fetchCardsResp.GetCards()[0]
	cardId := card.GetId()

	// Initialize delivery data with card details
	deliveryData := &DebitCardDeliveryData{}
	deliveryData.CardForm = card.GetForm().String()
	deliveryData.CardState = card.GetState().String()
	// OrderedAt will be set only from dispatch request, otherwise empty

	// Step 2: Get physical card dispatch requests (all requests in ascending order of created_at)
	dispatchResp, err := c.cardProvisioningClient.FetchPhysicalCardDispatchRequests(ctx, &cpPb.FetchPhysicalCardDispatchRequestsRequest{
		CardIdentifier: &cpPb.CardIdentifier{
			Identifier: &cpPb.CardIdentifier_CardId{CardId: cardId},
		},
	})

	if rErr := epifigrpc.RPCError(dispatchResp, err); rErr != nil {
		logger.Error(ctx, "error calling FetchPhysicalCardDispatchRequests",
			zap.String(logger.ACTOR_ID, actorId),
			zap.String(logger.CARD_ID, cardId),
			zap.Error(rErr))
		// Leave PaymentStatus and OrderedAt empty on error
	} else {
		// Step 3: Process dispatch request if RPC was successful
		// Use the latest (last) dispatch request as requests are in ascending order of created_at
		dispatchRequests := dispatchResp.GetPhysicalCardDispatchRequests()
		paymentStatus := c.determinePaymentStatus(dispatchRequests)
		if paymentStatus != "" {
			deliveryData.PaymentStatus = paymentStatus
			// Use latest dispatch request created time as ordered_at (last element in array)
			latestRequest := dispatchRequests[len(dispatchRequests)-1]
			deliveryData.OrderedAt = strconv.FormatInt(latestRequest.GetCreatedAt().GetSeconds(), 10)
		}
	}

	// Step 4: Get card shipment tracking details
	trackingResp, err := c.cardProvisioningClient.GetCardShipmentTrackingDetails(ctx, &cpPb.GetCardShipmentTrackingDetailsRequest{
		CardId: cardId,
	})

	if rErr := epifigrpc.RPCError(trackingResp, err); rErr != nil {
		// Handle record not found gracefully - log but don't fail if tracking details are not found

		logger.Error(ctx, "error calling GetCardShipmentTrackingDetails",
			zap.String(logger.ACTOR_ID, actorId),
			zap.String(logger.CARD_ID, cardId),
			zap.Error(rErr))

		// Continue with empty tracking data instead of failing
	} else {
		// Set tracking details
		trackingDetails := trackingResp.GetTrackingDetails()
		if trackingDetails != nil {
			deliveryData.AwbNumber = trackingDetails.GetAwb()
			deliveryData.Carrier = trackingDetails.GetCarrier()
			deliveryData.DeliveryState = trackingDetails.GetDeliveryState().String()

		}
	}

	return deliveryData, nil
}

// determinePaymentStatus determines payment status based on dispatch requests
func (c *DebitCardDeliveryDataCollector) determinePaymentStatus(dispatchRequests []*cpPb.PhysicalCardDispatchRequest) string {
	// Check if we have any dispatch requests
	if len(dispatchRequests) == 0 {
		return "" // Return empty string if no dispatch requests found
	}

	// Use the latest (last) dispatch request as requests are in ascending order of created_at
	dispatchRequest := dispatchRequests[len(dispatchRequests)-1]
	state := dispatchRequest.GetState()
	currentStage := dispatchRequest.GetCurrentStage()

	// Payment is pending if:
	// - State is QUEUED or MANUAL_INTERVENTION AND
	// - Current stage is DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS
	if (state == cpPb.RequestState_QUEUED || state == cpPb.RequestState_MANUAL_INTERVENTION) &&
		currentStage == cpPb.DCRequestStage_DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS {
		return "PENDING"
	}

	// Otherwise payment is confirmed
	return "SUCCESS"
}
