package collectors

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	beMoney "github.com/epifi/be-common/pkg/money"

	ussAccPb "github.com/epifi/gamma/api/usstocks/account"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type UsStocksWalletBalanceDataCollector struct {
	usStocksAccountClient ussAccPb.AccountManagerClient
}

func NewUsStocksWalletBalanceDataCollector(
	usStocksAccountClient ussAccPb.AccountManagerClient,
) *UsStocksWalletBalanceDataCollector {
	return &UsStocksWalletBalanceDataCollector{
		usStocksAccountClient: usStocksAccountClient,
	}
}

type UsStocksWalletBalanceData struct {
	Amount string `json:"amount"`
}

func (c *UsStocksWalletBalanceDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	resp, err := c.usStocksAccountClient.GetTradingAccountDetails(ctx, &ussAccPb.GetTradingAccountDetailsRequest{
		Identifier: &ussAccPb.TradingAccountIdentifier{
			Identifier: &ussAccPb.TradingAccountIdentifier_ActorId{
				ActorId: actorId,
			},
		},
		Strategy: ussAccPb.GetTradingAccountDetailsRequest_BEST_EFFORT,
	})

	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error calling US stocks trading account service", zap.String(logger.ACTOR_ID, actorId), zap.Error(rpcErr))
		return nil, errors.Wrap(rpcErr, "failed to get US stocks wallet balance")
	}

	walletDetails := resp.GetTradingAccount().GetWalletDetails()
	if walletDetails == nil {
		return nil, errors.New("wallet details not found")
	}

	if walletDetails.GetCash() == nil || beMoney.IsZero(walletDetails.GetCash()) {
		return &UsStocksWalletBalanceData{
			Amount: "0.00",
		}, nil
	}

	amountString := beMoney.ToDisplayStringWithPrecision(walletDetails.GetCash(), 2)

	return &UsStocksWalletBalanceData{
		Amount: amountString,
	}, nil
}
