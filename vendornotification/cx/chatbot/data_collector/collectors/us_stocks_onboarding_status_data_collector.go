package collectors

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	ussAccPb "github.com/epifi/gamma/api/usstocks/account"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type UsStocksOnboardingStatusDataCollector struct {
	usStocksAccountClient ussAccPb.AccountManagerClient
}

func NewUsStocksOnboardingStatusDataCollector(
	usStocksAccountClient ussAccPb.AccountManagerClient,
) *UsStocksOnboardingStatusDataCollector {
	return &UsStocksOnboardingStatusDataCollector{
		usStocksAccountClient: usStocksAccountClient,
	}
}

type UsStocksOnboardingStatusData struct {
	State string `json:"state"`
	Stage string `json:"stage"`
}

func (c *UsStocksOnboardingStatusDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	resp, err := c.usStocksAccountClient.GetAccount(ctx, &ussAccPb.GetAccountRequest{
		ActorId: actorId,
		Vendor:  commonvgpb.Vendor_ALPACA,
	})

	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error calling US stocks account service", zap.String(logger.ACTOR_ID, actorId), zap.Error(rpcErr))
		return nil, errors.Wrap(rpcErr, "failed to get US stocks account status")
	}

	accountStatus := resp.GetAccount().GetAccountStatus()
	vendorAccountId := resp.GetAccount().GetVendorAccountId()

	if accountStatus == ussAccPb.AccountStatus_ACTIVE && vendorAccountId != "" {
		return &UsStocksOnboardingStatusData{
			State: "ACTIVE",
			Stage: "COMPLETED",
		}, nil
	}

	return &UsStocksOnboardingStatusData{
		State: "INACTIVE",
		Stage: accountStatus.String(),
	}, nil
}
