package collectors

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	evrMocks "github.com/epifi/gamma/api/casper/external_vendor_redemption/mocks"
	genConf "github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

const (
	testActorIdFiStore = "test-actor-123"
)

// FiStoreRedemptionsRequestMatcher is a custom gomock matcher for GetFiStoreRedemptionsRequest
type FiStoreRedemptionsRequestMatcher struct {
	expectedReq *evrPb.GetFiStoreRedemptionsRequest
}

func (m *FiStoreRedemptionsRequestMatcher) Matches(req interface{}) bool {
	fiStoreReq, ok := req.(*evrPb.GetFiStoreRedemptionsRequest)
	if !ok {
		return false
	}

	// Validate ActorId matches
	if fiStoreReq.ActorId != m.expectedReq.ActorId {
		return false
	}

	// Validate Filters exist
	if fiStoreReq.Filters == nil || m.expectedReq.Filters == nil {
		return fiStoreReq.Filters == m.expectedReq.Filters
	}

	// Validate Categories match
	if len(fiStoreReq.Filters.Categories) != len(m.expectedReq.Filters.Categories) {
		return false
	}
	for i, category := range fiStoreReq.Filters.Categories {
		if category != m.expectedReq.Filters.Categories[i] {
			return false
		}
	}

	// Validate OrderStatuses are populated (we check that they exist and exclude UNSPECIFIED and ABANDONED)
	orderStatuses := fiStoreReq.Filters.OrderStatuses
	if len(orderStatuses) != len(evrPb.OrderStatus_name)-2 {
		return false
	}

	// Check that UNSPECIFIED and ABANDONED are not present
	for _, status := range orderStatuses {
		if status == evrPb.OrderStatus_ORDER_STATUS_UNSPECIFIED ||
			status == evrPb.OrderStatus_ORDER_STATUS_ABANDONED {
			return false
		}
	}

	// Validate PageCtxRequest matches
	if fiStoreReq.PageCtxRequest == nil || m.expectedReq.PageCtxRequest == nil {
		return fiStoreReq.PageCtxRequest == m.expectedReq.PageCtxRequest
	}

	if fiStoreReq.PageCtxRequest.PageSize != m.expectedReq.PageCtxRequest.PageSize {
		return false
	}

	return true
}

func (m *FiStoreRedemptionsRequestMatcher) String() string {
	return fmt.Sprintf("FiStoreRedemptionsRequest matching %v", m.expectedReq)
}

type mockDependenciesFiStore struct {
	mockExternalVendorRedemptionsClient *evrMocks.MockExternalVendorRedemptionServiceClient
}

func newFiStoreDataCollectorWithMocks(t *testing.T) (*FiStoreRedemptionsDataCollector, *mockDependenciesFiStore) {
	ctrl := gomock.NewController(t)

	md := &mockDependenciesFiStore{
		mockExternalVendorRedemptionsClient: evrMocks.NewMockExternalVendorRedemptionServiceClient(ctrl),
	}

	// Create mock config with proper Nugget setup
	conf, _ := genConf.NewConfig()
	_ = conf.Nugget().SetFiStoreRedemptionsLimit(int32(5), false, nil)

	dataCollector := NewFiStoreRedemptionsDataCollector(md.mockExternalVendorRedemptionsClient, conf)

	t.Cleanup(func() {
		ctrl.Finish()
	})

	return dataCollector, md
}

func TestFiStoreRedemptionsDataCollector_CollectData(t *testing.T) {
	// Initialize logger for testing
	logger.Init(cfg.TestEnv)
	t.Parallel()

	type args struct {
		ctx         context.Context
		inputParams options.DataCollectorParams
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(md *mockDependenciesFiStore)
		want    *FiStoreRedemptionsData
		wantErr error
	}{
		{
			name: "success: gift cards redemptions with fi points transacted",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:        testActorIdFiStore,
					RewardCategory: "CATEGORY_GIFT_CARDS",
				},
			},
			mocks: func(md *mockDependenciesFiStore) {
				// Create expected request (without OrderStatuses as they are populated dynamically)
				expectedRequest := &evrPb.GetFiStoreRedemptionsRequest{
					ActorId: testActorIdFiStore,
					Filters: &evrPb.GetFiStoreRedemptionsRequest_Filters{
						Categories: []evrPb.Category{evrPb.Category_CATEGORY_GIFT_CARDS},
						// OrderStatuses will be populated dynamically by the data collector
					},
					PageCtxRequest: &rpc.PageContextRequest{
						PageSize: uint32(5),
					},
				}

				requestMatcher := &FiStoreRedemptionsRequestMatcher{
					expectedReq: expectedRequest,
				}

				md.mockExternalVendorRedemptionsClient.EXPECT().GetFiStoreRedemptions(
					context.Background(),
					requestMatcher,
				).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status: rpc.StatusOk(),
					Redemptions: []*evrPb.FiStoreRedemption{
						{
							Id:               "redemption-1",
							VendorRefId:      "order-1",
							Vendor:           evrPb.Vendor_DPANDA,
							OrderStatus:      evrPb.OrderStatus_ORDER_STATUS_DELIVERED,
							ProductPrice:     &money.Money{CurrencyCode: "INR", Units: 100, Nanos: 0},
							SpentFiCoinUnits: 500,
							SpentCashUnits:   &money.Money{CurrencyCode: "INR", Units: 50, Nanos: 0},
							RedemptionMetaData: &evrPb.RedemptionMetaData{
								BrandName:   "brand-1",
								ProductName: "product-name-1",
							},
							OrderTimestamp: timestamppb.Now(),
						},
					},
				}, nil)

				// Call to GetExternalVendorRedemptions for fi points check
				md.mockExternalVendorRedemptionsClient.EXPECT().GetExternalVendorRedemptions(
					context.Background(),
					&evrPb.GetExternalVendorRedemptionsRequest{
						ActorId: testActorIdFiStore,
						Filters: &evrPb.GetExternalVendorRedemptionsRequest_Filters{
							VendorOrderId: "order-1",
							Vendor:        evrPb.Vendor_DPANDA,
						},
					},
				).Return(&evrPb.GetExternalVendorRedemptionsResponse{
					Status: rpc.StatusOk(),
					ExternalVendorRedemptions: []*evrPb.ExternalVendorRedemption{
						{
							Id:      "ext-redemption-1",
							TxnType: evrPb.TxnType_DEBIT,
						},
					},
				}, nil)
			},
			want: &FiStoreRedemptionsData{
				RedemptionItems: []*FiStoreRedemptionItem{
					{
						ProductPrice:         "₹100.00",
						Status:               "ORDER_STATUS_DELIVERED",
						FiCoinsSpent:         "500",
						CashSpent:            "₹50.00",
						BrandName:            "brand-1",
						IsFiPointsTransacted: "true",
						OrderId:              "order-1",
						ProductName:          "product-name-1",
					},
				},
				TotalCount: 1,
			},
			wantErr: nil,
		},
		{
			name: "success: miles exchange redemptions without fi points transacted",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:        testActorIdFiStore,
					RewardCategory: "CATEGORY_MILES_EXCHANGE",
				},
			},
			mocks: func(md *mockDependenciesFiStore) {
				// Create expected request (without OrderStatuses as they are populated dynamically)
				expectedRequest := &evrPb.GetFiStoreRedemptionsRequest{
					ActorId: testActorIdFiStore,
					Filters: &evrPb.GetFiStoreRedemptionsRequest_Filters{
						Categories: []evrPb.Category{evrPb.Category_CATEGORY_MILES_EXCHANGE},
						// OrderStatuses will be populated dynamically by the data collector
					},
					PageCtxRequest: &rpc.PageContextRequest{
						PageSize: uint32(5),
					},
				}

				requestMatcher := &FiStoreRedemptionsRequestMatcher{
					expectedReq: expectedRequest,
				}

				md.mockExternalVendorRedemptionsClient.EXPECT().GetFiStoreRedemptions(
					context.Background(),
					requestMatcher,
				).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status: rpc.StatusOk(),
					Redemptions: []*evrPb.FiStoreRedemption{
						{
							Id:               "redemption-2",
							VendorRefId:      "order-2",
							Vendor:           evrPb.Vendor_POSHVINE,
							OrderStatus:      evrPb.OrderStatus_ORDER_STATUS_CONFIRMED,
							ProductPrice:     &money.Money{CurrencyCode: "INR", Units: 200, Nanos: 500000000},
							SpentFiCoinUnits: 1000,
							SpentCashUnits:   &money.Money{CurrencyCode: "INR", Units: 0, Nanos: 0},
							RedemptionMetaData: &evrPb.RedemptionMetaData{
								BrandName:   "brand-2",
								ProductName: "product-name-2",
							},
							OrderTimestamp: timestamppb.Now(),
						},
					},
				}, nil)

				md.mockExternalVendorRedemptionsClient.EXPECT().GetExternalVendorRedemptions(
					context.Background(),
					&evrPb.GetExternalVendorRedemptionsRequest{
						ActorId: testActorIdFiStore,
						Filters: &evrPb.GetExternalVendorRedemptionsRequest_Filters{
							VendorOrderId: "order-2",
							Vendor:        evrPb.Vendor_POSHVINE,
						},
					},
				).Return(&evrPb.GetExternalVendorRedemptionsResponse{
					Status:                    rpc.StatusOk(),
					ExternalVendorRedemptions: []*evrPb.ExternalVendorRedemption{}, // Empty response
				}, nil)
			},
			want: &FiStoreRedemptionsData{
				RedemptionItems: []*FiStoreRedemptionItem{
					{
						ProductPrice:         "₹200.50",
						Status:               "ORDER_STATUS_CONFIRMED",
						FiCoinsSpent:         "1000",
						CashSpent:            "₹0.00",
						BrandName:            "brand-2",
						IsFiPointsTransacted: "false",
						OrderId:              "order-2",
						ProductName:          "product-name-2",
					},
				},
				TotalCount: 1,
			},
			wantErr: nil,
		},
		{
			name: "success: multiple redemptions with mixed fi points status",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:        testActorIdFiStore,
					RewardCategory: "CATEGORY_GIFT_CARDS",
				},
			},
			mocks: func(md *mockDependenciesFiStore) {
				// Create expected request (without OrderStatuses as they are populated dynamically)
				expectedRequest := &evrPb.GetFiStoreRedemptionsRequest{
					ActorId: testActorIdFiStore,
					Filters: &evrPb.GetFiStoreRedemptionsRequest_Filters{
						Categories: []evrPb.Category{evrPb.Category_CATEGORY_GIFT_CARDS},
						// OrderStatuses will be populated dynamically by the data collector
					},
					PageCtxRequest: &rpc.PageContextRequest{
						PageSize: uint32(5),
					},
				}

				requestMatcher := &FiStoreRedemptionsRequestMatcher{
					expectedReq: expectedRequest,
				}

				md.mockExternalVendorRedemptionsClient.EXPECT().GetFiStoreRedemptions(
					context.Background(),
					requestMatcher,
				).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status: rpc.StatusOk(),
					Redemptions: []*evrPb.FiStoreRedemption{
						{
							Id:               "redemption-3",
							VendorRefId:      "order-3",
							Vendor:           evrPb.Vendor_RAZORPAY,
							OrderStatus:      evrPb.OrderStatus_ORDER_STATUS_SHIPPED,
							ProductPrice:     &money.Money{CurrencyCode: "INR", Units: 150, Nanos: 0},
							SpentFiCoinUnits: 750,
							SpentCashUnits:   &money.Money{CurrencyCode: "INR", Units: 25, Nanos: 0},
							RedemptionMetaData: &evrPb.RedemptionMetaData{
								BrandName:   "brand-3",
								ProductName: "product-name-3",
							},
							OrderTimestamp: timestamppb.Now(),
						},
						{
							Id:               "redemption-4",
							VendorRefId:      "order-4",
							Vendor:           evrPb.Vendor_DPANDA,
							OrderStatus:      evrPb.OrderStatus_ORDER_STATUS_PROCESSING,
							ProductPrice:     &money.Money{CurrencyCode: "INR", Units: 300, Nanos: 0},
							SpentFiCoinUnits: 1500,
							SpentCashUnits:   &money.Money{CurrencyCode: "INR", Units: 0, Nanos: 0},
							RedemptionMetaData: &evrPb.RedemptionMetaData{
								BrandName:   "brand-4",
								ProductName: "product-name-4",
							},
							OrderTimestamp: timestamppb.Now(),
						},
					},
				}, nil)

				// Mock external vendor redemption calls - using DoAndReturn for exact request validation
				// This approach is necessary because:
				// 1. Protobuf objects have internal fields that make exact matching difficult
				// 2. Parallel execution means calls can happen in any order
				// 3. We want to validate the exact request content without gomock.Any()
				md.mockExternalVendorRedemptionsClient.EXPECT().GetExternalVendorRedemptions(
					gomock.Any(), // context can vary
					gomock.Any(), // request - we'll validate this in DoAndReturn
				).DoAndReturn(func(ctx context.Context, req *evrPb.GetExternalVendorRedemptionsRequest, opts ...interface{}) (*evrPb.GetExternalVendorRedemptionsResponse, error) {
					// Validate that the request has the expected ActorId
					if req.GetActorId() != testActorIdFiStore {
						return nil, fmt.Errorf("unexpected actor id: %s", req.GetActorId())
					}

					// Handle order-3 (RAZORPAY vendor) - has fi points
					if req.GetFilters().GetVendorOrderId() == "order-3" && req.GetFilters().GetVendor() == evrPb.Vendor_RAZORPAY {
						return &evrPb.GetExternalVendorRedemptionsResponse{
							Status: rpc.StatusOk(),
							ExternalVendorRedemptions: []*evrPb.ExternalVendorRedemption{
								{
									Id:      "ext-redemption-3",
									TxnType: evrPb.TxnType_DEBIT,
								},
							},
						}, nil
					}

					// Handle order-4 (DPANDA vendor) - doesn't have fi points
					if req.GetFilters().GetVendorOrderId() == "order-4" && req.GetFilters().GetVendor() == evrPb.Vendor_DPANDA {
						return &evrPb.GetExternalVendorRedemptionsResponse{
							Status:                    rpc.StatusOk(),
							ExternalVendorRedemptions: []*evrPb.ExternalVendorRedemption{},
						}, nil
					}

					return nil, fmt.Errorf("unexpected request: orderId=%s, vendor=%s",
						req.GetFilters().GetVendorOrderId(), req.GetFilters().GetVendor().String())
				}).Times(2)
			},
			want: &FiStoreRedemptionsData{
				RedemptionItems: []*FiStoreRedemptionItem{
					{
						ProductPrice:         "₹150.00",
						Status:               "ORDER_STATUS_SHIPPED",
						FiCoinsSpent:         "750",
						CashSpent:            "₹25.00",
						BrandName:            "brand-3",
						IsFiPointsTransacted: "true", // order-3 has fi points
						OrderId:              "order-3",
						ProductName:          "product-name-3",
					},
					{
						ProductPrice:         "₹300.00",
						Status:               "ORDER_STATUS_PROCESSING",
						FiCoinsSpent:         "1500",
						CashSpent:            "₹0.00",
						BrandName:            "brand-4",
						IsFiPointsTransacted: "false", // order-4 doesn't have fi points
						OrderId:              "order-4",
						ProductName:          "product-name-4",
					},
				},
				TotalCount: 2,
			},
			wantErr: nil,
		},
		{
			name: "error: missing actor id",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					RewardCategory: "CATEGORY_GIFT_CARDS",
				},
			},
			mocks:   func(md *mockDependenciesFiStore) {},
			want:    nil,
			wantErr: fmt.Errorf("actorId is required"),
		},
		{
			name: "error: missing reward category",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdFiStore,
				},
			},
			mocks:   func(md *mockDependenciesFiStore) {},
			want:    nil,
			wantErr: fmt.Errorf("rewardCategory is required"),
		},
		{
			name: "error: unsupported reward category",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:        testActorIdFiStore,
					RewardCategory: "INVALID_CATEGORY",
				},
			},
			mocks:   func(md *mockDependenciesFiStore) {},
			want:    nil,
			wantErr: fmt.Errorf("unsupported reward category: INVALID_CATEGORY"),
		},
		{
			name: "error: GetFiStoreRedemptions RPC failure",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:        testActorIdFiStore,
					RewardCategory: "CATEGORY_GIFT_CARDS",
				},
			},
			mocks: func(md *mockDependenciesFiStore) {
				// Create expected request (without OrderStatuses as they are populated dynamically)
				expectedRequest := &evrPb.GetFiStoreRedemptionsRequest{
					ActorId: testActorIdFiStore,
					Filters: &evrPb.GetFiStoreRedemptionsRequest_Filters{
						Categories: []evrPb.Category{evrPb.Category_CATEGORY_GIFT_CARDS},
						// OrderStatuses will be populated dynamically by the data collector
					},
					PageCtxRequest: &rpc.PageContextRequest{
						PageSize: uint32(5),
					},
				}

				requestMatcher := &FiStoreRedemptionsRequestMatcher{
					expectedReq: expectedRequest,
				}

				md.mockExternalVendorRedemptionsClient.EXPECT().GetFiStoreRedemptions(
					context.Background(),
					requestMatcher,
				).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:    nil,
			wantErr: fmt.Errorf("failed to get fi store redemptions"),
		},
		{
			name: "success: external vendor redemption RPC error but continues processing",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:        testActorIdFiStore,
					RewardCategory: "CATEGORY_GIFT_CARDS",
				},
			},
			mocks: func(md *mockDependenciesFiStore) {
				// Create expected request (without OrderStatuses as they are populated dynamically)
				expectedRequest := &evrPb.GetFiStoreRedemptionsRequest{
					ActorId: testActorIdFiStore,
					Filters: &evrPb.GetFiStoreRedemptionsRequest_Filters{
						Categories: []evrPb.Category{evrPb.Category_CATEGORY_GIFT_CARDS},
						// OrderStatuses will be populated dynamically by the data collector
					},
					PageCtxRequest: &rpc.PageContextRequest{
						PageSize: uint32(5),
					},
				}

				requestMatcher := &FiStoreRedemptionsRequestMatcher{
					expectedReq: expectedRequest,
				}

				md.mockExternalVendorRedemptionsClient.EXPECT().GetFiStoreRedemptions(
					context.Background(),
					requestMatcher,
				).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status: rpc.StatusOk(),
					Redemptions: []*evrPb.FiStoreRedemption{
						{
							Id:               "redemption-5",
							VendorRefId:      "order-5",
							Vendor:           evrPb.Vendor_DPANDA,
							OrderStatus:      evrPb.OrderStatus_ORDER_STATUS_DELIVERED,
							ProductPrice:     &money.Money{CurrencyCode: "INR", Units: 100, Nanos: 0},
							SpentFiCoinUnits: 500,
							SpentCashUnits:   &money.Money{CurrencyCode: "INR", Units: 50, Nanos: 0},
							RedemptionMetaData: &evrPb.RedemptionMetaData{
								BrandName:   "brand-5",
								ProductName: "product-name-5",
							},
							OrderTimestamp: timestamppb.Now(),
						},
					},
				}, nil)

				// External vendor redemption call fails
				md.mockExternalVendorRedemptionsClient.EXPECT().GetExternalVendorRedemptions(
					context.Background(),
					&evrPb.GetExternalVendorRedemptionsRequest{
						ActorId: testActorIdFiStore,
						Filters: &evrPb.GetExternalVendorRedemptionsRequest_Filters{
							VendorOrderId: "order-5",
							Vendor:        evrPb.Vendor_DPANDA,
						},
					},
				).Return(&evrPb.GetExternalVendorRedemptionsResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &FiStoreRedemptionsData{
				RedemptionItems: []*FiStoreRedemptionItem{
					{
						ProductPrice:         "₹100.00",
						Status:               "ORDER_STATUS_DELIVERED",
						FiCoinsSpent:         "500",
						CashSpent:            "₹50.00",
						BrandName:            "brand-5",
						IsFiPointsTransacted: "false", // Defaults to false on error
						OrderId:              "order-5",
						ProductName:          "product-name-5",
					},
				},
				TotalCount: 1,
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			dataCollector, md := newFiStoreDataCollectorWithMocks(t)

			tt.mocks(md)

			got, err := dataCollector.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.wantErr != nil {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr.Error())
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				gotData, ok := got.(*FiStoreRedemptionsData)
				require.True(t, ok)
				require.Equal(t, tt.want.TotalCount, gotData.TotalCount)
				require.Len(t, gotData.RedemptionItems, tt.want.TotalCount)

				// Compare redemption items (order is maintained with errgroup)
				if len(tt.want.RedemptionItems) > 0 {
					require.Equal(t, tt.want.RedemptionItems[0], gotData.RedemptionItems[0])
				}
			}
		})
	}
}
