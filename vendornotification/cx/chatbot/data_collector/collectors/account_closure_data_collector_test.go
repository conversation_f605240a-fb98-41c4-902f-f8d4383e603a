package collectors

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	enumsPb "github.com/epifi/gamma/api/frontend/account/enums"
	saClosurePb "github.com/epifi/gamma/api/frontend/account/sa_closure"
	saClosureMocks "github.com/epifi/gamma/api/frontend/account/sa_closure/mocks"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

const (
	testActorId = "test-actor-123"
)

type mockDependencies struct {
	mockSaClosureClient *saClosureMocks.MockSavingsAccountClosureClient
}

func newDataCollectorWithMocks(t *testing.T) (*AccountClosureDataCollector, *mockDependencies) {
	ctrl := gomock.NewController(t)

	md := &mockDependencies{
		mockSaClosureClient: saClosureMocks.NewMockSavingsAccountClosureClient(ctrl),
	}

	dataCollector := NewAccountClosureDataCollector(md.mockSaClosureClient)

	t.Cleanup(func() {
		ctrl.Finish()
	})

	return dataCollector, md
}

func TestAccountClosureDataCollector_CollectData(t *testing.T) {
	// Initialize logger for testing
	logger.Init(cfg.TestEnv)
	type args struct {
		ctx         context.Context
		inputParams options.DataCollectorParams
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(md *mockDependencies)
		want    *AccountClosureData
		wantErr error
	}{
		{
			name: "error: empty actor id",
			args: args{
				ctx:         context.Background(),
				inputParams: options.DataCollectorParams{},
			},
			mocks:   func(md *mockDependencies) {},
			want:    nil,
			wantErr: errors.New("actorId is required"),
		},
		{
			name: "error: rpc call failed",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(md *mockDependencies) {
				md.mockSaClosureClient.EXPECT().EvaluateUserForClosureEligibility(
					context.Background(),
					&saClosurePb.EvaluateUserForClosureEligibilityRequest{
						ActorId: testActorId,
					},
				).Return(nil, errors.New("rpc error"))
			},
			want:    nil,
			wantErr: errors.New("failed to evaluate user for closure eligibility: rpc error"),
		},

		{
			name: "success: user is eligible for closure",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(md *mockDependencies) {
				resp := &saClosurePb.EvaluateUserForClosureEligibilityResponse{
					RespHeader: &headerPb.ResponseHeader{
						Status: rpc.StatusOk(),
					},
					FailedCriteriaCheck:    false,
					HasFreeze:              false,
					HasLien:                false,
					IsAccountClosed:        false,
					EvaluatedCriteriaItems: []*saClosurePb.SaClosureCriteriaItemStatus{},
					PendingChargesAmount:   nil,
				}
				md.mockSaClosureClient.EXPECT().EvaluateUserForClosureEligibility(
					context.Background(),
					&saClosurePb.EvaluateUserForClosureEligibilityRequest{
						ActorId: testActorId,
					},
				).Return(resp, nil)
			},
			want: &AccountClosureData{
				IsEligible:              "true",
				ReasonsForIneligibility: nil,
				PendingChargesAmount:    "",
			},
			wantErr: nil,
		},
		{
			name: "success: user is not eligible - failed criteria items",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(md *mockDependencies) {
				resp := &saClosurePb.EvaluateUserForClosureEligibilityResponse{
					RespHeader: &headerPb.ResponseHeader{
						Status: rpc.StatusOk(),
					},
					FailedCriteriaCheck: true,
					HasFreeze:           false,
					HasLien:             false,
					IsAccountClosed:     false,
					EvaluatedCriteriaItems: []*saClosurePb.SaClosureCriteriaItemStatus{
						{
							CriteriaItem: enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_JUMP,
							IsFailed:     true,
						},
						{
							CriteriaItem: enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_MUTUAL_FUND,
							IsFailed:     true,
						},
						{
							CriteriaItem: enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_FI_COIN_BALANCE,
							IsFailed:     false, // This should not be included in reasons
						},
					},
					PendingChargesAmount: nil,
				}
				md.mockSaClosureClient.EXPECT().EvaluateUserForClosureEligibility(
					context.Background(),
					&saClosurePb.EvaluateUserForClosureEligibilityRequest{
						ActorId: testActorId,
					},
				).Return(resp, nil)
			},
			want: &AccountClosureData{
				IsEligible:              "false",
				ReasonsForIneligibility: []string{"Jump Investment", "Mutual Fund"},
				PendingChargesAmount:    "",
			},
			wantErr: nil,
		},
		{
			name: "success: user is not eligible - has lien, freeze, and pending charges",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(md *mockDependencies) {
				pendingCharges := &moneyPb.Money{
					Units:        15, // 15.00 INR
					Nanos:        0,
					CurrencyCode: "INR",
				}
				resp := &saClosurePb.EvaluateUserForClosureEligibilityResponse{
					RespHeader: &headerPb.ResponseHeader{
						Status: rpc.StatusOk(),
					},
					FailedCriteriaCheck:    true,
					HasFreeze:              true,
					HasLien:                true,
					IsAccountClosed:        false,
					EvaluatedCriteriaItems: []*saClosurePb.SaClosureCriteriaItemStatus{},
					PendingChargesAmount:   pendingCharges,
				}
				md.mockSaClosureClient.EXPECT().EvaluateUserForClosureEligibility(
					context.Background(),
					&saClosurePb.EvaluateUserForClosureEligibilityRequest{
						ActorId: testActorId,
					},
				).Return(resp, nil)
			},
			want: &AccountClosureData{
				IsEligible:              "false",
				ReasonsForIneligibility: []string{"Account Lien", "Account Freeze", "Pending Charges"},
				PendingChargesAmount:    "₹15.00",
			},
			wantErr: nil,
		},
		{
			name: "success: user is not eligible - account already closed",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(md *mockDependencies) {
				resp := &saClosurePb.EvaluateUserForClosureEligibilityResponse{
					RespHeader: &headerPb.ResponseHeader{
						Status: rpc.StatusOk(),
					},
					FailedCriteriaCheck:    true,
					HasFreeze:              false,
					HasLien:                false,
					IsAccountClosed:        true,
					EvaluatedCriteriaItems: []*saClosurePb.SaClosureCriteriaItemStatus{},
					PendingChargesAmount:   nil,
				}
				md.mockSaClosureClient.EXPECT().EvaluateUserForClosureEligibility(
					context.Background(),
					&saClosurePb.EvaluateUserForClosureEligibilityRequest{
						ActorId: testActorId,
					},
				).Return(resp, nil)
			},
			want: &AccountClosureData{
				IsEligible:              "false",
				ReasonsForIneligibility: []string{"Account already closed"},
				PendingChargesAmount:    "",
			},
			wantErr: nil,
		},
		{
			name: "success: comprehensive test with multiple criteria and pending charges",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(md *mockDependencies) {
				pendingCharges := &moneyPb.Money{
					Units:        250, // 250.75 INR
					Nanos:        *********,
					CurrencyCode: "INR",
				}
				resp := &saClosurePb.EvaluateUserForClosureEligibilityResponse{
					RespHeader: &headerPb.ResponseHeader{
						Status: rpc.StatusOk(),
					},
					FailedCriteriaCheck: true,
					HasFreeze:           true,
					HasLien:             false,
					IsAccountClosed:     false,
					EvaluatedCriteriaItems: []*saClosurePb.SaClosureCriteriaItemStatus{
						{
							CriteriaItem: enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_AMPLIFI_CREDIT_CARD,
							IsFailed:     true,
						},
						{
							CriteriaItem: enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_AUTO_PAY,
							IsFailed:     true,
						},
						{
							CriteriaItem: enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_SAVINGS_ACCOUNT_BALANCE,
							IsFailed:     false, // Should not be included
						},
					},
					PendingChargesAmount: pendingCharges,
				}
				md.mockSaClosureClient.EXPECT().EvaluateUserForClosureEligibility(
					context.Background(),
					&saClosurePb.EvaluateUserForClosureEligibilityRequest{
						ActorId: testActorId,
					},
				).Return(resp, nil)
			},
			want: &AccountClosureData{
				IsEligible:              "false",
				ReasonsForIneligibility: []string{"Amplifi credit card", "Auto Pay", "Account Freeze", "Pending Charges"},
				PendingChargesAmount:    "₹250.75",
			},
			wantErr: nil,
		},
		{
			name: "success: pending charges with zero amount - should not be included",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(md *mockDependencies) {
				zeroPendingCharges := &moneyPb.Money{
					Units:        0,
					Nanos:        0,
					CurrencyCode: "INR",
				}
				resp := &saClosurePb.EvaluateUserForClosureEligibilityResponse{
					RespHeader: &headerPb.ResponseHeader{
						Status: rpc.StatusOk(),
					},
					FailedCriteriaCheck:    false,
					HasFreeze:              false,
					HasLien:                false,
					IsAccountClosed:        false,
					EvaluatedCriteriaItems: []*saClosurePb.SaClosureCriteriaItemStatus{},
					PendingChargesAmount:   zeroPendingCharges,
				}
				md.mockSaClosureClient.EXPECT().EvaluateUserForClosureEligibility(
					context.Background(),
					&saClosurePb.EvaluateUserForClosureEligibilityRequest{
						ActorId: testActorId,
					},
				).Return(resp, nil)
			},
			want: &AccountClosureData{
				IsEligible:              "true",
				ReasonsForIneligibility: nil,
				PendingChargesAmount:    "",
			},
			wantErr: nil,
		},
		{
			name: "error: unmapped criteria item - should return error",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(md *mockDependencies) {
				resp := &saClosurePb.EvaluateUserForClosureEligibilityResponse{
					RespHeader: &headerPb.ResponseHeader{
						Status: rpc.StatusOk(),
					},
					FailedCriteriaCheck: true,
					HasFreeze:           false,
					HasLien:             false,
					IsAccountClosed:     false,
					EvaluatedCriteriaItems: []*saClosurePb.SaClosureCriteriaItemStatus{
						{
							CriteriaItem: enumsPb.SaClosureCriteriaItem(999), // Unknown criteria item
							IsFailed:     true,
						},
					},
					PendingChargesAmount: nil,
				}
				md.mockSaClosureClient.EXPECT().EvaluateUserForClosureEligibility(
					context.Background(),
					&saClosurePb.EvaluateUserForClosureEligibilityRequest{
						ActorId: testActorId,
					},
				).Return(resp, nil)
			},
			want:    nil,
			wantErr: errors.New("unmapped criteria item found"),
		},
		{
			name: "success: all enum values are mapped - comprehensive test",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(md *mockDependencies) {
				// Create criteria items by iterating from 0 to the number of enums
				var allCriteriaItems []*saClosurePb.SaClosureCriteriaItemStatus
				for i := 0; i < len(enumsPb.SaClosureCriteriaItem_name); i++ {
					allCriteriaItems = append(allCriteriaItems, &saClosurePb.SaClosureCriteriaItemStatus{
						CriteriaItem: enumsPb.SaClosureCriteriaItem(i),
						IsFailed:     true,
					})
				}

				resp := &saClosurePb.EvaluateUserForClosureEligibilityResponse{
					RespHeader: &headerPb.ResponseHeader{
						Status: rpc.StatusOk(),
					},
					FailedCriteriaCheck:    true,
					HasFreeze:              false,
					HasLien:                false,
					IsAccountClosed:        false,
					EvaluatedCriteriaItems: allCriteriaItems,
					PendingChargesAmount:   nil,
				}
				md.mockSaClosureClient.EXPECT().EvaluateUserForClosureEligibility(
					context.Background(),
					&saClosurePb.EvaluateUserForClosureEligibilityRequest{
						ActorId: testActorId,
					},
				).Return(resp, nil)
			},
			want: &AccountClosureData{
				IsEligible: "false",
				ReasonsForIneligibility: []string{
					"unspecified reason",
					"Jump Investment",
					"Smart Deposit",
					"Fixed Deposit",
					"Mutual Fund",
					"US Stocks Investments",
					"Indian Stocks Investments",
					"Vehicle Loan",
					"Personal Loan",
					"Education Loan",
					"Home Loan",
					"Other Loan",
					"Amplifi credit card",
					"Simplifi credit card",
					"Auto Pay",
					"Auto Pay SIP",
					"Zero Fi point balance",
					"Zero savings account balance",
					"Magnifi credit card",
					"Auto save",
					"UPI Lite",
				},
				PendingChargesAmount: "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dataCollector, md := newDataCollectorWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(md)
			}
			got, err := dataCollector.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.wantErr != nil {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr.Error())
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.want, got)
			}
		})
	}
}
