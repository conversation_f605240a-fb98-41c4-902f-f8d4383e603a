package collectors

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type TieringRewardEligibilityDataCollector struct {
	tieringClient tieringPb.TieringClient
	timeImpl      datetime.Time
}

func NewTieringRewardEligibilityDataCollector(
	tieringClient tieringPb.TieringClient,
	timeImpl datetime.Time,
) *TieringRewardEligibilityDataCollector {
	return &TieringRewardEligibilityDataCollector{
		tieringClient: tieringClient,
		timeImpl:      timeImpl,
	}
}

type TieringRewardEligibilityData struct {
	IsEligible string `json:"is_eligible"`
}

// CollectData evaluates if an actor is eligible for cashback rewards based on their tier history
// The evaluation logic:
// 1. We fetch distinct tiers the user was in during the previous month using GetActorDistinctTiers
//   - We do this first to save API calls. Without this, we'd have to call CheckIfActorIsEligibleForCashbackReward
//     for ALL cashback eligible tiers (e.g., INFINITE, PLUS, SALARY, etc.), which would be wasteful
//   - By getting distinct tiers first, we only call the eligibility check for tiers the user was actually in
//
// 2. For each distinct tier, we check if it's cashback eligible using IsCashbackEligibleTier()
// 3. For cashback eligible tiers, we call CheckIfActorIsEligibleForCashbackReward to verify detailed eligibility
// 4. If any tier returns eligible, we return true immediately (early return pattern)
//
// NOTE : This only tells will the rewards be credited or actualized for the actor or not.
//
//	This does not check whether there were any rewards in projected state for the user or not .
func (c *TieringRewardEligibilityDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	// Convert current time to IST before doing any month operations to ensure consistent timezone handling
	// This is critical because month start/end calculations can vary depending on timezone
	currentTimeIST := c.timeImpl.Now().In(datetime.IST)

	// Calculate previous month start and end in IST timezone
	// Use safer approach: get current month start first, then subtract month to avoid edge cases
	// (e.g., March 31 - 1 month = March 3 due to February having fewer days)
	currentMonthStart := datetime.StartOfMonth(currentTimeIST)
	previousMonth := currentMonthStart.AddDate(0, -1, 0)
	previousMonthStart := datetime.StartOfMonth(previousMonth)
	previousMonthEnd := datetime.EndOfMonth(previousMonth)

	// First, get all distinct tiers the user was in during the previous month
	// We fetch distinct tiers ourselves instead of relying on the RPC's internal distinct tier check
	// because we want to evaluate eligibility across ALL tiers the user was part of during the month
	// This ensures comprehensive evaluation even if user switched tiers mid-month
	distinctTiersResp, err := c.tieringClient.GetActorDistinctTiers(ctx, &tieringPb.GetActorDistinctTiersRequest{
		ActorId:   actorId,
		TimeSince: timestamppb.New(previousMonthStart),
		TimeTill:  timestamppb.New(previousMonthEnd),
	})

	if rErr := epifigrpc.RPCError(distinctTiersResp, err); rErr != nil {
		logger.Error(ctx, "error calling GetActorDistinctTiers",
			zap.String(logger.ACTOR_ID, actorId),
			zap.Error(rErr))
		return nil, errors.Wrap(rErr, "failed to get actor distinct tiers")
	}

	distinctTiers := distinctTiersResp.GetDistinctTiers()
	if len(distinctTiers) == 0 {
		logger.Info(ctx, "no distinct tiers found for actor",
			zap.String(logger.ACTOR_ID, actorId))
		return &TieringRewardEligibilityData{
			IsEligible: "false",
		}, nil
	}

	// Evaluate each distinct tier for cashback reward eligibility
	for _, tier := range distinctTiers {
		// First check if tier is cashback eligible at all
		if !tier.IsCashbackEligibleTier() {
			logger.Info(ctx, "tier is not cashback eligible, skipping",
				zap.String(logger.ACTOR_ID, actorId),
				zap.String("tier", tier.String()))
			continue
		}

		// For cashback eligible tiers, check detailed eligibility for the previous month
		eligibilityResp, err := c.tieringClient.CheckIfActorIsEligibleForCashbackReward(ctx, &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
			ActorId:     actorId,
			Tier:        tier,
			RewardMonth: timestamppb.New(previousMonthStart), // Use previous month for evaluation
		})

		if rErr := epifigrpc.RPCError(eligibilityResp, err); rErr != nil {
			logger.Error(ctx, "error calling CheckIfActorIsEligibleForCashbackReward",
				zap.String(logger.ACTOR_ID, actorId),
				zap.String("tier", tier.String()),
				zap.Error(rErr))
			return nil, errors.Wrapf(rErr, "failed to check cashback eligibility for tier %s", tier.String())
		}

		if eligibilityResp.GetIsEligible() {
			// This check verifies if the user has projected rewards and is eligible for that cashback reward.
			// If eligible, the user will be rewarded the rewards.
			// Early return - if any tier is eligible, user is eligible for rewards
			return &TieringRewardEligibilityData{
				IsEligible: "true",
			}, nil
		}
	}

	// If we reach here, no tier was eligible
	return &TieringRewardEligibilityData{
		IsEligible: "false",
	}, nil
}
