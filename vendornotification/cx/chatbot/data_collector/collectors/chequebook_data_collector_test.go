package collectors

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	alfredPb "github.com/epifi/gamma/api/alfred"
	alfredMocks "github.com/epifi/gamma/api/alfred/mocks"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

func TestChequebookDataCollector_CollectData(t *testing.T) {
	t.Parallel()
	logger.Init("test")

	type args struct {
		ctx         context.Context
		inputParams options.DataCollectorParams
	}

	tests := []struct {
		name           string
		args           args
		setupMockCalls func(mockAlfredClient *alfredMocks.MockAlfredClient)
		want           any
		wantErr        bool
		wantErrMsg     string
	}{
		{
			name: "success: chequebook request found with complete metadata",
			args: args{
				ctx:         context.Background(),
				inputParams: options.DataCollectorParams{ActorId: "test-actor-123"},
			},
			setupMockCalls: func(mockAlfredClient *alfredMocks.MockAlfredClient) {
				orderedAt := timestamppb.New(time.Unix(1640995200, 0)) // 2022-01-01 00:00:00 UTC
				mockAlfredClient.EXPECT().GetAllRequestStatusDetails(
					gomock.Any(),
					&alfredPb.GetAllRequestStatusDetailsRequest{
						Filters: &alfredPb.Filters{
							ActorId:      "test-actor-123",
							RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK},
							StatusList: []alfredPb.Status{
								alfredPb.Status_STATUS_IN_PROGRESS,
								alfredPb.Status_STATUS_SUCCESS,
								alfredPb.Status_STATUS_FAILED,
								alfredPb.Status_STATUS_STUCK,
								alfredPb.Status_STATUS_CREATED,
							},
						},
						PageContext: &rpcPb.PageContextRequest{
							PageSize: 1,
						},
						SortOrder: alfredPb.SortOrder_SORT_ORDER_DESC,
					},
				).Return(&alfredPb.GetAllRequestStatusDetailsResponse{
					Status: rpcPb.StatusOk(),
					ServiceRequestList: []*alfredPb.ServiceRequest{
						{
							Status: alfredPb.Status_STATUS_SUCCESS,
							Details: &alfredPb.Details{
								Metadata: &alfredPb.Details_ChequebookMetadata{
									ChequebookMetadata: &alfredPb.ChequebookMetadata{
										OrderedAt:  orderedAt,
										TrackingId: "TRK123456789",
									},
								},
							},
						},
					},
				}, nil)
			},
			want: &ChequebookRequestData{
				HasOrdered:  "true",
				OrderedAt:   "1640995200",
				AwbNumber:   "TRK123456789",
				OrderStatus: "STATUS_SUCCESS",
			},
			wantErr: false,
		},
		{
			name: "success: chequebook request found with partial metadata",
			args: args{
				ctx:         context.Background(),
				inputParams: options.DataCollectorParams{ActorId: "test-actor-456"},
			},
			setupMockCalls: func(mockAlfredClient *alfredMocks.MockAlfredClient) {
				mockAlfredClient.EXPECT().GetAllRequestStatusDetails(
					gomock.Any(),
					&alfredPb.GetAllRequestStatusDetailsRequest{
						Filters: &alfredPb.Filters{
							ActorId:      "test-actor-456",
							RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK},
							StatusList: []alfredPb.Status{
								alfredPb.Status_STATUS_IN_PROGRESS,
								alfredPb.Status_STATUS_SUCCESS,
								alfredPb.Status_STATUS_FAILED,
								alfredPb.Status_STATUS_STUCK,
								alfredPb.Status_STATUS_CREATED,
							},
						},
						PageContext: &rpcPb.PageContextRequest{
							PageSize: 1,
						},
						SortOrder: alfredPb.SortOrder_SORT_ORDER_DESC,
					},
				).Return(&alfredPb.GetAllRequestStatusDetailsResponse{
					Status: rpcPb.StatusOk(),
					ServiceRequestList: []*alfredPb.ServiceRequest{
						{
							Status: alfredPb.Status_STATUS_IN_PROGRESS,
							Details: &alfredPb.Details{
								Metadata: &alfredPb.Details_ChequebookMetadata{
									ChequebookMetadata: &alfredPb.ChequebookMetadata{
										// No OrderedAt and TrackingId
									},
								},
							},
						},
					},
				}, nil)
			},
			want: &ChequebookRequestData{
				HasOrdered:  "true",
				OrderedAt:   "",
				AwbNumber:   "",
				OrderStatus: "STATUS_IN_PROGRESS",
			},
			wantErr: false,
		},
		{
			name: "success: chequebook request found with no metadata",
			args: args{
				ctx:         context.Background(),
				inputParams: options.DataCollectorParams{ActorId: "test-actor-789"},
			},
			setupMockCalls: func(mockAlfredClient *alfredMocks.MockAlfredClient) {
				mockAlfredClient.EXPECT().GetAllRequestStatusDetails(
					gomock.Any(),
					&alfredPb.GetAllRequestStatusDetailsRequest{
						Filters: &alfredPb.Filters{
							ActorId:      "test-actor-789",
							RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK},
							StatusList: []alfredPb.Status{
								alfredPb.Status_STATUS_IN_PROGRESS,
								alfredPb.Status_STATUS_SUCCESS,
								alfredPb.Status_STATUS_FAILED,
								alfredPb.Status_STATUS_STUCK,
								alfredPb.Status_STATUS_CREATED,
							},
						},
						PageContext: &rpcPb.PageContextRequest{
							PageSize: 1,
						},
						SortOrder: alfredPb.SortOrder_SORT_ORDER_DESC,
					},
				).Return(&alfredPb.GetAllRequestStatusDetailsResponse{
					Status: rpcPb.StatusOk(),
					ServiceRequestList: []*alfredPb.ServiceRequest{
						{
							Status:  alfredPb.Status_STATUS_FAILED,
							Details: nil, // No details
						},
					},
				}, nil)
			},
			want: &ChequebookRequestData{
				HasOrdered:  "true",
				OrderedAt:   "",
				AwbNumber:   "",
				OrderStatus: "STATUS_FAILED",
			},
			wantErr: false,
		},
		{
			name: "success: no chequebook requests found",
			args: args{
				ctx:         context.Background(),
				inputParams: options.DataCollectorParams{ActorId: "test-actor-no-requests"},
			},
			setupMockCalls: func(mockAlfredClient *alfredMocks.MockAlfredClient) {
				mockAlfredClient.EXPECT().GetAllRequestStatusDetails(
					gomock.Any(),
					&alfredPb.GetAllRequestStatusDetailsRequest{
						Filters: &alfredPb.Filters{
							ActorId:      "test-actor-no-requests",
							RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK},
							StatusList: []alfredPb.Status{
								alfredPb.Status_STATUS_IN_PROGRESS,
								alfredPb.Status_STATUS_SUCCESS,
								alfredPb.Status_STATUS_FAILED,
								alfredPb.Status_STATUS_STUCK,
								alfredPb.Status_STATUS_CREATED,
							},
						},
						PageContext: &rpcPb.PageContextRequest{
							PageSize: 1,
						},
						SortOrder: alfredPb.SortOrder_SORT_ORDER_DESC,
					},
				).Return(&alfredPb.GetAllRequestStatusDetailsResponse{
					Status:             rpcPb.StatusOk(),
					ServiceRequestList: []*alfredPb.ServiceRequest{}, // Empty list
				}, nil)
			},
			want: &ChequebookRequestData{
				HasOrdered: "false",
			},
			wantErr: false,
		},
		{
			name: "failure: empty actor id",
			args: args{
				ctx:         context.Background(),
				inputParams: options.DataCollectorParams{ActorId: ""},
			},
			setupMockCalls: func(mockAlfredClient *alfredMocks.MockAlfredClient) {
				// No mock calls expected as validation fails early
			},
			want:       nil,
			wantErr:    true,
			wantErrMsg: "actorId is required",
		},
		{
			name: "failure: alfred client returns error",
			args: args{
				ctx:         context.Background(),
				inputParams: options.DataCollectorParams{ActorId: "test-actor-error"},
			},
			setupMockCalls: func(mockAlfredClient *alfredMocks.MockAlfredClient) {
				mockAlfredClient.EXPECT().GetAllRequestStatusDetails(
					gomock.Any(),
					&alfredPb.GetAllRequestStatusDetailsRequest{
						Filters: &alfredPb.Filters{
							ActorId:      "test-actor-error",
							RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK},
							StatusList: []alfredPb.Status{
								alfredPb.Status_STATUS_IN_PROGRESS,
								alfredPb.Status_STATUS_SUCCESS,
								alfredPb.Status_STATUS_FAILED,
								alfredPb.Status_STATUS_STUCK,
								alfredPb.Status_STATUS_CREATED,
							},
						},
						PageContext: &rpcPb.PageContextRequest{
							PageSize: 1,
						},
						SortOrder: alfredPb.SortOrder_SORT_ORDER_DESC,
					},
				).Return(nil, errors.New("connection timeout"))
			},
			want:       nil,
			wantErr:    true,
			wantErrMsg: "failed to get chequebook request status details for actor test-actor-error",
		},
		{
			name: "failure: alfred client returns rpc error",
			args: args{
				ctx:         context.Background(),
				inputParams: options.DataCollectorParams{ActorId: "test-actor-rpc-error"},
			},
			setupMockCalls: func(mockAlfredClient *alfredMocks.MockAlfredClient) {
				mockAlfredClient.EXPECT().GetAllRequestStatusDetails(
					gomock.Any(),
					&alfredPb.GetAllRequestStatusDetailsRequest{
						Filters: &alfredPb.Filters{
							ActorId:      "test-actor-rpc-error",
							RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK},
							StatusList: []alfredPb.Status{
								alfredPb.Status_STATUS_IN_PROGRESS,
								alfredPb.Status_STATUS_SUCCESS,
								alfredPb.Status_STATUS_FAILED,
								alfredPb.Status_STATUS_STUCK,
								alfredPb.Status_STATUS_CREATED,
							},
						},
						PageContext: &rpcPb.PageContextRequest{
							PageSize: 1,
						},
						SortOrder: alfredPb.SortOrder_SORT_ORDER_DESC,
					},
				).Return(&alfredPb.GetAllRequestStatusDetailsResponse{
					Status: rpcPb.StatusInternalWithDebugMsg("internal server error"),
				}, nil)
			},
			want:       nil,
			wantErr:    true,
			wantErrMsg: "failed to get chequebook request status details for actor test-actor-rpc-error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockAlfredClient := alfredMocks.NewMockAlfredClient(ctrl)
			tt.setupMockCalls(mockAlfredClient)

			c := NewChequebookDataCollector(mockAlfredClient)
			got, err := c.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.wantErr {
				require.Error(t, err)
				if tt.wantErrMsg != "" {
					assert.Contains(t, err.Error(), tt.wantErrMsg)
				}
				assert.Nil(t, got)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}
