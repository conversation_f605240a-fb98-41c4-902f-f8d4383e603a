package collectors

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	salaryProgramPb "github.com/epifi/gamma/api/salaryprogram"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type B2BSalaryProgramDataCollector struct {
	salaryProgramClient salaryProgramPb.SalaryProgramClient
	userClient          userPb.UsersClient
}

func NewB2BSalaryProgramDataCollector(
	salaryProgramClient salaryProgramPb.SalaryProgramClient,
	userClient userPb.UsersClient,
) *B2BSalaryProgramDataCollector {
	return &B2BSalaryProgramDataCollector{
		salaryProgramClient: salaryProgramClient,
		userClient:          userClient,
	}
}

func (c *B2BSalaryProgramDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	// Initialize the response with default values
	b2bSalaryProgramData := &B2BSalaryProgramData{
		IsSalaryRegisteredAndB2BVerified: "false",
		SalaryRegistrationCompletedDate:  "",
		LastVerifiedSalaryAt:             "",
	}

	// Get registration details which includes completion time
	regDetailsResp, err := c.salaryProgramClient.GetRegistrationDetails(ctx, &salaryProgramPb.GetRegistrationDetailsRequest{
		ActorId:  actorId,
		FlowType: salaryProgramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})

	if te := epifigrpc.RPCError(regDetailsResp, err); te != nil {
		logger.Error(ctx, "error calling salary program registration details service", zap.String(logger.ACTOR_ID, actorId), zap.Error(te))
		return nil, te
	}

	// Check if user is registered for salary program
	if regDetailsResp.GetRegistrationStatus() != salaryProgramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		// User is not registered or registration not completed, return default values
		return b2bSalaryProgramData, nil
	}

	// Check if user is B2B verified
	b2bVerificationResp, err := c.userClient.GetB2BSalaryProgramVerificationStatus(ctx, &userPb.GetB2BSalaryProgramVerificationStatusRequest{
		Identifier: &userPb.GetB2BSalaryProgramVerificationStatusRequest_ActorId{
			ActorId: actorId,
		},
	})

	if rpcErr := epifigrpc.RPCError(b2bVerificationResp, err); rpcErr != nil {
		logger.Error(ctx, "error calling B2B salary program verification status service", zap.String(logger.ACTOR_ID, actorId), zap.Error(rpcErr))
		return nil, rpcErr
	}

	// If user is not B2B verified, return default values (not registered)
	if !b2bVerificationResp.GetIsVerified() {
		// User is registered but not B2B verified, return default values
		return b2bSalaryProgramData, nil
	}

	// User is registered and B2B verified
	b2bSalaryProgramData.IsSalaryRegisteredAndB2BVerified = "true"

	// Set registration completion date from the actual registration details
	if regDetailsResp.GetRegistrationCompletionTime() != nil {
		b2bSalaryProgramData.SalaryRegistrationCompletedDate = GetTimeAsUnixTimestampString(regDetailsResp.GetRegistrationCompletionTime())
	}

	// Get activation details using the BEST activation kind to determine when the user's salary was last verified.
	// We query for the latest activation that is "currently active" (at the current time) to find:
	// 1. The "active_from" timestamp which can be used as when was the salary verified at .
	// 2. This timestamp will be used as "LastVerifiedSalaryAt" in the chatbot response
	// The BEST activation kind ensures we get the most optimal/recent activation for the user,
	// keeping the implementation consistent with the actual salary program flow.
	activationDetailsResp, err := c.salaryProgramClient.GetLatestActivationDetailsActiveAtTime(ctx, &salaryProgramPb.LatestActivationDetailsActiveAtTimeRequest{
		RegistrationId: regDetailsResp.GetRegistrationId(),
		ActiveAtTime:   timestampPb.Now(),
		ActivationKind: salaryProgramPb.SalaryProgramActivationKind_SALARY_PROGRAM_ACTIVATION_KIND_BEST,
	})

	if rpcErr := epifigrpc.RPCError(activationDetailsResp, err); rpcErr != nil {
		// If no activation details found, it's not an error - user might not have been activated yet
		if activationDetailsResp != nil && activationDetailsResp.GetStatus().IsRecordNotFound() {
			return b2bSalaryProgramData, nil
		}
		logger.Error(ctx, "error calling salary program activation details service", zap.String(logger.ACTOR_ID, actorId), zap.Error(rpcErr))
		return nil, rpcErr
	}

	// Extract the "active_from" timestamp which indicates when the user's salary verification became active.
	// This represents the last time their salary was successfully verified and activated in the system.
	if activationDetailsResp.GetActiveFrom() != nil {
		b2bSalaryProgramData.LastVerifiedSalaryAt = GetTimeAsUnixTimestampString(activationDetailsResp.GetActiveFrom())
	}

	return b2bSalaryProgramData, nil
}
