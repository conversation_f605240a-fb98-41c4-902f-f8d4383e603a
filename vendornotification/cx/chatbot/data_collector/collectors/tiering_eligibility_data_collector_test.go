package collectors

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/tiering/mocks"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

func TestTieringEligibilityDataCollector_CollectData(t *testing.T) {
	// Initialize logger for tests
	logger.Init(cfg.TestEnv)
	t.Parallel()

	type args struct {
		ctx         context.Context
		inputParams options.DataCollectorParams
	}

	tests := []struct {
		name    string
		args    args
		mocks   func(mockTieringClient *mocks.MockTieringClient)
		want    any
		wantErr bool
	}{
		{
			name: "failure - missing actor id",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "",
					QueriedTier: "Plus",
				},
			},
			mocks:   func(mockTieringClient *mocks.MockTieringClient) {},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure - missing tier parameter",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-123",
					QueriedTier: "",
				},
			},
			mocks:   func(mockTieringClient *mocks.MockTieringClient) {},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure - invalid tier parameter",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-123",
					QueriedTier: "InvalidTier",
				},
			},
			mocks:   func(mockTieringClient *mocks.MockTieringClient) {},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure - GetTieringPitchV2 returns error",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-pitch-error",
					QueriedTier: "Plus",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-pitch-error",
					},
				).Return(nil, errors.New("tiering pitch service unavailable"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure - EvaluateTierForActor returns error",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-error",
					QueriedTier: "Plus",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-error",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:              rpc.StatusOk(),
					CurrentTier:         tieringExtPb.Tier_TIER_FI_BASIC,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{},
				}, nil)

				mockTieringClient.EXPECT().EvaluateTierForActor(
					gomock.Any(),
					&tieringPb.EvaluateTierForActorRequest{
						ActorId: "test-actor-error",
					},
				).Return(nil, errors.New("tiering service unavailable"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure - user has unspecified evaluated tier",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-unspecified",
					QueriedTier: "Plus",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-unspecified",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:              rpc.StatusOk(),
					CurrentTier:         tieringExtPb.Tier_TIER_FI_BASIC,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{},
				}, nil)

				mockTieringClient.EXPECT().EvaluateTierForActor(
					gomock.Any(),
					&tieringPb.EvaluateTierForActorRequest{
						ActorId: "test-actor-unspecified",
					},
				).Return(&tieringPb.EvaluateTierForActorResponse{
					Status:        rpc.StatusOk(),
					EvaluatedTier: tieringExtPb.Tier_TIER_UNSPECIFIED,
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success - user has salary tier but requests Plus tier - not eligible",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-salary-requests-plus",
					QueriedTier: "Plus",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-salary-requests-plus",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:              rpc.StatusOk(),
					CurrentTier:         tieringExtPb.Tier_TIER_FI_SALARY,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{},
				}, nil)

				mockTieringClient.EXPECT().EvaluateTierForActor(
					gomock.Any(),
					&tieringPb.EvaluateTierForActorRequest{
						ActorId: "test-actor-salary-requests-plus",
					},
				).Return(&tieringPb.EvaluateTierForActorResponse{
					Status:        rpc.StatusOk(),
					EvaluatedTier: tieringExtPb.Tier_TIER_FI_SALARY, // Salary tier
				}, nil)
			},
			want: &TieringEligibilityData{
				IsEligible:   "false",
				IsInCooldown: "false",
			},
			wantErr: false,
		},
		{
			name: "success - user has salary basic tier but requests Infinite tier - not eligible",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-salary-basic-requests-infinite",
					QueriedTier: "Infinite",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-salary-basic-requests-infinite",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:              rpc.StatusOk(),
					CurrentTier:         tieringExtPb.Tier_TIER_FI_SALARY_BASIC,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{},
				}, nil)

				mockTieringClient.EXPECT().EvaluateTierForActor(
					gomock.Any(),
					&tieringPb.EvaluateTierForActorRequest{
						ActorId: "test-actor-salary-basic-requests-infinite",
					},
				).Return(&tieringPb.EvaluateTierForActorResponse{
					Status:        rpc.StatusOk(),
					EvaluatedTier: tieringExtPb.Tier_TIER_FI_SALARY_BASIC, // Salary basic tier
				}, nil)
			},
			want: &TieringEligibilityData{
				IsEligible:   "false",
				IsInCooldown: "false",
			},
			wantErr: false,
		},
		{
			name: "success - user has salary tier but requests Prime tier - not eligible",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-salary-requests-prime",
					QueriedTier: "Prime",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-salary-requests-prime",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:              rpc.StatusOk(),
					CurrentTier:         tieringExtPb.Tier_TIER_FI_SALARY,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{},
				}, nil)

				mockTieringClient.EXPECT().EvaluateTierForActor(
					gomock.Any(),
					&tieringPb.EvaluateTierForActorRequest{
						ActorId: "test-actor-salary-requests-prime",
					},
				).Return(&tieringPb.EvaluateTierForActorResponse{
					Status:        rpc.StatusOk(),
					EvaluatedTier: tieringExtPb.Tier_TIER_FI_SALARY, // Salary tier
				}, nil)
			},
			want: &TieringEligibilityData{
				IsEligible:   "false",
				IsInCooldown: "false",
			},
			wantErr: false,
		},
		{
			name: "success - user eligible for Plus tier (evaluated tier is Infinite)",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-eligible",
					QueriedTier: "Plus",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-eligible",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:              rpc.StatusOk(),
					CurrentTier:         tieringExtPb.Tier_TIER_FI_INFINITE,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{},
				}, nil)

				mockTieringClient.EXPECT().EvaluateTierForActor(
					gomock.Any(),
					&tieringPb.EvaluateTierForActorRequest{
						ActorId: "test-actor-eligible",
					},
				).Return(&tieringPb.EvaluateTierForActorResponse{
					Status:        rpc.StatusOk(),
					EvaluatedTier: tieringExtPb.Tier_TIER_FI_INFINITE, // Internal: 1000 >= Plus Internal: 100
				}, nil)
			},
			want: &TieringEligibilityData{
				IsEligible:   "true",
				IsInCooldown: "false",
			},
			wantErr: false,
		},
		{
			name: "success - user not eligible for Infinite tier (evaluated tier is Plus)",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-not-eligible",
					QueriedTier: "Infinite",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-not-eligible",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:              rpc.StatusOk(),
					CurrentTier:         tieringExtPb.Tier_TIER_FI_PLUS,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{},
				}, nil)

				mockTieringClient.EXPECT().EvaluateTierForActor(
					gomock.Any(),
					&tieringPb.EvaluateTierForActorRequest{
						ActorId: "test-actor-not-eligible",
					},
				).Return(&tieringPb.EvaluateTierForActorResponse{
					Status:        rpc.StatusOk(),
					EvaluatedTier: tieringExtPb.Tier_TIER_FI_PLUS, // Internal: 100 < Infinite Internal: 1000
				}, nil)
			},
			want: &TieringEligibilityData{
				IsEligible:   "false",
				IsInCooldown: "false",
			},
			wantErr: false,
		},
		{
			name: "success - user eligible for Prime tier (evaluated tier is Prime)",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-prime-eligible",
					QueriedTier: "Prime",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-prime-eligible",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:              rpc.StatusOk(),
					CurrentTier:         tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{},
				}, nil)

				mockTieringClient.EXPECT().EvaluateTierForActor(
					gomock.Any(),
					&tieringPb.EvaluateTierForActorRequest{
						ActorId: "test-actor-prime-eligible",
					},
				).Return(&tieringPb.EvaluateTierForActorResponse{
					Status:        rpc.StatusOk(),
					EvaluatedTier: tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3, // Internal: 1290 >= Prime Internal: 1290
				}, nil)
			},
			want: &TieringEligibilityData{
				IsEligible:   "true",
				IsInCooldown: "false",
			},
			wantErr: false,
		},
		{
			name: "success - user not eligible for Salary tier (evaluated tier is Basic)",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-salary-not-eligible",
					QueriedTier: "Salary",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-salary-not-eligible",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:              rpc.StatusOk(),
					CurrentTier:         tieringExtPb.Tier_TIER_FI_BASIC,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{},
				}, nil)

				mockTieringClient.EXPECT().EvaluateTierForActor(
					gomock.Any(),
					&tieringPb.EvaluateTierForActorRequest{
						ActorId: "test-actor-salary-not-eligible",
					},
				).Return(&tieringPb.EvaluateTierForActorResponse{
					Status:        rpc.StatusOk(),
					EvaluatedTier: tieringExtPb.Tier_TIER_FI_BASIC, // Internal: 10 < Salary Internal: 2000
				}, nil)
			},
			want: &TieringEligibilityData{
				IsEligible:   "false",
				IsInCooldown: "false",
			},
			wantErr: false,
		},
		{
			name: "success - user eligible for Salary tier (evaluated tier is Salary)",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-salary-eligible",
					QueriedTier: "Salary",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-salary-eligible",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:              rpc.StatusOk(),
					CurrentTier:         tieringExtPb.Tier_TIER_FI_SALARY,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{},
				}, nil)

				mockTieringClient.EXPECT().EvaluateTierForActor(
					gomock.Any(),
					&tieringPb.EvaluateTierForActorRequest{
						ActorId: "test-actor-salary-eligible",
					},
				).Return(&tieringPb.EvaluateTierForActorResponse{
					Status:        rpc.StatusOk(),
					EvaluatedTier: tieringExtPb.Tier_TIER_FI_SALARY, // User with TIER_FI_SALARY is eligible for "Salary"
				}, nil)
			},
			want: &TieringEligibilityData{
				IsEligible:   "true",
				IsInCooldown: "false",
			},
			wantErr: false,
		},
		{
			name: "success - user eligible for Salary tier (evaluated tier is Salary Basic)",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-salary-basic-eligible-for-salary",
					QueriedTier: "Salary",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-salary-basic-eligible-for-salary",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:              rpc.StatusOk(),
					CurrentTier:         tieringExtPb.Tier_TIER_FI_SALARY_BASIC,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{},
				}, nil)

				mockTieringClient.EXPECT().EvaluateTierForActor(
					gomock.Any(),
					&tieringPb.EvaluateTierForActorRequest{
						ActorId: "test-actor-salary-basic-eligible-for-salary",
					},
				).Return(&tieringPb.EvaluateTierForActorResponse{
					Status:        rpc.StatusOk(),
					EvaluatedTier: tieringExtPb.Tier_TIER_FI_SALARY_BASIC, // User with TIER_FI_SALARY_BASIC is eligible for "Salary"
				}, nil)
			},
			want: &TieringEligibilityData{
				IsEligible:   "true",
				IsInCooldown: "false",
			},
			wantErr: false,
		},
		{
			name: "success - user in cooldown but requests current tier - eligible",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-in-cooldown-current-tier",
					QueriedTier: "Plus",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-in-cooldown-current-tier",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_PLUS, // Current tier is Plus
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: false, // User is in cooldown for higher tier
						},
					},
				}, nil)

				mockTieringClient.EXPECT().EvaluateTierForActor(
					gomock.Any(),
					&tieringPb.EvaluateTierForActorRequest{
						ActorId: "test-actor-in-cooldown-current-tier",
					},
				).Return(&tieringPb.EvaluateTierForActorResponse{
					Status:        rpc.StatusOk(),
					EvaluatedTier: tieringExtPb.Tier_TIER_FI_PLUS,
				}, nil)
			},
			want: &TieringEligibilityData{
				IsEligible:   "true",
				IsInCooldown: "true",
			},
			wantErr: false,
		},
		{
			name: "success - user in cooldown and requests different tier - not eligible",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-in-cooldown-different-tier",
					QueriedTier: "Infinite",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-in-cooldown-different-tier",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_PLUS, // Current tier is Plus, requesting Infinite
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: false, // User is in cooldown for higher tier
						},
					},
				}, nil)
				// No EvaluateTierForActor call expected since we return early due to cooldown
			},
			want: &TieringEligibilityData{
				IsEligible:   "false",
				IsInCooldown: "true",
			},
			wantErr: false,
		},
		{
			name: "success - user not in cooldown (movement to higher tier allowed)",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId:     "test-actor-not-in-cooldown",
					QueriedTier: "Plus",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-not-in-cooldown",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_BASIC,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
							IsMovementAllowed: true, // Movement allowed
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: true, // Movement allowed
						},
					},
				}, nil)

				mockTieringClient.EXPECT().EvaluateTierForActor(
					gomock.Any(),
					&tieringPb.EvaluateTierForActorRequest{
						ActorId: "test-actor-not-in-cooldown",
					},
				).Return(&tieringPb.EvaluateTierForActorResponse{
					Status:        rpc.StatusOk(),
					EvaluatedTier: tieringExtPb.Tier_TIER_FI_PLUS,
				}, nil)
			},
			want: &TieringEligibilityData{
				IsEligible:   "true",
				IsInCooldown: "false",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockTieringClient := mocks.NewMockTieringClient(ctrl)
			tt.mocks(mockTieringClient)

			c := NewTieringEligibilityDataCollector(mockTieringClient)
			got, err := c.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.wantErr {
				require.Error(t, err)
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.want, got)
			}
		})
	}
}
