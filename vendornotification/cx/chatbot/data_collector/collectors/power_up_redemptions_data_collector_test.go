package collectors

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	casperPb "github.com/epifi/gamma/api/casper"
	casperMocks "github.com/epifi/gamma/api/casper/mocks"
	redemptionPb "github.com/epifi/gamma/api/casper/redemption"
	redemptionMocks "github.com/epifi/gamma/api/casper/redemption/mocks"
	"github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

const (
	testActorIdPowerUp = "test-actor-power-up-123"
	testLimit          = 5
)

type mockPowerUpDependencies struct {
	mockOfferRedemptionClient *redemptionMocks.MockOfferRedemptionServiceClient
	mockOfferCatalogClient    *casperMocks.MockOfferCatalogServiceClient
	mockGenConf               *genconf.Config
}

func newPowerUpDataCollectorWithMocks(t *testing.T) (*PowerUpRedemptionsDataCollector, *mockPowerUpDependencies) {
	ctrl := gomock.NewController(t)

	// Create a real genconf.Config for testing
	gconf, _ := genconf.NewConfig()
	// Set PowerUpRedemptionsLimit using SetPowerUpRedemptionsLimit method
	err := gconf.Nugget().SetPowerUpRedemptionsLimit(testLimit, false, nil)
	require.NoError(t, err)

	md := &mockPowerUpDependencies{
		mockOfferRedemptionClient: redemptionMocks.NewMockOfferRedemptionServiceClient(ctrl),
		mockOfferCatalogClient:    casperMocks.NewMockOfferCatalogServiceClient(ctrl),
		mockGenConf:               gconf,
	}

	dataCollector := NewPowerUpRedemptionsDataCollector(
		md.mockOfferRedemptionClient,
		md.mockOfferCatalogClient,
		md.mockGenConf,
	)

	t.Cleanup(func() {
		ctrl.Finish()
	})

	return dataCollector, md
}

func TestPowerUpRedemptionsDataCollector_CollectData(t *testing.T) {
	// Initialize logger for testing
	logger.Init(cfg.TestEnv)
	t.Parallel()

	type args struct {
		ctx         context.Context
		inputParams options.DataCollectorParams
	}

	tests := []struct {
		name           string
		args           args
		setupMocks     func(*mockPowerUpDependencies)
		expectedResult interface{}
		expectedError  string
	}{
		{
			name: "success_with_redemptions",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdPowerUp,
				},
			},
			setupMocks: func(md *mockPowerUpDependencies) {
				// Configuration is already set in the setup function

				// Mock GetRedeemedOffersForActor response
				redeemedOffers := []*redemptionPb.RedeemedOffer{
					{
						RedemptionRequestId: "redemption_req_001",
						OfferId:             "offer_001",
						RedemptionState:     1, // SUCCESS state
					},
					{
						RedemptionRequestId: "redemption_req_002",
						OfferId:             "offer_002",
						RedemptionState:     2, // PENDING state
					},
				}

				md.mockOfferRedemptionClient.EXPECT().
					GetRedeemedOffersForActor(gomock.Any(), &redemptionPb.GetRedeemedOffersForActorRequest{
						ActorId: testActorIdPowerUp,
						PageContext: &rpcPb.PageContextRequest{
							PageSize: uint32(testLimit),
						},
						Filters: &redemptionPb.GetRedeemedOffersForActorRequest_Filters{
							OfferType: casperPb.OfferType_POWER_UP,
						},
					}).Return(&redemptionPb.GetRedeemedOffersForActorResponse{
					Status:         &rpcPb.Status{Code: 0},
					RedeemedOffers: redeemedOffers,
				}, nil)

				// Mock GetBulkOfferDetailsByIds response
				offers := []*casperPb.Offer{
					{
						Id:   "offer_001",
						Name: "Swiggy 10% Cashback",
					},
					{
						Id:   "offer_002",
						Name: "Zomato 15% Cashback",
					},
				}

				md.mockOfferCatalogClient.EXPECT().
					GetBulkOfferDetailsByIds(gomock.Any(), &casperPb.GetBulkOfferDetailsByIdsRequest{
						OfferIds: []string{"offer_001", "offer_002"},
					}).Return(&casperPb.GetBulkOfferDetailsByIdsResponse{
					Status: &rpcPb.Status{Code: 0},
					Offers: offers,
				}, nil)
			},
			expectedResult: &PowerUpRedemptionsData{
				PowerUpRedemptions: []PowerUpRedemptionData{
					{
						ID:     "redemption_req_001",
						Name:   "Swiggy 10% Cashback",
						Status: "OFFER_REDEMPTION_INITIATED",
					},
					{
						ID:     "redemption_req_002",
						Name:   "Zomato 15% Cashback",
						Status: "2",
					},
				},
			},
		},
		{
			name: "success_empty_redemptions",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdPowerUp,
				},
			},
			setupMocks: func(md *mockPowerUpDependencies) {
				// Configuration is already set in the setup function

				// Mock GetRedeemedOffersForActor response with empty results
				md.mockOfferRedemptionClient.EXPECT().
					GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).
					Return(&redemptionPb.GetRedeemedOffersForActorResponse{
						Status:         &rpcPb.Status{Code: 0},
						RedeemedOffers: []*redemptionPb.RedeemedOffer{},
					}, nil)
			},
			expectedResult: &PowerUpRedemptionsData{
				PowerUpRedemptions: []PowerUpRedemptionData{},
			},
		},
		{
			name: "error_offer_details_rpc_failure",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdPowerUp,
				},
			},
			setupMocks: func(md *mockPowerUpDependencies) {
				// Configuration is already set in the setup function

				// Mock GetRedeemedOffersForActor response
				redeemedOffers := []*redemptionPb.RedeemedOffer{
					{
						RedemptionRequestId: "redemption_req_001",
						OfferId:             "offer_001",
						RedemptionState:     1, // SUCCESS state
					},
				}

				md.mockOfferRedemptionClient.EXPECT().
					GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).
					Return(&redemptionPb.GetRedeemedOffersForActorResponse{
						Status:         &rpcPb.Status{Code: 0},
						RedeemedOffers: redeemedOffers,
					}, nil)

				// Mock GetBulkOfferDetailsByIds failure
				md.mockOfferCatalogClient.EXPECT().
					GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).
					Return(&casperPb.GetBulkOfferDetailsByIdsResponse{
						Status: &rpcPb.Status{Code: 1},
					}, nil)
			},
			expectedError: "failed to get offers details",
		},
		{
			name: "error_empty_actor_id",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "",
				},
			},
			setupMocks:    func(md *mockPowerUpDependencies) {},
			expectedError: "actorId is required",
		},
		{
			name: "error_redemption_rpc_failure",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdPowerUp,
				},
			},
			setupMocks: func(md *mockPowerUpDependencies) {
				// Configuration is already set in the setup function

				// Mock GetRedeemedOffersForActor RPC error
				md.mockOfferRedemptionClient.EXPECT().
					GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("RPC connection failed"))
			},
			expectedError: "failed to get redeemed offers for actor",
		},
		{
			name: "error_redemption_rpc_status_failure",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdPowerUp,
				},
			},
			setupMocks: func(md *mockPowerUpDependencies) {
				// Configuration is already set in the setup function

				// Mock GetRedeemedOffersForActor status failure
				md.mockOfferRedemptionClient.EXPECT().
					GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).
					Return(&redemptionPb.GetRedeemedOffersForActorResponse{
						Status: &rpcPb.Status{Code: 1},
					}, nil)
			},
			expectedError: "failed to get redeemed offers for actor",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			dataCollector, md := newPowerUpDataCollectorWithMocks(t)
			tt.setupMocks(md)

			result, err := dataCollector.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.expectedError != "" {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.expectedError)
				require.Nil(t, result)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expectedResult, result)
			}
		})
	}
}
