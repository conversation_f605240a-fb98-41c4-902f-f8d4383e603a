package collectors

import (
	"context"
	"fmt"
	"strconv"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	beMoney "github.com/epifi/be-common/pkg/money"
	accountsPb "github.com/epifi/gamma/api/accounts"
	depositPb "github.com/epifi/gamma/api/deposit"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type DepositDataCollector struct {
	depositClient depositPb.DepositClient
	paymentClient paymentPb.PaymentClient
	piClient      piPb.PiClient
	config        *genconf.Config
}

func NewDepositDataCollector(
	depositClient depositPb.DepositClient,
	paymentClient paymentPb.PaymentClient,
	piClient piPb.PiClient,
	config *genconf.Config,
) *DepositDataCollector {
	return &DepositDataCollector{
		depositClient: depositClient,
		paymentClient: paymentClient,
		piClient:      piClient,
		config:        config,
	}
}

// DepositInfo represents comprehensive information about a deposit (FD/SD/RD)
type DepositInfo struct {
	AccountID               string `json:"account_id"`
	DepositName             string `json:"deposit_name"`
	Status                  string `json:"status"` // Raw deposit state
	IsDepositLinkedToCC     string `json:"is_deposit_linked_to_cc"`
	IsDepositAutoRenewed    string `json:"is_deposit_auto_renewed"`
	InterestPayoutFrequency string `json:"interest_payout_frequency"`
	RunningBalance          string `json:"running_balance"`
	MaturityDate            string `json:"maturity_date,omitempty"`
	UTR                     string `json:"utr,omitempty"`
	IsTDSDeducted           string `json:"is_tds_deducted"`
	TDSAmount               string `json:"tds_amount,omitempty"`
}

// DepositData represents all deposit-related data for the chatbot
type DepositData struct {
	DepositList []DepositInfo `json:"deposit_list"`
}

// CollectData fetches deposit-related information for the nugget bot
func (c *DepositDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	// Get the deposit type from request parameters
	depositType := inputParams.GetDepositType()
	if depositType == "" {
		return nil, errors.New("deposit_type parameter is required")
	}

	// Convert string to deposit Type enum
	depositTypeEnum, err := c.parseDepositType(depositType)
	if err != nil {
		return nil, errors.Wrap(err, "invalid deposit_type parameter")
	}

	// Get list of deposits for the specific type requested
	depositList, err := c.getDepositList(ctx, actorId, depositTypeEnum)
	if err != nil {
		logger.Error(ctx, "error getting deposit list",
			zap.String(logger.ACTOR_ID, actorId),
			zap.String("deposit_type", depositType),
			zap.Error(err))
		return nil, errors.Wrap(err, "failed to get deposit list")
	}

	// Initialize response data
	depositData := &DepositData{
		DepositList: depositList,
	}

	return depositData, nil
}

// getDepositList retrieves deposits for the user of the specified type.
// Returns up to maxDepositsToProcess deposits to ensure manageable chatbot responses.
func (c *DepositDataCollector) getDepositList(ctx context.Context, actorId string, depositType accountsPb.Type) ([]DepositInfo, error) {
	// Request deposit accounts of the specific type requested
	resp, err := c.depositClient.ListDepositAccounts(ctx, &depositPb.ListDepositAccountsRequest{
		ActorId: actorId,
		States:  c.getAllStatesExceptUnspecified(),
		Types: []accountsPb.Type{
			depositType, // Only request the specific type
		},
		Provenances: c.getAllProvenancesExceptUnspecified(),
	})

	if err := epifigrpc.RPCError(resp, err); err != nil {
		return nil, errors.Wrap(err, "failed to list deposit accounts")
	}

	depositList := []DepositInfo{}

	// Process deposits up to the maximum limit to keep chatbot responses manageable
	// Send max MaxDepositsToProcess deposits for the user to enquire, if the deposit that the user want to enquire about isn't there bot is going to escalate to agent
	// TODO: Support pagination support in nugget chat bot
	accountsToProcess := resp.GetAccounts()
	maxDeposits := c.config.Nugget().MaxDepositsToProcess()
	if len(accountsToProcess) > maxDeposits {
		accountsToProcess = accountsToProcess[:maxDeposits]
	}

	for _, account := range accountsToProcess {
		depositInfo := DepositInfo{
			AccountID:               account.GetId(),
			DepositName:             account.GetName(),
			Status:                  account.GetState().String(),
			IsDepositLinkedToCC:     strconv.FormatBool(c.getDepositCCLinkage(account)),
			IsDepositAutoRenewed:    strconv.FormatBool(account.GetRenewInfo().GetIsAutoRenewable()),
			InterestPayoutFrequency: account.GetSchemeCode().String(),
			IsTDSDeducted:           "false",
		}

		if account.GetRunningBalance() != nil {
			depositInfo.RunningBalance = beMoney.ToDisplayStringWithPrecision(account.GetRunningBalance(), 2)
		}

		// Set maturity date as epoch timestamp string
		if account.GetMaturityDate() != nil {
			depositInfo.MaturityDate = fmt.Sprintf("%d", account.GetMaturityDate().AsTime().Unix())
		}

		c.populateWithdrawalInfo(ctx, account, &depositInfo)

		// Check for TDS in closure info (if available)
		if account.GetClosureInfo() != nil && account.GetClosureInfo().GetTdsAmount() != nil {
			if beMoney.IsPositive(account.GetClosureInfo().GetTdsAmount()) {
				depositInfo.IsTDSDeducted = "true"
				depositInfo.TDSAmount = beMoney.ToDisplayStringWithPrecision(account.GetClosureInfo().GetTdsAmount(), 2)
			}
		}

		depositList = append(depositList, depositInfo)
	}

	return depositList, nil
}

// getAllStatesExceptUnspecified returns all deposit states except unspecified
// by iterating over the DepositState_value map and filtering out unspecified
func (c *DepositDataCollector) getAllStatesExceptUnspecified() []depositPb.DepositState {
	var states []depositPb.DepositState
	for key, value := range depositPb.DepositState_value {
		if key != depositPb.DepositState_STATE_UNSPECIFIED.String() {
			states = append(states, depositPb.DepositState(value))
		}
	}
	return states
}

// getAllProvenancesExceptUnspecified returns all deposit account provenances except unspecified
// by iterating over the DepositAccountProvenance_value map and filtering out unspecified
func (c *DepositDataCollector) getAllProvenancesExceptUnspecified() []depositPb.DepositAccountProvenance {
	var provenances []depositPb.DepositAccountProvenance
	for key, value := range depositPb.DepositAccountProvenance_value {
		if key != depositPb.DepositAccountProvenance_DEPOSIT_ACCOUNT_PROVENANCE_UNSPECIFIED.String() {
			provenances = append(provenances, depositPb.DepositAccountProvenance(value))
		}
	}

	return provenances
}

// getDepositCCLinkage checks if this specific deposit is linked to credit card
func (c *DepositDataCollector) getDepositCCLinkage(account *depositPb.DepositAccount) bool {
	if account.GetProvenance() == depositPb.DepositAccountProvenance_CREDIT_CARD ||
		account.GetProvenance() == depositPb.DepositAccountProvenance_CREDIT_CARD_NON_SA {
		return true
	}
	return false
}

// populateWithdrawalInfo populates withdrawal information using payment transactions
func (c *DepositDataCollector) populateWithdrawalInfo(ctx context.Context, account *depositPb.DepositAccount, depositInfo *DepositInfo) {
	// Get payment instrument for this deposit account
	piRes, err := c.piClient.GetPi(ctx, &piPb.GetPiRequest{
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.GetPiRequest_AccountRequestParams_{AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
			ActualAccountNumber: account.GetAccountNumber(),
			IfscCode:            account.GetIfscCode(),
		}},
	})
	if err := epifigrpc.RPCError(piRes, err); err != nil {
		logger.WarnWithCtx(ctx, "could not get payment instrument for deposit",
			zap.String(logger.ACTOR_ID, account.GetActorId()),
			zap.String("account_id", account.GetId()),
			zap.Error(err))
		return
	}

	// Get debit transaction (withdrawal/closure) for this deposit
	// There should be only one debit transaction for a deposit account
	debitTxnRes, err := c.paymentClient.GetTxnsByPi(ctx, &paymentPb.GetTxnsByPiRequest{
		PiFrom:        piRes.GetPaymentInstrument().GetId(),
		FromTimestamp: account.GetCreatedAt(),
		ToTimestamp:   timestamppb.Now(),
		SortBy:        paymentPb.TransactionFieldMask_CREATED_AT,
		SortDesc:      true,
		PageSize:      1, // Only one debit transaction expected
		Offset:        0,
		Statuses:      []paymentPb.TransactionStatus{paymentPb.TransactionStatus_SUCCESS},
	})

	if err := epifigrpc.RPCError(debitTxnRes, err); err != nil {
		logger.WarnWithCtx(ctx, "could not get debit transactions for deposit",
			zap.String(logger.ACTOR_ID, account.GetActorId()),
			zap.String("account_id", account.GetId()),
			zap.Error(err))
		return
	}

	// Extract UTR from the transaction - assume UTR is present
	txns := debitTxnRes.GetTransactions()
	if len(txns) > 0 {
		txn := txns[0]
		depositInfo.UTR = txn.GetUtr()
	}
}

// parseDepositType converts the string deposit type to Type enum using protobuf Type_value map
func (c *DepositDataCollector) parseDepositType(depositType string) (accountsPb.Type, error) {
	// Use protobuf generated Type_value map to convert string to enum value
	if enumValue, exists := accountsPb.Type_value[depositType]; exists {
		return accountsPb.Type(enumValue), nil
	}
	return accountsPb.Type_TYPE_UNSPECIFIED, fmt.Errorf("unsupported deposit type: %s, supported values are FIXED_DEPOSIT, SMART_DEPOSIT, RECURRING_DEPOSIT", depositType)
}
