package collectors_test

import (
	"context"
	"errors"
	"flag"
	"os"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	bankcustPb "github.com/epifi/gamma/api/bankcust"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	complianceMocks "github.com/epifi/gamma/api/bankcust/compliance/mocks"
	bankcustMocks "github.com/epifi/gamma/api/bankcust/mocks"
	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/collectors"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

// TestMain initializes test components, runs tests and exits
func TestMain(m *testing.M) {
	flag.Parse()

	// Setup logger
	logger.Init("test")

	exitCode := m.Run()
	os.Exit(exitCode)
}

var (
	testActorId  = "test-actor-id"
	errTest      = errors.New("test error")
	testVendorId = "test-vendor-id"
)

type mockServices struct {
	bankCustClient   *bankcustMocks.MockBankCustomerServiceClient
	complianceClient *complianceMocks.MockComplianceClient
}

func getTestKycDataCollectorWithMock(t *testing.T) (*collectors.KycBotDataCollector, *mockServices) {
	ctrl := gomock.NewController(t)

	mockBankCustClient := bankcustMocks.NewMockBankCustomerServiceClient(ctrl)
	mockComplianceClient := complianceMocks.NewMockComplianceClient(ctrl)

	collector := collectors.NewKycDataCollector(mockBankCustClient, mockComplianceClient)

	mocks := &mockServices{
		bankCustClient:   mockBankCustClient,
		complianceClient: mockComplianceClient,
	}

	return collector, mocks
}

func TestKycBotDataCollector_CollectData(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx         context.Context
		inputParams options.DataCollectorParams
	}

	tests := []struct {
		name    string
		args    args
		mocks   func(mocks *mockServices)
		want    any
		wantErr bool
	}{
		{
			name: "missing actor id",
			args: args{
				ctx:         context.Background(),
				inputParams: options.DataCollectorParams{},
			},
			mocks:   nil,
			want:    nil,
			wantErr: true,
		},
		{
			name: "bank customer record not found",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcustPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{
						ActorId: testActorId,
					},
				}).Return(&bankcustPb.GetBankCustomerResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "bank customer nil, compliance service success",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcustPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{
						ActorId: testActorId,
					},
				}).Return(&bankcustPb.GetBankCustomerResponse{
					Status:       rpc.StatusOk(),
					BankCustomer: nil,
				}, nil)

				mocks.complianceClient.EXPECT().GetPeriodicKYCDetail(gomock.Any(), &compliancePb.GetPeriodicKYCDetailRequest{
					ActorId:     testActorId,
					FetchLatest: false,
				}).Return(&compliancePb.GetPeriodicKYCDetailResponse{
					Status: rpc.StatusOk(),
					PeriodicKYCDetail: &compliancePb.PeriodicKYCDetail{
						KYCComplianceStatus: compliancePb.KYCComplianceStatus_KYC_COMPLIANCE_STATUS_DUE,
					},
				}, nil)
			},
			want: &collectors.KycBotData{
				ComplianceStatus: "KYC_COMPLIANCE_STATUS_DUE",
			},
			wantErr: false,
		},
		{
			name: "kyc info nil, compliance service success",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcustPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{
						ActorId: testActorId,
					},
				}).Return(&bankcustPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcustPb.BankCustomer{
						VendorCustomerId: testVendorId,
						KycInfo:          nil,
					},
				}, nil)

				mocks.complianceClient.EXPECT().GetPeriodicKYCDetail(gomock.Any(), &compliancePb.GetPeriodicKYCDetailRequest{
					ActorId:     testActorId,
					FetchLatest: false,
				}).Return(&compliancePb.GetPeriodicKYCDetailResponse{
					Status: rpc.StatusOk(),
					PeriodicKYCDetail: &compliancePb.PeriodicKYCDetail{
						KYCComplianceStatus: compliancePb.KYCComplianceStatus_KYC_COMPLIANCE_STATUS_DUE,
					},
				}, nil)
			},
			want: &collectors.KycBotData{
				ComplianceStatus: "KYC_COMPLIANCE_STATUS_DUE",
			},
			wantErr: false,
		},
		{
			name: "kyc level unspecified, compliance service success",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcustPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{
						ActorId: testActorId,
					},
				}).Return(&bankcustPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcustPb.BankCustomer{
						VendorCustomerId: testVendorId,
						KycInfo: &bankcustPb.KYCInfo{
							KycLevel: kycPb.KYCLevel_UNSPECIFIED,
						},
					},
				}, nil)

				mocks.complianceClient.EXPECT().GetPeriodicKYCDetail(gomock.Any(), &compliancePb.GetPeriodicKYCDetailRequest{
					ActorId:     testActorId,
					FetchLatest: false,
				}).Return(&compliancePb.GetPeriodicKYCDetailResponse{
					Status: rpc.StatusOk(),
					PeriodicKYCDetail: &compliancePb.PeriodicKYCDetail{
						KYCComplianceStatus: compliancePb.KYCComplianceStatus_KYC_COMPLIANCE_STATUS_COMPLIED,
					},
				}, nil)
			},
			want: &collectors.KycBotData{
				ComplianceStatus: "KYC_COMPLIANCE_STATUS_COMPLIED",
			},
			wantErr: false,
		},
		{
			name: "compliance service error",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcustPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{
						ActorId: testActorId,
					},
				}).Return(&bankcustPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcustPb.BankCustomer{
						VendorCustomerId: testVendorId,
						KycInfo: &bankcustPb.KYCInfo{
							KycLevel: kycPb.KYCLevel_FULL_KYC,
						},
					},
				}, nil)

				mocks.complianceClient.EXPECT().GetPeriodicKYCDetail(gomock.Any(), &compliancePb.GetPeriodicKYCDetailRequest{
					ActorId:     testActorId,
					FetchLatest: false,
				}).Return(nil, errTest)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "compliance record not found",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcustPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{
						ActorId: testActorId,
					},
				}).Return(&bankcustPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcustPb.BankCustomer{
						VendorCustomerId: testVendorId,
						KycInfo: &bankcustPb.KYCInfo{
							KycLevel: kycPb.KYCLevel_FULL_KYC,
						},
					},
				}, nil)

				mocks.complianceClient.EXPECT().GetPeriodicKYCDetail(gomock.Any(), &compliancePb.GetPeriodicKYCDetailRequest{
					ActorId:     testActorId,
					FetchLatest: false,
				}).Return(&compliancePb.GetPeriodicKYCDetailResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "compliance status unspecified",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcustPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{
						ActorId: testActorId,
					},
				}).Return(&bankcustPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcustPb.BankCustomer{
						VendorCustomerId: testVendorId,
						KycInfo: &bankcustPb.KYCInfo{
							KycLevel: kycPb.KYCLevel_FULL_KYC,
						},
					},
				}, nil)

				mocks.complianceClient.EXPECT().GetPeriodicKYCDetail(gomock.Any(), &compliancePb.GetPeriodicKYCDetailRequest{
					ActorId:     testActorId,
					FetchLatest: false,
				}).Return(&compliancePb.GetPeriodicKYCDetailResponse{
					Status: rpc.StatusOk(),
					PeriodicKYCDetail: &compliancePb.PeriodicKYCDetail{
						KYCComplianceStatus: compliancePb.KYCComplianceStatus_KYC_COMPLIANCE_STATUS_UNSPECIFIED,
					},
				}, nil)
			},
			want: &collectors.KycBotData{
				KycLevel: "FULL_KYC",
			},
			wantErr: false,
		},
		{
			name: "compliance data nil",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcustPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{
						ActorId: testActorId,
					},
				}).Return(&bankcustPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcustPb.BankCustomer{
						VendorCustomerId: testVendorId,
						KycInfo: &bankcustPb.KYCInfo{
							KycLevel: kycPb.KYCLevel_FULL_KYC,
						},
					},
				}, nil)

				mocks.complianceClient.EXPECT().GetPeriodicKYCDetail(gomock.Any(), &compliancePb.GetPeriodicKYCDetailRequest{
					ActorId:     testActorId,
					FetchLatest: false,
				}).Return(&compliancePb.GetPeriodicKYCDetailResponse{
					Status:            rpc.StatusOk(),
					PeriodicKYCDetail: nil,
				}, nil)
			},
			want: &collectors.KycBotData{
				KycLevel: "FULL_KYC",
			},
			wantErr: false,
		},
		{
			name: "success with both values",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorId,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcustPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{
						ActorId: testActorId,
					},
				}).Return(&bankcustPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcustPb.BankCustomer{
						VendorCustomerId: testVendorId,
						KycInfo: &bankcustPb.KYCInfo{
							KycLevel: kycPb.KYCLevel_FULL_KYC,
						},
					},
				}, nil)

				mocks.complianceClient.EXPECT().GetPeriodicKYCDetail(gomock.Any(), &compliancePb.GetPeriodicKYCDetailRequest{
					ActorId:     testActorId,
					FetchLatest: false,
				}).Return(&compliancePb.GetPeriodicKYCDetailResponse{
					Status: rpc.StatusOk(),
					PeriodicKYCDetail: &compliancePb.PeriodicKYCDetail{
						KYCComplianceStatus: compliancePb.KYCComplianceStatus_KYC_COMPLIANCE_STATUS_DUE,
					},
				}, nil)
			},
			want: &collectors.KycBotData{
				ComplianceStatus: "KYC_COMPLIANCE_STATUS_DUE",
				KycLevel:         "FULL_KYC",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			collector, mocks := getTestKycDataCollectorWithMock(t)
			if tt.mocks != nil {
				tt.mocks(mocks)
			}
			got, err := collector.CollectData(tt.args.ctx, tt.args.inputParams)
			if (err != nil) != tt.wantErr {
				t.Errorf("KycBotDataCollector.CollectData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("KycBotDataCollector.CollectData() = %v, want %v", got, tt.want)
			}
		})
	}
}
