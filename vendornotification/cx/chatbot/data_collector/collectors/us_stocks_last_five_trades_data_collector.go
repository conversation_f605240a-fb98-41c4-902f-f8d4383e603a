package collectors

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	ussCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	ussOrderPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type UsStocksLastFiveTradesDataCollector struct {
	usStocksOrderClient   ussOrderPb.OrderManagerClient
	usStocksCatalogClient ussCatalogPb.CatalogManagerClient
}

func NewUsStocksLastFiveTradesDataCollector(
	usStocksOrderClient ussOrderPb.OrderManagerClient,
	usStocksCatalogClient ussCatalogPb.CatalogManagerClient,
) *UsStocksLastFiveTradesDataCollector {
	return &UsStocksLastFiveTradesDataCollector{
		usStocksOrderClient:   usStocksOrderClient,
		usStocksCatalogClient: usStocksCatalogClient,
	}
}

type UsStocksLastFiveTradesData struct {
	Trades []TradeInfo `json:"trades"`
}

type TradeInfo struct {
	StockName      string `json:"stock_name"`
	OrderId        string `json:"order_id"`
	OrderTimestamp string `json:"order_timestamp"`
	OrderStatus    string `json:"order_status"`
	ActivityType   string `json:"activity_type"`
}

func (c *UsStocksLastFiveTradesDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	resp, err := c.usStocksOrderClient.GetOrders(ctx, &ussOrderPb.GetOrdersRequest{
		ActorId: actorId,
		PageContext: &rpc.PageContextRequest{
			PageSize: 5,
		},
	})

	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error calling US stocks order service", zap.String(logger.ACTOR_ID, actorId), zap.Error(rpcErr))
		return nil, errors.Wrap(rpcErr, "failed to get US stocks orders")
	}

	var trades []TradeInfo
	for _, order := range resp.GetOrders() {
		stockName, err := c.getStockName(ctx, order.GetCatalogRefId())
		if err != nil {
			logger.Error(ctx, "error getting stock name", zap.String(logger.STOCK_ID, order.GetCatalogRefId()), zap.Error(err))
			stockName = order.GetSymbol()
		}

		trade := TradeInfo{
			StockName:      stockName,
			OrderId:        order.GetExternalOrderId(),
			OrderTimestamp: order.GetCreatedAt().AsTime().Format("2006-01-02T15:04:05Z"),
			OrderStatus:    order.GetState().String(),
			ActivityType:   order.GetSide().String(),
		}
		trades = append(trades, trade)
	}

	return &UsStocksLastFiveTradesData{
		Trades: trades,
	}, nil
}

func (c *UsStocksLastFiveTradesDataCollector) getStockName(ctx context.Context, catalogRefId string) (string, error) {
	catalogResp, err := c.usStocksCatalogClient.GetStocks(ctx, &ussCatalogPb.GetStocksRequest{
		Identifiers: &ussCatalogPb.GetStocksRequest_StockIds{
			StockIds: &ussCatalogPb.RepeatedStrings{
				Ids: []string{catalogRefId},
			},
		},
	})
	if err = epifigrpc.RPCError(catalogResp, err); err != nil {
		logger.Error(ctx, "error calling US stocks catalog service", zap.String(logger.STOCK_ID, catalogRefId), zap.Error(err))
		return "", errors.Wrap(err, "failed to get US stocks catalog")
	}

	stock := catalogResp.GetStocks()[catalogRefId]
	if stock != nil && stock.CompanyInfo != nil && stock.CompanyInfo.GetCompanyName() != nil {
		if shortName := stock.CompanyInfo.GetCompanyName().GetShortName(); shortName != "" {
			return shortName, nil
		}
	}
	return fmt.Sprintf("Unknown Stock (%s)", catalogRefId), nil
}
