package collectors

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	bankcustPb "github.com/epifi/gamma/api/bankcust"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type KycBotDataCollector struct {
	bankCustClient   bankcustPb.BankCustomerServiceClient
	complianceClient compliancePb.ComplianceClient
}

func NewKycDataCollector(
	bankCustClient bankcustPb.BankCustomerServiceClient,
	complianceClient compliancePb.ComplianceClient,
) *KycBotDataCollector {
	return &KycBotDataCollector{
		bankCustClient:   bankCustClient,
		complianceClient: complianceClient,
	}
}

func (c *KycBotDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	kycBotDataResp := &KycBotData{}

	// Get bank customer details to fetch KYC level
	bankCustomerResp, err := c.bankCustClient.GetBankCustomer(ctx, &bankcustPb.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})

	if te := epifigrpc.RPCError(bankCustomerResp, err); te != nil {
		logger.Error(ctx, "error calling bank customer service", zap.String(logger.ACTOR_ID, actorId), zap.Error(te))
		return nil, te
	}

	// Extract KYC level if available and valid
	if bankCustomerResp.GetBankCustomer().GetKycInfo() == nil || bankCustomerResp.GetBankCustomer().GetKycInfo().GetKycLevel() == kycPb.KYCLevel_UNSPECIFIED {
		logger.Info(ctx, "kyc level not found, continuing without KYC level", zap.String(logger.ACTOR_ID, actorId))
	} else {
		kycBotDataResp.KycLevel = bankCustomerResp.GetBankCustomer().GetKycInfo().GetKycLevel().String()
	}

	// Get KYC compliance status
	complianceResp, err := c.complianceClient.GetPeriodicKYCDetail(ctx, &compliancePb.GetPeriodicKYCDetailRequest{
		ActorId:     actorId,
		FetchLatest: false, // Use cached data first
	})

	if te := epifigrpc.RPCError(complianceResp, err); te != nil {
		logger.Error(ctx, "error calling compliance service", zap.String(logger.ACTOR_ID, actorId), zap.Error(te))
		return nil, te
	}

	// Extract compliance status if available and valid
	if complianceResp.GetPeriodicKYCDetail() == nil || complianceResp.GetPeriodicKYCDetail().GetKYCComplianceStatus() == compliancePb.KYCComplianceStatus_KYC_COMPLIANCE_STATUS_UNSPECIFIED {
		logger.Info(ctx, "compliance status not found, continuing without compliance status", zap.String(logger.ACTOR_ID, actorId))
	} else {
		kycBotDataResp.ComplianceStatus = complianceResp.GetPeriodicKYCDetail().GetKYCComplianceStatus().String()
	}

	return kycBotDataResp, nil
}
