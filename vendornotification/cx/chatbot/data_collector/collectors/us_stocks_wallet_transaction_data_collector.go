package collectors

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	beMoney "github.com/epifi/be-common/pkg/money"

	usstocksPb "github.com/epifi/gamma/api/usstocks"
	ussOrderPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type UsStocksWalletTransactionDataCollector struct {
	usStocksOrderClient ussOrderPb.OrderManagerClient
}

func NewUsStocksWalletTransactionDataCollector(
	usStocksOrderClient ussOrderPb.OrderManagerClient,
) *UsStocksWalletTransactionDataCollector {
	return &UsStocksWalletTransactionDataCollector{
		usStocksOrderClient: usStocksOrderClient,
	}
}

type UsStocksWalletTransactionData struct {
	AddFunds      *WalletOrderInfo `json:"add_funds,omitempty"`
	WithdrawFunds *WalletOrderInfo `json:"withdraw_funds,omitempty"`
}

type WalletOrderInfo struct {
	Status    string `json:"status"`
	Amount    string `json:"amount"`
	CreatedAt string `json:"created_at"`
}

func (c *UsStocksWalletTransactionDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	addFundsResp, err := c.usStocksOrderClient.GetWalletOrders(ctx, &ussOrderPb.GetWalletOrdersRequest{
		ActorId: actorId,
		OrderType: []usstocksPb.WalletOrderType{
			usstocksPb.WalletOrderType_WALLET_ORDER_TYPE_ADD_FUNDS,
		},
		PageContext: &rpc.PageContextRequest{
			PageSize: 1,
		},
	})

	if rpcErr := epifigrpc.RPCError(addFundsResp, err); rpcErr != nil {
		logger.Error(ctx, "error calling US stocks order service for add funds", zap.String(logger.ACTOR_ID, actorId), zap.Error(rpcErr))
		return nil, errors.Wrap(rpcErr, "failed to get US stocks add funds orders")
	}

	withdrawFundsResp, err := c.usStocksOrderClient.GetWalletOrders(ctx, &ussOrderPb.GetWalletOrdersRequest{
		ActorId: actorId,
		OrderType: []usstocksPb.WalletOrderType{
			usstocksPb.WalletOrderType_WALLET_ORDER_TYPE_WITHDRAW_FUNDS,
		},
		PageContext: &rpc.PageContextRequest{
			PageSize: 1,
		},
	})

	if rpcErr := epifigrpc.RPCError(withdrawFundsResp, err); rpcErr != nil {
		logger.Error(ctx, "error calling US stocks order service for withdrawal funds", zap.String(logger.ACTOR_ID, actorId), zap.Error(rpcErr))
		return nil, errors.Wrap(rpcErr, "failed to get US stocks withdrawal funds orders")
	}

	var addFundsInfo *WalletOrderInfo
	if len(addFundsResp.GetOrders()) > 0 {
		order := addFundsResp.GetOrders()[0]
		addFundsInfo = &WalletOrderInfo{
			Status:    order.GetStatus().String(),
			Amount:    beMoney.ToDisplayStringWithPrecision(order.GetAmountRequested(), 2),
			CreatedAt: order.GetCreatedAt().AsTime().Format("2006-01-02T15:04:05Z"),
		}
	}

	var withdrawFundsInfo *WalletOrderInfo
	if len(withdrawFundsResp.GetOrders()) > 0 {
		order := withdrawFundsResp.GetOrders()[0]
		withdrawFundsInfo = &WalletOrderInfo{
			Status:    order.GetStatus().String(),
			Amount:    beMoney.ToDisplayStringWithPrecision(order.GetAmountRequested(), 2),
			CreatedAt: order.GetCreatedAt().AsTime().Format("2006-01-02T15:04:05Z"),
		}
	}

	return &UsStocksWalletTransactionData{
		AddFunds:      addFundsInfo,
		WithdrawFunds: withdrawFundsInfo,
	}, nil
}
