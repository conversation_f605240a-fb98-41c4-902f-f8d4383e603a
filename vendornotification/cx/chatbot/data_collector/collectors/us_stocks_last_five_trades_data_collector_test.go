package collectors

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"

	usstocksPb "github.com/epifi/gamma/api/usstocks"
	ussCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	ussCatalogMocks "github.com/epifi/gamma/api/usstocks/catalog/mocks"
	ussOrderPb "github.com/epifi/gamma/api/usstocks/order"
	ussOrderMocks "github.com/epifi/gamma/api/usstocks/order/mocks"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

const testActorIdLastFiveTrades = "test-actor-123"

type mockDependenciesLastFiveTrades struct {
	orderClient   *ussOrderMocks.MockOrderManagerClient
	catalogClient *ussCatalogMocks.MockCatalogManagerClient
}

func newCollectorWithMocks(t *testing.T) (*UsStocksLastFiveTradesDataCollector, *mockDependenciesLastFiveTrades) {
	ctrl := gomock.NewController(t)
	orderClient := ussOrderMocks.NewMockOrderManagerClient(ctrl)
	catalogClient := ussCatalogMocks.NewMockCatalogManagerClient(ctrl)

	collector := NewUsStocksLastFiveTradesDataCollector(orderClient, catalogClient)

	return collector, &mockDependenciesLastFiveTrades{
		orderClient:   orderClient,
		catalogClient: catalogClient,
	}
}

func createOrder(id, symbol, catalogRefId string, side usstocksPb.OrderSide, state usstocksPb.OrderState, createdAt time.Time) *ussOrderPb.Order {
	return &ussOrderPb.Order{
		ExternalOrderId: id,
		ActorId:         testActorIdLastFiveTrades,
		Symbol:          symbol,
		CatalogRefId:    catalogRefId,
		Side:            side,
		State:           state,
		CreatedAt:       timestamppb.New(createdAt),
	}
}

func createStockResponse(catalogId, companyName string) *ussCatalogPb.GetStocksResponse {
	return &ussCatalogPb.GetStocksResponse{
		Status: rpc.StatusOk(),
		Stocks: map[string]*ussCatalogPb.Stock{
			catalogId: {
				CompanyInfo: &ussCatalogPb.CompanyInfo{
					CompanyName: &ussCatalogPb.CompanyName{
						ShortName: companyName,
					},
				},
			},
		},
	}
}

func createTradeInfo(stockName, orderId, orderTimestamp, orderStatus, activityType string) TradeInfo {
	return TradeInfo{
		StockName:      stockName,
		OrderId:        orderId,
		OrderTimestamp: orderTimestamp,
		OrderStatus:    orderStatus,
		ActivityType:   activityType,
	}
}

func TestUsStocksLastFiveTradesDataCollector_CollectData(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name    string
		actorId string
		mocks   func(md *mockDependenciesLastFiveTrades)
		want    *UsStocksLastFiveTradesData
		wantErr bool
	}{
		{
			name:    "success: user has 3 trading orders",
			actorId: testActorIdLastFiveTrades,
			mocks: func(md *mockDependenciesLastFiveTrades) {
				orders := []*ussOrderPb.Order{
					createOrder("order-123", "AAPL", "catalog-aapl", usstocksPb.OrderSide_BUY, usstocksPb.OrderState_ORDER_SUCCESS, time.Date(2024, 1, 15, 10, 30, 0, 0, time.UTC)),
					createOrder("order-456", "GOOGL", "catalog-googl", usstocksPb.OrderSide_SELL, usstocksPb.OrderState_ORDER_SUCCESS, time.Date(2024, 1, 14, 14, 20, 0, 0, time.UTC)),
					createOrder("order-789", "MSFT", "catalog-msft", usstocksPb.OrderSide_BUY, usstocksPb.OrderState_ORDER_INITIATED, time.Date(2024, 1, 13, 12, 0, 0, 0, time.UTC)),
				}

				md.orderClient.EXPECT().GetOrders(
					gomock.Any(),
					&ussOrderPb.GetOrdersRequest{
						ActorId:     testActorIdLastFiveTrades,
						PageContext: &rpc.PageContextRequest{PageSize: 5},
					},
				).Return(&ussOrderPb.GetOrdersResponse{
					Status: rpc.StatusOk(),
					Orders: orders,
				}, nil)

				md.catalogClient.EXPECT().GetStocks(gomock.Any(), gomock.Any()).Return(createStockResponse("catalog-aapl", "Apple Inc."), nil).Times(1)
				md.catalogClient.EXPECT().GetStocks(gomock.Any(), gomock.Any()).Return(createStockResponse("catalog-googl", "Alphabet Inc."), nil).Times(1)
				md.catalogClient.EXPECT().GetStocks(gomock.Any(), gomock.Any()).Return(createStockResponse("catalog-msft", "Microsoft Corporation"), nil).Times(1)
			},
			want: &UsStocksLastFiveTradesData{
				Trades: []TradeInfo{
					createTradeInfo("Apple Inc.", "order-123", "2024-01-15T10:30:00Z", "ORDER_SUCCESS", "BUY"),
					createTradeInfo("Alphabet Inc.", "order-456", "2024-01-14T14:20:00Z", "ORDER_SUCCESS", "SELL"),
					createTradeInfo("Microsoft Corporation", "order-789", "2024-01-13T12:00:00Z", "ORDER_INITIATED", "BUY"),
				},
			},
			wantErr: false,
		},
		{
			name:    "success: no trading orders found",
			actorId: testActorIdLastFiveTrades,
			mocks: func(md *mockDependenciesLastFiveTrades) {
				md.orderClient.EXPECT().GetOrders(
					gomock.Any(),
					&ussOrderPb.GetOrdersRequest{
						ActorId:     testActorIdLastFiveTrades,
						PageContext: &rpc.PageContextRequest{PageSize: 5},
					},
				).Return(&ussOrderPb.GetOrdersResponse{
					Status: rpc.StatusOk(),
					Orders: []*ussOrderPb.Order{},
				}, nil)
			},
			want: &UsStocksLastFiveTradesData{
				Trades: nil,
			},
			wantErr: false,
		},
		{
			name:    "error: missing actorId",
			actorId: "",
			mocks: func(md *mockDependenciesLastFiveTrades) {
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:    "error: RPC failure",
			actorId: testActorIdLastFiveTrades,
			mocks: func(md *mockDependenciesLastFiveTrades) {
				md.orderClient.EXPECT().GetOrders(
					gomock.Any(),
					&ussOrderPb.GetOrdersRequest{
						ActorId:     testActorIdLastFiveTrades,
						PageContext: &rpc.PageContextRequest{PageSize: 5},
					},
				).Return(&ussOrderPb.GetOrdersResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			collector, md := newCollectorWithMocks(t)
			tt.mocks(md)

			inputParams := options.DataCollectorParams{ActorId: tt.actorId}
			got, err := collector.CollectData(context.Background(), inputParams)

			if tt.wantErr {
				require.Error(t, err)
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				gotData, ok := got.(*UsStocksLastFiveTradesData)
				require.True(t, ok)
				require.Equal(t, tt.want, gotData)
			}
		})
	}
}
