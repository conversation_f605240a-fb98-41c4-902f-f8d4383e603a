package collectors

import (
	"context"
	"fmt"
	"strconv"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	accountEnumsPb "github.com/epifi/gamma/api/accounts/enums"
	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/auth"
	afu "github.com/epifi/gamma/api/auth/afu"
	"github.com/epifi/gamma/api/bankcust"
	productPb "github.com/epifi/gamma/api/product"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

// AFUUpdateEligibilityCollector determines if a user is eligible to update their auth factors (phone/email).
//
// IMPORTANT: The order of eligibility checks in this collector is intentionally kept as close as possible
// to the actual checks performed in the AFU (Auth Factor Update) flow. This ensures consistency between
// what the chatbot reports to users and what they will actually experience during the update process.
// AFU string to enum mapping
var afuStringToEnumMap = map[string]afu.AuthFactor{
	"PHONE_NUM": afu.AuthFactor_PHONE_NUM,
	"EMAIL":     afu.AuthFactor_EMAIL,
}

// Ineligibility reason constants
const (
	ReasonAfuVendorUpdateInProgress  = "AFU_VENDOR_UPDATE_IN_PROGRESS"
	ReasonAfuInProgressOrStuck       = "AFU_IN_PROGRESS_OR_STUCK"
	ReasonCoolOffPeriod              = "COOL_OFF_PERIOD"
	ReasonNonResidentRestricted      = "NON_RESIDENT_USER_RESTRICTED"
	ReasonIncompatibleActiveProducts = "INCOMPATIBLE_ACTIVE_PRODUCTS"
	ReasonBankCustomerInactive       = "BANK_CUSTOMER_INACTIVE"
	ReasonBankCustomerNotFound       = "BANK_CUSTOMER_NOT_FOUND"
	ReasonAccountInactive            = "ACCOUNT_INACTIVE"
	ReasonAccountDormant             = "ACCOUNT_DORMANT"
)

type AFUUpdateEligibilityCollector struct {
	authClient     auth.AuthClient
	userClient     user.UsersClient
	bankCustClient bankcust.BankCustomerServiceClient
	savingsClient  savings.SavingsClient
	opStatusClient operationalStatusPb.OperationalStatusServiceClient
	productClient  productPb.ProductClient
}

func NewAFUUpdateEligibilityCollector(
	authClient auth.AuthClient,
	userClient user.UsersClient,
	bankCustClient bankcust.BankCustomerServiceClient,
	savingsClient savings.SavingsClient,
	opStatusClient operationalStatusPb.OperationalStatusServiceClient,
	productClient productPb.ProductClient,
) *AFUUpdateEligibilityCollector {
	return &AFUUpdateEligibilityCollector{
		authClient:     authClient,
		userClient:     userClient,
		bankCustClient: bankCustClient,
		savingsClient:  savingsClient,
		opStatusClient: opStatusClient,
		productClient:  productClient,
	}
}

func (c *AFUUpdateEligibilityCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	// Get the AFU type from request parameters
	afuParam := inputParams.GetAFU()
	if afuParam == "" {
		return nil, errors.New("AFU parameter is required")
	}

	// Convert string to AuthFactor enum
	authFactor, err := c.parseAuthFactor(afuParam)
	if err != nil {
		return nil, errors.Wrap(err, "invalid AFU parameter")
	}

	// 1) Check if vendor update is in progress
	hasVendorUpdateInProgress, err := c.checkVendorUpdateInProgress(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error checking vendor update in progress")
	}
	if hasVendorUpdateInProgress {
		return &AFUUpdateEligibilityData{
			IsEligible: strconv.FormatBool(false),
			Reason:     ReasonAfuVendorUpdateInProgress,
		}, nil
	}

	// 2) Check last AFU status
	lastAfuStatus, err := c.getLastAfuStatus(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error getting last AFU status")
	}
	if lastAfuStatus == afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS ||
		lastAfuStatus == afu.OverallStatus_OVERALL_STATUS_STUCK {
		return &AFUUpdateEligibilityData{
			IsEligible: strconv.FormatBool(false),
			Reason:     ReasonAfuInProgressOrStuck,
		}, nil
	}

	// 3) Check cooldown for the specific auth factor
	cooldownInfos, err := c.authClient.GetAuthFactorCooldownInfo(ctx, &auth.GetAuthFactorCooldownInfoRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(cooldownInfos, err); rpcErr != nil {
		return nil, errors.Wrap(rpcErr, "error getting cooldown info")
	}

	for _, cooldownInfo := range cooldownInfos.GetCooldownInfo() {
		if cooldownInfo.GetAuthFactor() == authFactor && cooldownInfo.GetIsInCooldown() {
			return &AFUUpdateEligibilityData{
				IsEligible:        strconv.FormatBool(false),
				Reason:            ReasonCoolOffPeriod,
				DaysToEligibility: cooldownInfo.GetDaysRemaining(),
			}, nil
		}
	}

	// 4) Check if user is non-resident (AFU updates not allowed for NR users)
	if ineligible, reason, err := c.checkNonResidentUser(ctx, actorId); err != nil {
		return nil, err
	} else if ineligible {
		return &AFUUpdateEligibilityData{
			IsEligible: strconv.FormatBool(false),
			Reason:     reason,
		}, nil
	}

	// 5) Check active products compatibility
	if ineligible, reason, err := c.checkActiveProducts(ctx, actorId); err != nil {
		return nil, err
	} else if ineligible {
		return &AFUUpdateEligibilityData{
			IsEligible: strconv.FormatBool(false),
			Reason:     reason,
		}, nil
	}

	// 6) Check bank customer activity status
	if ineligible, reason, err := c.checkBankCustomerStatus(ctx, actorId, authFactor); err != nil {
		return nil, err
	} else if ineligible {
		return &AFUUpdateEligibilityData{
			IsEligible: strconv.FormatBool(false),
			Reason:     reason,
		}, nil
	}

	// 7) Check operational status
	if ineligible, reason, err := c.checkOperationalStatus(ctx, actorId); err != nil {
		return nil, err
	} else if ineligible {
		return &AFUUpdateEligibilityData{
			IsEligible: strconv.FormatBool(false),
			Reason:     reason,
		}, nil
	}

	// If all checks pass, user is eligible
	return &AFUUpdateEligibilityData{IsEligible: strconv.FormatBool(true)}, nil
}

// checkNonResidentUser checks if user is non-resident (AFU updates not allowed)
func (c *AFUUpdateEligibilityCollector) checkNonResidentUser(ctx context.Context, actorId string) (bool, string, error) {
	res, err := c.userClient.IsNonResidentUser(ctx, &user.IsNonResidentUserRequest{
		Identifier: &user.IsNonResidentUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		logger.Error(ctx, "error checking non-resident user status", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		// Propagate error for all cases - RNF, invalid argument, internal, etc.
		return false, "", err
	}

	if res.GetIsNonResidentUser().ToBool() {
		return true, ReasonNonResidentRestricted, nil
	}
	return false, "", nil
}

// checkActiveProducts checks if user has compatible active products
func (c *AFUUpdateEligibilityCollector) checkActiveProducts(ctx context.Context, actorId string) (bool, string, error) {
	// Check for the required product types
	productTypes := []productPb.ProductType{
		productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
		productPb.ProductType_PRODUCT_TYPE_USSTOCKS,
		productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
	}

	productsRes, err := c.productClient.GetProductsStatus(ctx, &productPb.GetProductsStatusRequest{
		ActorId:      actorId,
		ProductTypes: productTypes,
	})
	if err = epifigrpc.RPCError(productsRes, err); err != nil {
		logger.Error(ctx, "error getting product status", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		// Propagate error instead of returning response with reason
		return false, "", err
	}

	productInfoMap := productsRes.GetProductInfoMap()
	if len(productInfoMap) == 0 {
		return true, ReasonIncompatibleActiveProducts, nil
	}

	// Check the specific product combination: SAVINGS_ACCOUNT but NOT (USSTOCKS or PERSONAL_LOANS) as we don't allow
	// afu update if the user have a loans or us stocks account
	hasSavingsAccount := false
	hasUSStocks := false
	hasPersonalLoans := false

	for productKey, productInfo := range productInfoMap {
		if productInfo.GetProductStatus() == productPb.ProductStatus_PRODUCT_STATUS_ACTIVE {
			switch {
			case productKey == productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT.String():
				hasSavingsAccount = true
			case productKey == productPb.ProductType_PRODUCT_TYPE_USSTOCKS.String():
				hasUSStocks = true
			case productKey == productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS.String():
				hasPersonalLoans = true
			}
		}
	}

	if hasSavingsAccount && !hasUSStocks && !hasPersonalLoans {
		return false, "", nil
	}

	return true, ReasonIncompatibleActiveProducts, nil
}

// checkBankCustomerStatus checks if bank customer is active or eligible inactive reason
func (c *AFUUpdateEligibilityCollector) checkBankCustomerStatus(ctx context.Context, actorId string, authFactor afu.AuthFactor) (bool, string, error) {
	bankCustResp, err := c.bankCustClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err = epifigrpc.RPCError(bankCustResp, err); err != nil {
		if bankCustResp != nil && bankCustResp.GetStatus().IsRecordNotFound() {
			// RNF case - customer not found, return with not eligible and reason
			return true, ReasonBankCustomerNotFound, nil
		}
		// Other errors (invalid argument, internal, etc.) - propagate error
		logger.Error(ctx, "error getting bank customer status", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return false, "", err
	}

	customerStatus := bankCustResp.GetBankCustomer().GetStatus()
	if customerStatus == bankcust.Status_STATUS_ACTIVE {
		return false, "", nil
	}

	// If not active, check reason
	if customerStatus == bankcust.Status_CUSTOMER_STATUS_INACTIVE {
		reasons := bankCustResp.GetBankCustomer().GetCustomerInactiveInfo().GetReasons()
		if len(reasons) == 0 {
			return true, ReasonBankCustomerInactive, nil
		}

		// Allow AFU update if reason is PHONE_NUMBER_UPDATE_OUTSIDE_FI and AFU is phone number
		if reasons[0].GetReasonType() == bankcust.CustomerInactiveReasonType_CUSTOMER_INACTIVE_REASON_TYPE_PHONE_NUMBER_UPDATE_OUTSIDE_FI {
			// Only allow if AFU being processed is phone number
			if authFactor == afu.AuthFactor_PHONE_NUM {
				return false, "", nil
			}
		}
	}

	return true, ReasonBankCustomerInactive, nil
}

// checkOperationalStatus checks if account is not inactive/dormant
func (c *AFUUpdateEligibilityCollector) checkOperationalStatus(ctx context.Context, actorId string) (bool, string, error) {
	// Get savings account
	savingsAccResp, err := c.savingsClient.GetAccount(ctx, &savings.GetAccountRequest{
		Identifier: &savings.GetAccountRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(savingsAccResp, err); rpcErr != nil {
		if savingsAccResp != nil && savingsAccResp.GetStatus().IsRecordNotFound() {
			// RNF case - account not found, user not eligible
			return true, ReasonAccountInactive, nil
		}
		// Other errors (invalid argument, internal, etc.) - propagate error
		logger.Error(ctx, "error getting savings account", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return false, "", rpcErr
	}

	// Get operational status
	opStatusResp, opErr := c.opStatusClient.GetOperationalStatus(ctx, &operationalStatusPb.GetOperationalStatusRequest{
		DataFreshness: operationalStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
		AccountIdentifier: &operationalStatusPb.GetOperationalStatusRequest_SavingsAccountId{
			SavingsAccountId: savingsAccResp.GetAccount().GetId(),
		},
	})

	// Handle phone mismatch gracefully
	if opErr = epifigrpc.RPCError(opStatusResp, opErr); opErr != nil &&
		opStatusResp.GetStatus().GetCode() != uint32(operationalStatusPb.GetOperationalStatusResponse_ACC_NUMBER_PHONE_MISMATCH) {
		logger.Error(ctx, "error getting operational status", zap.Error(opErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return false, "", opErr
	}

	// Check operational status
	switch opStatusResp.GetOperationalStatusInfo().GetOperationalStatus() {
	case accountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_INACTIVE:
		return true, ReasonAccountInactive, nil
	case accountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_DORMANT:
		return true, ReasonAccountDormant, nil
	default:
		return false, "", nil
	}
}

// checkVendorUpdateInProgress checks if there's any AFU with vendor update in progress
func (c *AFUUpdateEligibilityCollector) checkVendorUpdateInProgress(ctx context.Context, actorId string) (bool, error) {
	// Get in-progress AFU records
	afuRes, err := c.authClient.GetAuthFactorUpdatesForActor(ctx, &auth.GetAuthFactorUpdatesForActorRequest{
		ActorId:       actorId,
		OverallStatus: afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS,
	})
	if rpcErr := epifigrpc.RPCError(afuRes, err); rpcErr != nil {
		if afuRes != nil && afuRes.GetStatus().IsRecordNotFound() {
			return false, nil // No in-progress AFU records
		}
		return false, rpcErr
	}

	// Check if any in-progress AFU has vendor update started
	for _, afuRecord := range afuRes.GetAuthFactorUpdates() {
		if c.isVendorUpdateStarted(afuRecord) {
			return true, nil
		}
	}

	return false, nil
}

// isVendorUpdateStarted checks if AFU record has vendor update started
func (c *AFUUpdateEligibilityCollector) isVendorUpdateStarted(afuRecord *afu.AuthFactorUpdate) bool {
	return afuRecord.GetVendorContext().GetState() != afu.UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED
}

// getLastAfuStatus gets the status of the last AFU update
func (c *AFUUpdateEligibilityCollector) getLastAfuStatus(ctx context.Context, actorId string) (afu.OverallStatus, error) {
	// Get the most recent AFU record
	afuRes, err := c.authClient.GetAuthFactorUpdatesForActor(ctx, &auth.GetAuthFactorUpdatesForActorRequest{
		ActorId: actorId,
		Count:   1,
	})
	if rpcErr := epifigrpc.RPCError(afuRes, err); rpcErr != nil {
		if afuRes != nil && afuRes.GetStatus().IsRecordNotFound() {
			// No AFU records found, allow user (don't block)
			return afu.OverallStatus_OVERALL_STATUS_COMPLETED, nil
		}
		return afu.OverallStatus_OVERALL_STATUS_UNSPECIFIED, rpcErr
	}

	if len(afuRes.GetAuthFactorUpdates()) == 0 {
		// No AFU records found, allow user (don't block)
		return afu.OverallStatus_OVERALL_STATUS_COMPLETED, nil
	}

	return afuRes.GetAuthFactorUpdates()[0].GetOverallStatus(), nil
}

// parseAuthFactor converts the string AFU parameter to AuthFactor enum using map lookup
func (c *AFUUpdateEligibilityCollector) parseAuthFactor(afuParam string) (afu.AuthFactor, error) {
	if authFactor, exists := afuStringToEnumMap[afuParam]; exists {
		return authFactor, nil
	}
	return afu.AuthFactor_AUTH_FACTOR_UNSPECIFIED, fmt.Errorf("unsupported AFU type: %s, supported values are PHONE_NUM, EMAIL", afuParam)
}
