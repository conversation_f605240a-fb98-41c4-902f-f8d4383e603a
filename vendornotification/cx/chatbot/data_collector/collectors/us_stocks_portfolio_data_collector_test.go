package collectors

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	googleMoney "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"

	usstocksPb "github.com/epifi/gamma/api/usstocks"
	ussPortfolioPb "github.com/epifi/gamma/api/usstocks/portfolio"
	ussPortfolioMocks "github.com/epifi/gamma/api/usstocks/portfolio/mocks"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

const testActorIdPortfolio = "test-actor-456"

type mockDependenciesPortfolio struct {
	portfolioClient *ussPortfolioMocks.MockPortfolioManagerClient
}

func newPortfolioCollectorWithMocks(t *testing.T) (*UsStocksPortfolioDataCollector, *mockDependenciesPortfolio) {
	ctrl := gomock.NewController(t)
	portfolioClient := ussPortfolioMocks.NewMockPortfolioManagerClient(ctrl)

	collector := NewUsStocksPortfolioDataCollector(portfolioClient)

	return collector, &mockDependenciesPortfolio{
		portfolioClient: portfolioClient,
	}
}

func createPortfolioResponse(positions []*ussPortfolioPb.Position) *ussPortfolioPb.GetAllOpenPositionsResponse {
	return &ussPortfolioPb.GetAllOpenPositionsResponse{
		Status:    rpc.StatusOk(),
		Positions: positions,
	}
}

func TestUsStocksPortfolioDataCollector_CollectData(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name string
		args struct {
			ctx         context.Context
			inputParams options.DataCollectorParams
		}
		mocks   func(md *mockDependenciesPortfolio)
		want    *UsStocksPortfolioData
		wantErr bool
	}{
		{
			name: "success: portfolio with assets",
			args: struct {
				ctx         context.Context
				inputParams options.DataCollectorParams
			}{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdPortfolio,
				},
			},
			mocks: func(md *mockDependenciesPortfolio) {
				md.portfolioClient.EXPECT().GetAllOpenPositions(
					context.Background(),
					&ussPortfolioPb.GetAllOpenPositionsRequest{
						ActorId: testActorIdPortfolio,
						SortBy:  usstocksPb.PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_UNSPECIFIED,
					},
				).Return(createPortfolioResponse([]*ussPortfolioPb.Position{
					{
						Symbol:         "AAPL",
						DisplayName:    "Apple Inc.",
						InvestedAmount: &googleMoney.Money{CurrencyCode: "USD", Units: 1000, Nanos: 50000000},
					},
					{
						Symbol:         "GOOGL",
						DisplayName:    "Alphabet Inc.",
						InvestedAmount: &googleMoney.Money{CurrencyCode: "USD", Units: 2500, Nanos: 0},
					},
				}), nil)
			},
			want: &UsStocksPortfolioData{
				Assets: []PortfolioAsset{
					{
						AssetsName:     "Apple Inc.",
						Symbol:         "AAPL",
						AmountInvested: "$1,000.05",
					},
					{
						AssetsName:     "Alphabet Inc.",
						Symbol:         "GOOGL",
						AmountInvested: "$2,500.00",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success: empty portfolio",
			args: struct {
				ctx         context.Context
				inputParams options.DataCollectorParams
			}{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdPortfolio,
				},
			},
			mocks: func(md *mockDependenciesPortfolio) {
				md.portfolioClient.EXPECT().GetAllOpenPositions(
					context.Background(),
					&ussPortfolioPb.GetAllOpenPositionsRequest{
						ActorId: testActorIdPortfolio,
						SortBy:  usstocksPb.PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_UNSPECIFIED,
					},
				).Return(createPortfolioResponse([]*ussPortfolioPb.Position{}), nil)
			},
			want: &UsStocksPortfolioData{
				Assets: nil,
			},
			wantErr: false,
		},
		{
			name: "error: missing actorId",
			args: struct {
				ctx         context.Context
				inputParams options.DataCollectorParams
			}{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "",
				},
			},
			mocks:   func(md *mockDependenciesPortfolio) {},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			collector, md := newPortfolioCollectorWithMocks(t)
			tt.mocks(md)

			got, err := collector.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.wantErr {
				require.Error(t, err)
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.want, got)
			}
		})
	}
}
