package collectors

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	beMoney "github.com/epifi/be-common/pkg/money"

	usstocksPb "github.com/epifi/gamma/api/usstocks"
	ussPortfolioPb "github.com/epifi/gamma/api/usstocks/portfolio"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type UsStocksPortfolioDataCollector struct {
	usStocksPortfolioClient ussPortfolioPb.PortfolioManagerClient
}

func NewUsStocksPortfolioDataCollector(
	usStocksPortfolioClient ussPortfolioPb.PortfolioManagerClient,
) *UsStocksPortfolioDataCollector {
	return &UsStocksPortfolioDataCollector{
		usStocksPortfolioClient: usStocksPortfolioClient,
	}
}

type UsStocksPortfolioData struct {
	Assets []PortfolioAsset `json:"assets"`
}

type PortfolioAsset struct {
	AssetsName     string `json:"assets_name"`
	Symbol         string `json:"symbol"`
	AmountInvested string `json:"amount_invested"`
}

func (c *UsStocksPortfolioDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	resp, err := c.usStocksPortfolioClient.GetAllOpenPositions(ctx, &ussPortfolioPb.GetAllOpenPositionsRequest{
		ActorId: actorId,
		SortBy:  usstocksPb.PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_UNSPECIFIED,
	})

	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error calling US stocks portfolio service", zap.String(logger.ACTOR_ID, actorId), zap.Error(rpcErr))
		return nil, errors.Wrap(rpcErr, "failed to get US stocks portfolio")
	}

	var assets []PortfolioAsset
	for _, position := range resp.GetPositions() {
		asset := PortfolioAsset{
			AssetsName:     position.GetDisplayName(),
			Symbol:         position.GetSymbol(),
			AmountInvested: beMoney.ToDisplayStringWithPrecision(position.GetInvestedAmount(), 2),
		}
		assets = append(assets, asset)
	}

	return &UsStocksPortfolioData{
		Assets: assets,
	}, nil
}
