package collectors

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/be-common/api/typesv2/common"

	accountEnumsPb "github.com/epifi/gamma/api/accounts/enums"
	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	opStatusMocks "github.com/epifi/gamma/api/accounts/operstatus/mocks"
	"github.com/epifi/gamma/api/auth"
	afu "github.com/epifi/gamma/api/auth/afu"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	"github.com/epifi/gamma/api/bankcust"
	bankCustMocks "github.com/epifi/gamma/api/bankcust/mocks"
	productPb "github.com/epifi/gamma/api/product"
	productMocks "github.com/epifi/gamma/api/product/mocks"
	"github.com/epifi/gamma/api/savings"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	"github.com/epifi/gamma/api/user"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

const (
	testAfuActorId = "test-actor-123"
)

type afuUpdateEligibilityMockDependencies struct {
	mockAuthClient     *authMocks.MockAuthClient
	mockUserClient     *userMocks.MockUsersClient
	mockBankCustClient *bankCustMocks.MockBankCustomerServiceClient
	mockSavingsClient  *savingsMocks.MockSavingsClient
	mockOpStatusClient *opStatusMocks.MockOperationalStatusServiceClient
	mockProductClient  *productMocks.MockProductClient
}

func newAFUUpdateEligibilityCollectorWithMocks(t *testing.T) (*AFUUpdateEligibilityCollector, *afuUpdateEligibilityMockDependencies) {
	ctrl := gomock.NewController(t)

	md := &afuUpdateEligibilityMockDependencies{
		mockAuthClient:     authMocks.NewMockAuthClient(ctrl),
		mockUserClient:     userMocks.NewMockUsersClient(ctrl),
		mockBankCustClient: bankCustMocks.NewMockBankCustomerServiceClient(ctrl),
		mockSavingsClient:  savingsMocks.NewMockSavingsClient(ctrl),
		mockOpStatusClient: opStatusMocks.NewMockOperationalStatusServiceClient(ctrl),
		mockProductClient:  productMocks.NewMockProductClient(ctrl),
	}

	dataCollector := NewAFUUpdateEligibilityCollector(
		md.mockAuthClient,
		md.mockUserClient,
		md.mockBankCustClient,
		md.mockSavingsClient,
		md.mockOpStatusClient,
		md.mockProductClient,
	)

	t.Cleanup(func() {
		ctrl.Finish()
	})

	return dataCollector, md
}

func TestAFUUpdateEligibilityCollector_CollectData(t *testing.T) {
	// Initialize logger for testing
	logger.Init(cfg.TestEnv)

	type args struct {
		ctx         context.Context
		inputParams options.DataCollectorParams
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(md *afuUpdateEligibilityMockDependencies)
		want    *AFUUpdateEligibilityData
		wantErr error
	}{
		{
			name: "return error due to empty actor id",
			args: args{
				ctx:         context.Background(),
				inputParams: options.DataCollectorParams{},
			},
			mocks:   func(md *afuUpdateEligibilityMockDependencies) {},
			want:    nil,
			wantErr: errors.New("actorId is required"),
		},
		{
			name: "return error due to empty AFU parameter",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testAfuActorId,
				},
			},
			mocks:   func(md *afuUpdateEligibilityMockDependencies) {},
			want:    nil,
			wantErr: errors.New("AFU parameter is required"),
		},
		{
			name: "return error due to invalid AFU parameter",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testAfuActorId,
					AFU:     "INVALID_AFU",
				},
			},
			mocks:   func(md *afuUpdateEligibilityMockDependencies) {},
			want:    nil,
			wantErr: errors.New("invalid AFU parameter"),
		},
		{
			name: "return ineligible when vendor update is in progress",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testAfuActorId,
					AFU:     "PHONE_NUM",
				},
			},
			mocks: func(md *afuUpdateEligibilityMockDependencies) {
				// Mock vendor update check - has in-progress AFU with vendor update started
				md.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(
					context.Background(),
					&auth.GetAuthFactorUpdatesForActorRequest{
						ActorId:       testAfuActorId,
						OverallStatus: afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS,
					},
				).Return(&auth.GetAuthFactorUpdatesForActorResponse{
					Status: rpc.StatusOk(),
					AuthFactorUpdates: []*afu.AuthFactorUpdate{
						{
							VendorContext: &afu.VendorContext{
								State: afu.UpdateVendorState_INITIATED,
							},
						},
					},
				}, nil)
			},
			want: &AFUUpdateEligibilityData{
				IsEligible: "false",
				Reason:     ReasonAfuVendorUpdateInProgress,
			},
			wantErr: nil,
		},
		{
			name: "return eligible when no previous AFU found - allows user (fix for getLastAfuStatus)",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testAfuActorId,
					AFU:     "PHONE_NUM",
				},
			},
			mocks: func(md *afuUpdateEligibilityMockDependencies) {
				// Mock vendor update check - no in-progress AFU
				md.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(
					context.Background(),
					&auth.GetAuthFactorUpdatesForActorRequest{
						ActorId:       testAfuActorId,
						OverallStatus: afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS,
					},
				).Return(&auth.GetAuthFactorUpdatesForActorResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				// Mock last AFU status check - no AFU found, should return COMPLETED (the fix)
				md.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(
					context.Background(),
					&auth.GetAuthFactorUpdatesForActorRequest{
						ActorId: testAfuActorId,
						Count:   1,
					},
				).Return(&auth.GetAuthFactorUpdatesForActorResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				// Mock cooldown check - no cooldown
				md.mockAuthClient.EXPECT().GetAuthFactorCooldownInfo(
					context.Background(),
					&auth.GetAuthFactorCooldownInfoRequest{
						ActorId: testAfuActorId,
					},
				).Return(&auth.GetAuthFactorCooldownInfoResponse{
					Status:       rpc.StatusOk(),
					CooldownInfo: []*auth.AuthFactorCooldownInfo{},
				}, nil)

				// Mock non-resident check - resident user
				md.mockUserClient.EXPECT().IsNonResidentUser(
					context.Background(),
					gomock.Any(),
				).Return(&user.IsNonResidentUserResponse{
					Status:            rpc.StatusOk(),
					IsNonResidentUser: common.BooleanEnum_FALSE,
				}, nil)

				// Mock product check - has savings account only
				md.mockProductClient.EXPECT().GetProductsStatus(
					context.Background(),
					gomock.Any(),
				).Return(&productPb.GetProductsStatusResponse{
					Status: rpc.StatusOk(),
					ProductInfoMap: map[string]*productPb.ProductInfo{
						productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT.String(): {
							ProductStatus: productPb.ProductStatus_PRODUCT_STATUS_ACTIVE,
						},
					},
				}, nil)

				// Mock bank customer check - active customer
				md.mockBankCustClient.EXPECT().GetBankCustomer(
					context.Background(),
					gomock.Any(),
				).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						Status: bankcust.Status_STATUS_ACTIVE,
					},
				}, nil)

				// Mock savings account check
				md.mockSavingsClient.EXPECT().GetAccount(
					context.Background(),
					gomock.Any(),
				).Return(&savings.GetAccountResponse{
					Status: rpc.StatusOk(),
					Account: &savings.Account{
						Id: "account-id",
					},
				}, nil)

				// Mock operational status check - active account
				md.mockOpStatusClient.EXPECT().GetOperationalStatus(
					context.Background(),
					gomock.Any(),
				).Return(&operationalStatusPb.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
					OperationalStatusInfo: &operationalStatusPb.OperationalStatusInfo{
						OperationalStatus: accountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
					},
				}, nil)
			},
			want: &AFUUpdateEligibilityData{
				IsEligible: "true",
			},
			wantErr: nil,
		},
		{
			name: "return eligible when phone AFU is requested for inactive customer with PHONE_NUMBER_UPDATE_OUTSIDE_FI reason",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testAfuActorId,
					AFU:     "PHONE_NUM",
				},
			},
			mocks: func(md *afuUpdateEligibilityMockDependencies) {
				// Mock vendor update check - no in-progress AFU
				md.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(
					context.Background(),
					gomock.Any(),
				).Return(&auth.GetAuthFactorUpdatesForActorResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				// Mock last AFU status check - has completed AFU
				md.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(
					context.Background(),
					gomock.Any(),
				).Return(&auth.GetAuthFactorUpdatesForActorResponse{
					Status: rpc.StatusOk(),
					AuthFactorUpdates: []*afu.AuthFactorUpdate{
						{
							OverallStatus: afu.OverallStatus_OVERALL_STATUS_COMPLETED,
						},
					},
				}, nil)

				// Mock cooldown check - no cooldown
				md.mockAuthClient.EXPECT().GetAuthFactorCooldownInfo(
					context.Background(),
					gomock.Any(),
				).Return(&auth.GetAuthFactorCooldownInfoResponse{
					Status:       rpc.StatusOk(),
					CooldownInfo: []*auth.AuthFactorCooldownInfo{},
				}, nil)

				// Mock non-resident check - resident user
				md.mockUserClient.EXPECT().IsNonResidentUser(
					context.Background(),
					gomock.Any(),
				).Return(&user.IsNonResidentUserResponse{
					Status:            rpc.StatusOk(),
					IsNonResidentUser: common.BooleanEnum_FALSE,
				}, nil)

				// Mock product check - has savings account only
				md.mockProductClient.EXPECT().GetProductsStatus(
					context.Background(),
					gomock.Any(),
				).Return(&productPb.GetProductsStatusResponse{
					Status: rpc.StatusOk(),
					ProductInfoMap: map[string]*productPb.ProductInfo{
						productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT.String(): {
							ProductStatus: productPb.ProductStatus_PRODUCT_STATUS_ACTIVE,
						},
					},
				}, nil)

				// Mock bank customer check - inactive customer with PHONE_NUMBER_UPDATE_OUTSIDE_FI reason
				// This should allow phone AFU but would block email AFU (the new validation)
				md.mockBankCustClient.EXPECT().GetBankCustomer(
					context.Background(),
					gomock.Any(),
				).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						Status: bankcust.Status_CUSTOMER_STATUS_INACTIVE,
						CustomerInactiveInfo: &bankcust.CustomerInactiveInfo{
							Reasons: []*bankcust.InactiveReasonInfo{
								{
									ReasonType: bankcust.CustomerInactiveReasonType_CUSTOMER_INACTIVE_REASON_TYPE_PHONE_NUMBER_UPDATE_OUTSIDE_FI,
								},
							},
						},
					},
				}, nil)

				// Mock savings account check
				md.mockSavingsClient.EXPECT().GetAccount(
					context.Background(),
					gomock.Any(),
				).Return(&savings.GetAccountResponse{
					Status: rpc.StatusOk(),
					Account: &savings.Account{
						Id: "account-id",
					},
				}, nil)

				// Mock operational status check - active account
				md.mockOpStatusClient.EXPECT().GetOperationalStatus(
					context.Background(),
					gomock.Any(),
				).Return(&operationalStatusPb.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
					OperationalStatusInfo: &operationalStatusPb.OperationalStatusInfo{
						OperationalStatus: accountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
					},
				}, nil)
			},
			want: &AFUUpdateEligibilityData{
				IsEligible: "true",
			},
			wantErr: nil,
		},
		{
			name: "return ineligible when email AFU is requested for inactive customer with PHONE_NUMBER_UPDATE_OUTSIDE_FI reason",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testAfuActorId,
					AFU:     "EMAIL",
				},
			},
			mocks: func(md *afuUpdateEligibilityMockDependencies) {
				// Mock vendor update check - no in-progress AFU
				md.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(
					context.Background(),
					gomock.Any(),
				).Return(&auth.GetAuthFactorUpdatesForActorResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				// Mock last AFU status check - has completed AFU
				md.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(
					context.Background(),
					gomock.Any(),
				).Return(&auth.GetAuthFactorUpdatesForActorResponse{
					Status: rpc.StatusOk(),
					AuthFactorUpdates: []*afu.AuthFactorUpdate{
						{
							OverallStatus: afu.OverallStatus_OVERALL_STATUS_COMPLETED,
						},
					},
				}, nil)

				// Mock cooldown check - no cooldown
				md.mockAuthClient.EXPECT().GetAuthFactorCooldownInfo(
					context.Background(),
					gomock.Any(),
				).Return(&auth.GetAuthFactorCooldownInfoResponse{
					Status:       rpc.StatusOk(),
					CooldownInfo: []*auth.AuthFactorCooldownInfo{},
				}, nil)

				// Mock non-resident check - resident user
				md.mockUserClient.EXPECT().IsNonResidentUser(
					context.Background(),
					gomock.Any(),
				).Return(&user.IsNonResidentUserResponse{
					Status:            rpc.StatusOk(),
					IsNonResidentUser: common.BooleanEnum_FALSE,
				}, nil)

				// Mock product check - has savings account only
				md.mockProductClient.EXPECT().GetProductsStatus(
					context.Background(),
					gomock.Any(),
				).Return(&productPb.GetProductsStatusResponse{
					Status: rpc.StatusOk(),
					ProductInfoMap: map[string]*productPb.ProductInfo{
						productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT.String(): {
							ProductStatus: productPb.ProductStatus_PRODUCT_STATUS_ACTIVE,
						},
					},
				}, nil)

				// Mock bank customer check - inactive customer with PHONE_NUMBER_UPDATE_OUTSIDE_FI reason
				// This should block email AFU since it's not phone AFU
				md.mockBankCustClient.EXPECT().GetBankCustomer(
					context.Background(),
					gomock.Any(),
				).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						Status: bankcust.Status_CUSTOMER_STATUS_INACTIVE,
						CustomerInactiveInfo: &bankcust.CustomerInactiveInfo{
							Reasons: []*bankcust.InactiveReasonInfo{
								{
									ReasonType: bankcust.CustomerInactiveReasonType_CUSTOMER_INACTIVE_REASON_TYPE_PHONE_NUMBER_UPDATE_OUTSIDE_FI,
								},
							},
						},
					},
				}, nil)
			},
			want: &AFUUpdateEligibilityData{
				IsEligible: "false",
				Reason:     ReasonBankCustomerInactive,
			},
			wantErr: nil,
		},
		{
			name: "return ineligible when cooldown period is active",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testAfuActorId,
					AFU:     "PHONE_NUM",
				},
			},
			mocks: func(md *afuUpdateEligibilityMockDependencies) {
				// Mock vendor update check - no in-progress AFU
				md.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(
					context.Background(),
					gomock.Any(),
				).Return(&auth.GetAuthFactorUpdatesForActorResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				// Mock last AFU status check - has completed AFU
				md.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(
					context.Background(),
					gomock.Any(),
				).Return(&auth.GetAuthFactorUpdatesForActorResponse{
					Status: rpc.StatusOk(),
					AuthFactorUpdates: []*afu.AuthFactorUpdate{
						{
							OverallStatus: afu.OverallStatus_OVERALL_STATUS_COMPLETED,
						},
					},
				}, nil)

				// Mock cooldown check - has active cooldown
				md.mockAuthClient.EXPECT().GetAuthFactorCooldownInfo(
					context.Background(),
					gomock.Any(),
				).Return(&auth.GetAuthFactorCooldownInfoResponse{
					Status: rpc.StatusOk(),
					CooldownInfo: []*auth.AuthFactorCooldownInfo{
						{
							AuthFactor:    afu.AuthFactor_PHONE_NUM,
							IsInCooldown:  true,
							DaysRemaining: 5,
						},
					},
				}, nil)
			},
			want: &AFUUpdateEligibilityData{
				IsEligible:        "false",
				Reason:            ReasonCoolOffPeriod,
				DaysToEligibility: 5,
			},
			wantErr: nil,
		},
		{
			name: "return ineligible when user is non-resident",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testAfuActorId,
					AFU:     "PHONE_NUM",
				},
			},
			mocks: func(md *afuUpdateEligibilityMockDependencies) {
				// Mock vendor update check - no in-progress AFU
				md.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(
					context.Background(),
					gomock.Any(),
				).Return(&auth.GetAuthFactorUpdatesForActorResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				// Mock last AFU status check - has completed AFU
				md.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(
					context.Background(),
					gomock.Any(),
				).Return(&auth.GetAuthFactorUpdatesForActorResponse{
					Status: rpc.StatusOk(),
					AuthFactorUpdates: []*afu.AuthFactorUpdate{
						{
							OverallStatus: afu.OverallStatus_OVERALL_STATUS_COMPLETED,
						},
					},
				}, nil)

				// Mock cooldown check - no cooldown
				md.mockAuthClient.EXPECT().GetAuthFactorCooldownInfo(
					context.Background(),
					gomock.Any(),
				).Return(&auth.GetAuthFactorCooldownInfoResponse{
					Status:       rpc.StatusOk(),
					CooldownInfo: []*auth.AuthFactorCooldownInfo{},
				}, nil)

				// Mock non-resident check - non-resident user (blocked)
				md.mockUserClient.EXPECT().IsNonResidentUser(
					context.Background(),
					gomock.Any(),
				).Return(&user.IsNonResidentUserResponse{
					Status:            rpc.StatusOk(),
					IsNonResidentUser: common.BooleanEnum_TRUE,
				}, nil)
			},
			want: &AFUUpdateEligibilityData{
				IsEligible: "false",
				Reason:     ReasonNonResidentRestricted,
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dataCollector, md := newAFUUpdateEligibilityCollectorWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(md)
			}
			got, err := dataCollector.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.wantErr != nil {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr.Error())
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				require.NotNil(t, got)

				// Cast to the expected type
				gotData, ok := got.(*AFUUpdateEligibilityData)
				require.True(t, ok, "Expected *AFUUpdateEligibilityData type")

				// Compare the entire response
				require.Equal(t, tt.want, gotData)
			}
		})
	}
}
