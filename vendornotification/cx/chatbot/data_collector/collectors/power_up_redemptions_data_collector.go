package collectors

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	casperPb "github.com/epifi/gamma/api/casper"
	redemptionPb "github.com/epifi/gamma/api/casper/redemption"

	"github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type PowerUpRedemptionsDataCollector struct {
	offerRedemptionClient redemptionPb.OfferRedemptionServiceClient
	offerCatalogClient    casperPb.OfferCatalogServiceClient
	genConf               *genconf.Config
}

func NewPowerUpRedemptionsDataCollector(
	offerRedemptionClient redemptionPb.OfferRedemptionServiceClient,
	offerCatalogClient casperPb.OfferCatalogServiceClient,
	genConf *genconf.Config,
) *PowerUpRedemptionsDataCollector {
	return &PowerUpRedemptionsDataCollector{
		offerRedemptionClient: offerRedemptionClient,
		offerCatalogClient:    offerCatalogClient,
		genConf:               genConf,
	}
}

// CollectData fetches power up redemptions for the actor
// Returns information about which power up rewards were bought, their status, name, and id
func (c *PowerUpRedemptionsDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	// Get the limit from configuration
	limit := c.genConf.Nugget().PowerUpRedemptionsLimit()

	// Fetch redeemed offers for the actor with power up filter
	redeemedOffersResp, err := c.offerRedemptionClient.GetRedeemedOffersForActor(ctx, &redemptionPb.GetRedeemedOffersForActorRequest{
		ActorId: actorId,
		PageContext: &rpcPb.PageContextRequest{
			// nolint : gosec
			PageSize: uint32(limit),
		},
		Filters: &redemptionPb.GetRedeemedOffersForActorRequest_Filters{
			OfferType: casperPb.OfferType_POWER_UP,
		},
	})

	if rpcErr := epifigrpc.RPCError(redeemedOffersResp, err); rpcErr != nil {
		logger.Error(ctx, "error calling GetRedeemedOffersForActor", zap.String(logger.ACTOR_ID, actorId), zap.Error(rpcErr))
		return nil, errors.Wrap(rpcErr, "failed to get redeemed offers for actor")
	}

	redeemedOffers := redeemedOffersResp.GetRedeemedOffers()
	if len(redeemedOffers) == 0 {
		return &PowerUpRedemptionsData{
			PowerUpRedemptions: []PowerUpRedemptionData{},
		}, nil
	}

	// Extract offer IDs to fetch offer details
	offerIds := make([]string, 0, len(redeemedOffers))
	for _, redeemedOffer := range redeemedOffers {
		if redeemedOffer.GetOfferId() != "" {
			offerIds = append(offerIds, redeemedOffer.GetOfferId())
		}
	}

	// Fetch offer details using bulk API
	offerDetailsResp, err := c.offerCatalogClient.GetBulkOfferDetailsByIds(ctx, &casperPb.GetBulkOfferDetailsByIdsRequest{
		OfferIds: offerIds,
	})

	var offerIdToNameMap map[string]string
	if rpcErr := epifigrpc.RPCError(offerDetailsResp, err); rpcErr != nil {
		logger.Error(ctx, "error calling GetBulkOfferDetailsByIds", zap.String(logger.ACTOR_ID, actorId), zap.Error(rpcErr))
		return nil, errors.Wrap(rpcErr, "failed to get offers details")
	} else {
		// Create mapping from offer ID to offer name
		offerIdToNameMap = make(map[string]string)
		for _, offer := range offerDetailsResp.GetOffers() {
			offerIdToNameMap[offer.GetId()] = offer.GetName()
		}
	}

	// Build the response data
	powerUpRedemptions := make([]PowerUpRedemptionData, 0, len(redeemedOffers))
	for _, redeemedOffer := range redeemedOffers {
		redemptionData := PowerUpRedemptionData{
			ID:     redeemedOffer.GetRedemptionRequestId(),
			Status: redeemedOffer.GetRedemptionState().String(),
		}

		// Get offer name from the mapping
		if offerName, exists := offerIdToNameMap[redeemedOffer.GetOfferId()]; exists {
			redemptionData.Name = offerName
		} else {
			redemptionData.Name = "Unknown Offer"
		}

		powerUpRedemptions = append(powerUpRedemptions, redemptionData)
	}

	return &PowerUpRedemptionsData{
		PowerUpRedemptions: powerUpRedemptions,
	}, nil
}
