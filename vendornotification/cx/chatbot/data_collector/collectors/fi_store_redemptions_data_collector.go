package collectors

import (
	"context"
	"strconv"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	beMoney "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/syncmap"
	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	genConf "github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type FiStoreRedemptionsDataCollector struct {
	externalVendorRedemptionsClient evrPb.ExternalVendorRedemptionServiceClient
	conf                            *genConf.Config
	supportedRewardCategories       map[string]evrPb.Category
}

func NewFiStoreRedemptionsDataCollector(
	externalVendorRedemptionsClient evrPb.ExternalVendorRedemptionServiceClient,
	conf *genConf.Config,
) *FiStoreRedemptionsDataCollector {
	return &FiStoreRedemptionsDataCollector{
		externalVendorRedemptionsClient: externalVendorRedemptionsClient,
		conf:                            conf,
		supportedRewardCategories: map[string]evrPb.Category{
			"CATEGORY_MILES_EXCHANGE": evrPb.Category_CATEGORY_MILES_EXCHANGE,
			"CATEGORY_GIFT_CARDS":     evrPb.Category_CATEGORY_GIFT_CARDS,
		},
	}
}

// FiStoreRedemptionItem represents a single redemption item
type FiStoreRedemptionItem struct {
	ProductPrice         string `json:"product_price"`
	Status               string `json:"status"`
	FiCoinsSpent         string `json:"fi_coins_spent"`
	CashSpent            string `json:"cash_spent"`
	BrandName            string `json:"brand_name"`
	IsFiPointsTransacted string `json:"is_fi_points_transacted"`
	OrderId              string `json:"order_id"`
	ProductName          string `json:"product_name"`
}

// FiStoreRedemptionsData represents the complete response data
type FiStoreRedemptionsData struct {
	RedemptionItems []*FiStoreRedemptionItem `json:"redemption_items"`
	TotalCount      int                      `json:"total_count"`
}

// CollectData fetches Fi Store redemptions for the given category
func (c *FiStoreRedemptionsDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	// Validate request parameters
	actorId, category, err := c.validateRequest(ctx, inputParams)
	if err != nil {
		return nil, err
	}

	// Get limit from config with dynamic tag
	limit := c.conf.Nugget().FiStoreRedemptionsLimit()

	// Extract valid order statuses dynamically (excluding UNSPECIFIED and ABANDONED)
	validStatuses := c.getValidOrderStatuses()

	// Call GetFiStoreRedemptions RPC with OrderStatus filtering
	resp, err := c.externalVendorRedemptionsClient.GetFiStoreRedemptions(ctx, &evrPb.GetFiStoreRedemptionsRequest{
		ActorId: actorId,
		Filters: &evrPb.GetFiStoreRedemptionsRequest_Filters{
			Categories:    []evrPb.Category{category},
			OrderStatuses: validStatuses,
		},
		PageCtxRequest: &rpcPb.PageContextRequest{
			// nolint: gosec
			PageSize: uint32(limit),
		},
	})

	if rErr := epifigrpc.RPCError(resp, err); rErr != nil {
		logger.Error(ctx, "error calling GetFiStoreRedemptions", zap.String(logger.ACTOR_ID, actorId), zap.String("category", category.String()), zap.Error(rErr))
		return nil, errors.Wrap(rErr, "failed to get fi store redemptions")
	}

	redemptions := resp.GetRedemptions()

	// Process each redemption in parallel using errgroup and thread-safe generic map
	g := errgroup.New()
	resultMap := &syncmap.Map[string, *FiStoreRedemptionItem]{}

	for _, redemption := range redemptions {
		redemption := redemption // capture loop variable
		g.Go(func() error {
			item, err := c.processRedemption(ctx, actorId, redemption)
			if err != nil {
				logger.Error(ctx, "error processing redemption", zap.String(logger.ACTOR_ID, actorId),
					zap.String("redemption_id", redemption.GetId()), zap.Error(err))
				return err
			}

			resultMap.Store(redemption.GetId(), item)
			return nil
		})
	}

	// Wait for all goroutines to complete
	if err := g.Wait(); err != nil {
		return nil, errors.Wrap(err, "failed to process redemptions")
	}

	// Collect results and maintain original order
	redemptionItems := make([]*FiStoreRedemptionItem, 0, len(redemptions))
	for _, redemption := range redemptions {
		item, ok := resultMap.Load(redemption.GetId())
		if !ok {
			return nil, errors.Errorf("failed to find processed redemption item for redemption id: %s", redemption.GetId())
		}
		redemptionItems = append(redemptionItems, item)
	}

	return &FiStoreRedemptionsData{
		RedemptionItems: redemptionItems,
		TotalCount:      len(redemptionItems),
	}, nil
}

// validateRequest validates the request parameters and returns validated values
func (c *FiStoreRedemptionsDataCollector) validateRequest(_ context.Context, inputParams options.DataCollectorParams) (string, evrPb.Category, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return "", evrPb.Category_CATEGORY_UNSPECIFIED, errors.New("actorId is required")
	}

	// Get reward category from input params
	rewardCategory := inputParams.GetRewardCategory()
	if rewardCategory == "" {
		return "", evrPb.Category_CATEGORY_UNSPECIFIED, errors.New("rewardCategory is required")
	}

	// Map string category to enum using supported categories map
	category, exists := c.supportedRewardCategories[rewardCategory]
	if !exists {
		return "", evrPb.Category_CATEGORY_UNSPECIFIED, errors.Errorf("unsupported reward category: %s", rewardCategory)
	}

	return actorId, category, nil
}

// processRedemption processes a single redemption and calculates is_fi_points_transacted
// nolint : unparam
func (c *FiStoreRedemptionsDataCollector) processRedemption(ctx context.Context, actorId string, redemption *evrPb.FiStoreRedemption) (*FiStoreRedemptionItem, error) {
	// Calculate is_fi_points_transacted by checking external vendor redemptions
	// This is based on the logic from GetFiStoreRedemptionAdditionalDetails in service.go
	externalVendorRedemptionsResp, err := c.externalVendorRedemptionsClient.GetExternalVendorRedemptions(ctx, &evrPb.GetExternalVendorRedemptionsRequest{
		ActorId: actorId,
		Filters: &evrPb.GetExternalVendorRedemptionsRequest_Filters{
			VendorOrderId: redemption.GetVendorRefId(),
			Vendor:        redemption.GetVendor(),
		},
	})

	var isFiPointsTransacted bool
	if rpcErr := epifigrpc.RPCError(externalVendorRedemptionsResp, err); rpcErr != nil {
		logger.Error(ctx, "error getting external vendor redemptions", zap.String(logger.ACTOR_ID, actorId), zap.Error(rpcErr))
	}
	if len(externalVendorRedemptionsResp.GetExternalVendorRedemptions()) > 0 {
		isFiPointsTransacted = true
	}

	return &FiStoreRedemptionItem{
		ProductPrice:         beMoney.ToDisplayStringWithPrecision(redemption.GetProductPrice(), 2),
		Status:               redemption.GetOrderStatus().String(),
		FiCoinsSpent:         strconv.Itoa(int(redemption.GetSpentFiCoinUnits())),
		CashSpent:            beMoney.ToDisplayStringWithPrecision(redemption.GetSpentCashUnits(), 2),
		BrandName:            redemption.GetRedemptionMetaData().GetBrandName(),
		IsFiPointsTransacted: strconv.FormatBool(isFiPointsTransacted),
		OrderId:              redemption.GetVendorRefId(),
		ProductName:          redemption.GetRedemptionMetaData().GetProductName(),
	}, nil
}

// getValidOrderStatuses extracts valid order statuses dynamically from OrderStatus_value
func (c *FiStoreRedemptionsDataCollector) getValidOrderStatuses() []evrPb.OrderStatus {
	var validStatuses []evrPb.OrderStatus

	// Iterate through all order status values and exclude UNSPECIFIED and ABANDONED
	for _, statusValue := range evrPb.OrderStatus_value {
		orderStatus := evrPb.OrderStatus(statusValue)
		if orderStatus != evrPb.OrderStatus_ORDER_STATUS_UNSPECIFIED &&
			orderStatus != evrPb.OrderStatus_ORDER_STATUS_ABANDONED {
			validStatuses = append(validStatuses, orderStatus)
		}
	}

	return validStatuses
}
