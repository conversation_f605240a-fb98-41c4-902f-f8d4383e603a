package collectors

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"

	ussAccPb "github.com/epifi/gamma/api/usstocks/account"
	ussAccMocks "github.com/epifi/gamma/api/usstocks/account/mocks"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

const testActorIdWallet = "test-actor-id-us-stocks-wallet"

type mockDependenciesWallet struct {
	accountClient *ussAccMocks.MockAccountManagerClient
}

func newWalletCollectorWithMocks(t *testing.T) (*UsStocksWalletBalanceDataCollector, *mockDependenciesWallet) {
	ctrl := gomock.NewController(t)
	accountClient := ussAccMocks.NewMockAccountManagerClient(ctrl)

	collector := NewUsStocksWalletBalanceDataCollector(accountClient)

	return collector, &mockDependenciesWallet{
		accountClient: accountClient,
	}
}

func createTradingAccountResponse(actorId string, cash *money.Money, buyingPower *money.Money) *ussAccPb.GetTradingAccountDetailsResponse {
	return &ussAccPb.GetTradingAccountDetailsResponse{
		Status: rpc.StatusOk(),
		TradingAccount: &ussAccPb.TradingAccount{
			Id:      "account-123",
			ActorId: actorId,
			WalletDetails: &ussAccPb.WalletDetails{
				Cash:        cash,
				BuyingPower: buyingPower,
			},
		},
	}
}

func TestUsStocksWalletBalanceDataCollector_CollectData(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name string
		args struct {
			ctx         context.Context
			inputParams options.DataCollectorParams
		}
		mocks   func(md *mockDependenciesWallet)
		want    *UsStocksWalletBalanceData
		wantErr bool
	}{
		{
			name: "success: wallet with balance",
			args: struct {
				ctx         context.Context
				inputParams options.DataCollectorParams
			}{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdWallet,
				},
			},
			mocks: func(md *mockDependenciesWallet) {
				md.accountClient.EXPECT().GetTradingAccountDetails(
					context.Background(),
					&ussAccPb.GetTradingAccountDetailsRequest{
						Identifier: &ussAccPb.TradingAccountIdentifier{
							Identifier: &ussAccPb.TradingAccountIdentifier_ActorId{
								ActorId: testActorIdWallet,
							},
						},
						Strategy: ussAccPb.GetTradingAccountDetailsRequest_BEST_EFFORT,
					},
				).Return(createTradingAccountResponse(testActorIdWallet, &money.Money{CurrencyCode: "USD", Units: 567, Nanos: *********}, &money.Money{CurrencyCode: "USD", Units: 567, Nanos: *********}), nil)
			},
			want: &UsStocksWalletBalanceData{
				Amount: "$567.87",
			},
			wantErr: false,
		},
		{
			name: "success: zero balance",
			args: struct {
				ctx         context.Context
				inputParams options.DataCollectorParams
			}{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdWallet,
				},
			},
			mocks: func(md *mockDependenciesWallet) {
				md.accountClient.EXPECT().GetTradingAccountDetails(
					context.Background(),
					&ussAccPb.GetTradingAccountDetailsRequest{
						Identifier: &ussAccPb.TradingAccountIdentifier{
							Identifier: &ussAccPb.TradingAccountIdentifier_ActorId{
								ActorId: testActorIdWallet,
							},
						},
						Strategy: ussAccPb.GetTradingAccountDetailsRequest_BEST_EFFORT,
					},
				).Return(createTradingAccountResponse(testActorIdWallet, &money.Money{CurrencyCode: "USD", Units: 0, Nanos: 0}, &money.Money{CurrencyCode: "USD", Units: 0, Nanos: 0}), nil)
			},
			want: &UsStocksWalletBalanceData{
				Amount: "0.00",
			},
			wantErr: false,
		},
		{
			name: "success: cash is nil but buying power exists",
			args: struct {
				ctx         context.Context
				inputParams options.DataCollectorParams
			}{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdWallet,
				},
			},
			mocks: func(md *mockDependenciesWallet) {
				md.accountClient.EXPECT().GetTradingAccountDetails(
					context.Background(),
					&ussAccPb.GetTradingAccountDetailsRequest{
						Identifier: &ussAccPb.TradingAccountIdentifier{
							Identifier: &ussAccPb.TradingAccountIdentifier_ActorId{
								ActorId: testActorIdWallet,
							},
						},
						Strategy: ussAccPb.GetTradingAccountDetailsRequest_BEST_EFFORT,
					},
				).Return(&ussAccPb.GetTradingAccountDetailsResponse{
					Status: rpc.StatusOk(),
					TradingAccount: &ussAccPb.TradingAccount{
						Id:      "account-123",
						ActorId: testActorIdWallet,
						WalletDetails: &ussAccPb.WalletDetails{
							Cash:        nil,
							BuyingPower: &money.Money{CurrencyCode: "USD", Units: 100, Nanos: 0},
						},
					},
				}, nil)
			},
			want: &UsStocksWalletBalanceData{
				Amount: "0.00",
			},
			wantErr: false,
		},
		{
			name: "error: missing actorId",
			args: struct {
				ctx         context.Context
				inputParams options.DataCollectorParams
			}{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "",
				},
			},
			mocks:   func(md *mockDependenciesWallet) {},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error: no wallet details",
			args: struct {
				ctx         context.Context
				inputParams options.DataCollectorParams
			}{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdWallet,
				},
			},
			mocks: func(md *mockDependenciesWallet) {
				md.accountClient.EXPECT().GetTradingAccountDetails(
					context.Background(),
					&ussAccPb.GetTradingAccountDetailsRequest{
						Identifier: &ussAccPb.TradingAccountIdentifier{
							Identifier: &ussAccPb.TradingAccountIdentifier_ActorId{
								ActorId: testActorIdWallet,
							},
						},
						Strategy: ussAccPb.GetTradingAccountDetailsRequest_BEST_EFFORT,
					},
				).Return(&ussAccPb.GetTradingAccountDetailsResponse{
					Status: rpc.StatusOk(),
					TradingAccount: &ussAccPb.TradingAccount{
						Id:            "account-123",
						ActorId:       testActorIdWallet,
						WalletDetails: nil,
					},
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			collector, md := newWalletCollectorWithMocks(t)
			tt.mocks(md)

			got, err := collector.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.wantErr {
				require.Error(t, err)
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.want, got)
			}
		})
	}
}
