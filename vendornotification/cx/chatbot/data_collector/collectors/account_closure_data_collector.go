package collectors

import (
	"context"
	"strconv"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	beMoney "github.com/epifi/be-common/pkg/money"
	enumsPb "github.com/epifi/gamma/api/frontend/account/enums"
	saClosurePb "github.com/epifi/gamma/api/frontend/account/sa_closure"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type AccountClosureDataCollector struct {
	saClosureClient saClosurePb.SavingsAccountClosureClient
}

func NewAccountClosureDataCollector(
	saClosureClient saClosurePb.SavingsAccountClosureClient,
) *AccountClosureDataCollector {
	return &AccountClosureDataCollector{
		saClosureClient: saClosureClient,
	}
}

var (
	// Map from criteria item enum to human-readable string
	saClosureCriteriaItemToStringMap = map[enumsPb.SaClosureCriteriaItem]string{
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_UNSPECIFIED:             "unspecified reason",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_JUMP:                    "Jump Investment",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_SMART_DEPOSIT:           "Smart Deposit",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_FIXED_DEPOSIT:           "Fixed Deposit",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_MUTUAL_FUND:             "Mutual Fund",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_US_STOCK:                "US Stocks Investments",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_INDIAN_STOCK:            "Indian Stocks Investments",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_VEHICLE_LOAN:            "Vehicle Loan",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_PERSONAL_LOAN:           "Personal Loan",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_EDUCATION_LOAN:          "Education Loan",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_HOME_LOAN:               "Home Loan",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_OTHER_LOAN:              "Other Loan",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_AMPLIFI_CREDIT_CARD:     "Amplifi credit card",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_SIMPLIFI_CREDIT_CARD:    "Simplifi credit card",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_AUTO_PAY:                "Auto Pay",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_AUTO_PAY_SIP:            "Auto Pay SIP",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_FI_COIN_BALANCE:         "Zero Fi point balance",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_SAVINGS_ACCOUNT_BALANCE: "Zero savings account balance",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_MAGNIFI_CREDIT_CARD:     "Magnifi credit card",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_AUTO_SAVE:               "Auto save",
		enumsPb.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_UPI_LITE:                "UPI Lite",
	}

	// Special case strings for non-enum pre closure start checks
	accountLienReason          = "Account Lien"
	accountFreezeReason        = "Account Freeze"
	pendingChargesReason       = "Pending Charges"
	accountAlreadyClosedReason = "Account already closed"
)

// CollectData
// IMPORTANT: Whenever a new SaClosureCriteriaItem enum is created following follow-up steps are required to be done:
// 1. Update saClosureCriteriaItemToStringMap with human-readable mapping for the newly created enum
// 2. Update nugget-chatbot configuration in the nugget dashboard to handle the new response
func (c *AccountClosureDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	// TODO: Start calling the BE service's account closure eligibility check when the logic is migrated to BE.
	resp, err := c.saClosureClient.EvaluateUserForClosureEligibility(ctx, &saClosurePb.EvaluateUserForClosureEligibilityRequest{
		ActorId: actorId,
	})

	if rErr := fepkg.FeRPCError(resp, err); rErr != nil {
		logger.Error(ctx, "error calling EvaluateUserForClosureEligibility", zap.String(logger.ACTOR_ID, actorId), zap.Error(rErr))
		return nil, errors.Wrap(rErr, "failed to evaluate user for closure eligibility")
	}

	// Determine eligibility - use the FailedCriteriaCheck from response which already accounts for all blocking conditions
	// including lien, freeze, pending charges, account closure status, and all criteria items
	isEligible := !resp.GetFailedCriteriaCheck()

	var reasonsForIneligibility []string

	// Add failed criteria items
	for _, evaluatedCriteriaItems := range resp.GetEvaluatedCriteriaItems() {
		if evaluatedCriteriaItems.GetIsFailed() {
			if reason, exists := saClosureCriteriaItemToStringMap[evaluatedCriteriaItems.GetCriteriaItem()]; exists {
				reasonsForIneligibility = append(reasonsForIneligibility, reason)
			} else {
				logger.Error(ctx, "unmapped criteria item found in account closure evaluation",
					zap.String(logger.ACTOR_ID, actorId),
					zap.String("criteria_item", evaluatedCriteriaItems.GetCriteriaItem().String()))
				return nil, errors.Errorf("unmapped criteria item found: %v. Please update saClosureCriteriaItemToStringMap and chatbot configuration", evaluatedCriteriaItems.GetCriteriaItem())
			}
		}
	}

	// Add special case reasons (these are separate from criteria items)
	if resp.GetHasLien() {
		reasonsForIneligibility = append(reasonsForIneligibility, accountLienReason)
	}

	if resp.GetHasFreeze() {
		reasonsForIneligibility = append(reasonsForIneligibility, accountFreezeReason)
	}

	if resp.GetIsAccountClosed() {
		reasonsForIneligibility = append(reasonsForIneligibility, accountAlreadyClosedReason)
	}

	// Handle pending charges
	if resp.GetPendingChargesAmount() != nil && beMoney.IsPositive(resp.GetPendingChargesAmount()) {
		reasonsForIneligibility = append(reasonsForIneligibility, pendingChargesReason)
	}

	accountClosureData := &AccountClosureData{
		IsEligible:              strconv.FormatBool(isEligible),
		ReasonsForIneligibility: reasonsForIneligibility,
	}

	// Include pending charges amount if there are pending charges
	if resp.GetPendingChargesAmount() != nil && beMoney.IsPositive(resp.GetPendingChargesAmount()) {
		accountClosureData.PendingChargesAmount = beMoney.ToDisplayStringWithPrecision(resp.GetPendingChargesAmount(), 2)
	}

	return accountClosureData, nil
}
