package collectors

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"

	usstocksPb "github.com/epifi/gamma/api/usstocks"
	ussOrderPb "github.com/epifi/gamma/api/usstocks/order"
	ussOrderMocks "github.com/epifi/gamma/api/usstocks/order/mocks"
	ussPortfolioPb "github.com/epifi/gamma/api/usstocks/portfolio"
	ussPortfolioMocks "github.com/epifi/gamma/api/usstocks/portfolio/mocks"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

const testActorIdUsStocksPortfolioHistory = "test-actor-789"

type mockDependenciesUsStocksPortfolioHistory struct {
	mockUsStocksPortfolioClient *ussPortfolioMocks.MockPortfolioManagerClient
	mockUsStocksOrderClient     *ussOrderMocks.MockOrderManagerClient
}

func newUsStocksPortfolioHistoryDataCollectorWithMocks(t *testing.T) (*UsStocksPortfolioHistoryDataCollector, *mockDependenciesUsStocksPortfolioHistory) {
	ctrl := gomock.NewController(t)
	mockUsStocksPortfolioClient := ussPortfolioMocks.NewMockPortfolioManagerClient(ctrl)
	mockUsStocksOrderClient := ussOrderMocks.NewMockOrderManagerClient(ctrl)

	collector := NewUsStocksPortfolioHistoryDataCollector(mockUsStocksPortfolioClient, mockUsStocksOrderClient)

	return collector, &mockDependenciesUsStocksPortfolioHistory{
		mockUsStocksPortfolioClient: mockUsStocksPortfolioClient,
		mockUsStocksOrderClient:     mockUsStocksOrderClient,
	}
}

type portfolioHistoryArgs struct {
	ctx         context.Context
	inputParams options.DataCollectorParams
}

func TestUsStocksPortfolioHistoryDataCollector_CollectData(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name    string
		args    portfolioHistoryArgs
		mocks   func(md *mockDependenciesUsStocksPortfolioHistory)
		want    *UsStocksPortfolioHistoryData
		wantErr bool
	}{
		{
			name: "success: user has portfolio with trading history",
			args: portfolioHistoryArgs{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdUsStocksPortfolioHistory,
				},
			},
			mocks: func(md *mockDependenciesUsStocksPortfolioHistory) {
				// Mock GetAllOpenPositions
				md.mockUsStocksPortfolioClient.EXPECT().GetAllOpenPositions(
					context.Background(),
					&ussPortfolioPb.GetAllOpenPositionsRequest{
						ActorId: testActorIdUsStocksPortfolioHistory,
						SortBy:  usstocksPb.PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_UNSPECIFIED,
					},
				).Return(&ussPortfolioPb.GetAllOpenPositionsResponse{
					Status: rpc.StatusOk(),
					Positions: []*ussPortfolioPb.Position{
						{Symbol: "AAPL"},
						{Symbol: "GOOGL"},
					},
				}, nil)

				// Mock GetOrders for AAPL
				md.mockUsStocksOrderClient.EXPECT().GetOrders(
					context.Background(),
					&ussOrderPb.GetOrdersRequest{
						ActorId: testActorIdUsStocksPortfolioHistory,
						Symbols: []string{"AAPL"},
						PageContext: &rpc.PageContextRequest{
							PageSize: 100,
						},
					},
				).Return(&ussOrderPb.GetOrdersResponse{
					Status: rpc.StatusOk(),
					Orders: []*ussOrderPb.Order{
						{
							Symbol:    "AAPL",
							Side:      usstocksPb.OrderSide_BUY,
							CreatedAt: timestamppb.New(time.Date(2024, 1, 15, 10, 30, 0, 0, time.UTC)),
						},
					},
				}, nil)

				// Mock GetOrders for GOOGL
				md.mockUsStocksOrderClient.EXPECT().GetOrders(
					context.Background(),
					&ussOrderPb.GetOrdersRequest{
						ActorId: testActorIdUsStocksPortfolioHistory,
						Symbols: []string{"GOOGL"},
						PageContext: &rpc.PageContextRequest{
							PageSize: 100,
						},
					},
				).Return(&ussOrderPb.GetOrdersResponse{
					Status: rpc.StatusOk(),
					Orders: []*ussOrderPb.Order{
						{
							Symbol:    "GOOGL",
							Side:      usstocksPb.OrderSide_SELL,
							CreatedAt: timestamppb.New(time.Date(2024, 1, 17, 9, 15, 0, 0, time.UTC)),
						},
					},
				}, nil)
			},
			want: &UsStocksPortfolioHistoryData{
				TradingHistory: []TradingRecord{
					{
						Symbol:         "AAPL",
						OrderTimestamp: "2024-01-15T10:30:00Z",
						ActivityType:   "buy",
					},
					{
						Symbol:         "GOOGL",
						OrderTimestamp: "2024-01-17T09:15:00Z",
						ActivityType:   "sell",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success: user has empty portfolio",
			args: portfolioHistoryArgs{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdUsStocksPortfolioHistory,
				},
			},
			mocks: func(md *mockDependenciesUsStocksPortfolioHistory) {
				// Mock GetAllOpenPositions with empty portfolio
				md.mockUsStocksPortfolioClient.EXPECT().GetAllOpenPositions(
					context.Background(),
					&ussPortfolioPb.GetAllOpenPositionsRequest{
						ActorId: testActorIdUsStocksPortfolioHistory,
						SortBy:  usstocksPb.PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_UNSPECIFIED,
					},
				).Return(&ussPortfolioPb.GetAllOpenPositionsResponse{
					Status:    rpc.StatusOk(),
					Positions: []*ussPortfolioPb.Position{},
				}, nil)
			},
			want: &UsStocksPortfolioHistoryData{
				TradingHistory: []TradingRecord{},
			},
			wantErr: false,
		},
		{
			name: "error: missing actorId",
			args: portfolioHistoryArgs{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "",
				},
			},
			mocks:   func(md *mockDependenciesUsStocksPortfolioHistory) {},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			collector, md := newUsStocksPortfolioHistoryDataCollectorWithMocks(t)
			tt.mocks(md)

			got, err := collector.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.wantErr {
				require.Error(t, err)
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.want, got)
			}
		})
	}
}
