package collectors

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/money"
	accountsPb "github.com/epifi/gamma/api/accounts"
	depositPb "github.com/epifi/gamma/api/deposit"
	depositMocks "github.com/epifi/gamma/api/deposit/mocks"
	paymentMocks "github.com/epifi/gamma/api/order/payment/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	vendornotificationConfig "github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

const (
	testDepositActorId      = "test-deposit-actor-123"
	testDepositAccountId    = "deposit-account-456"
	testDepositName         = "My Deposit Account"
	testDepositInterestRate = "6.50"
)

// ArgMatcher for ListDepositAccountsRequest validation
type ArgMatcher struct {
	req *depositPb.ListDepositAccountsRequest
}

func (a *ArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*depositPb.ListDepositAccountsRequest)
	if !ok {
		return false
	}

	// Check ActorId
	if got.ActorId != a.req.ActorId {
		return false
	}

	// Check Types - should contain exactly the expected deposit type
	if len(got.Types) != len(a.req.Types) {
		return false
	}
	gotTypesMap := make(map[accountsPb.Type]bool)
	for _, t := range got.Types {
		gotTypesMap[t] = true
	}
	for _, expectedType := range a.req.Types {
		if !gotTypesMap[expectedType] {
			return false
		}
	}

	// Check States - should contain all states except unspecified
	gotStatesMap := make(map[depositPb.DepositState]bool)
	for _, state := range got.States {
		gotStatesMap[state] = true
	}
	for key, value := range depositPb.DepositState_value {
		if key != depositPb.DepositState_STATE_UNSPECIFIED.String() {
			if !gotStatesMap[depositPb.DepositState(value)] {
				return false
			}
		}
	}

	// Check Provenances - should contain all provenances except unspecified
	gotProvenancesMap := make(map[depositPb.DepositAccountProvenance]bool)
	for _, prov := range got.Provenances {
		gotProvenancesMap[prov] = true
	}
	for key, value := range depositPb.DepositAccountProvenance_value {
		if key != depositPb.DepositAccountProvenance_DEPOSIT_ACCOUNT_PROVENANCE_UNSPECIFIED.String() {
			if !gotProvenancesMap[depositPb.DepositAccountProvenance(value)] {
				return false
			}
		}
	}

	return true
}

func (a *ArgMatcher) String() string {
	return fmt.Sprintf("is %s", a.req)
}

type mockDepositServices struct {
	depositClient *depositMocks.MockDepositClient
	paymentClient *paymentMocks.MockPaymentClient
	piClient      *piMocks.MockPiClient
	config        *genconf.Config
}

func getTestDepositDataCollectorWithMock(t *testing.T) (*DepositDataCollector, *mockDepositServices) {
	ctrl := gomock.NewController(t)

	mockDepositClient := depositMocks.NewMockDepositClient(ctrl)
	mockPaymentClient := paymentMocks.NewMockPaymentClient(ctrl)
	mockPiClient := piMocks.NewMockPiClient(ctrl)

	// Create config using the standard pattern from other tests
	config := &genconf.Config{}
	config.Init("test")

	// Create a minimal static config with the required Nugget configuration
	staticConfig := &vendornotificationConfig.Config{
		Nugget: &vendornotificationConfig.Nugget{
			MaxDepositsToProcess: 5, // Use the same value as in the config file
		},
	}

	// Set the static config into the dynamic config
	err := config.Set(staticConfig, false, nil)
	if err != nil {
		t.Fatalf("Failed to set config: %v", err)
	}

	collector := NewDepositDataCollector(mockDepositClient, mockPaymentClient, mockPiClient, config)

	mocks := &mockDepositServices{
		depositClient: mockDepositClient,
		paymentClient: mockPaymentClient,
		piClient:      mockPiClient,
		config:        config,
	}

	return collector, mocks
}

func TestDepositDataCollector_CollectData(t *testing.T) {
	t.Parallel()
	ctx := context.Background()

	testCases := []struct {
		name           string
		params         options.DataCollectorParams
		mocks          func(mocks *mockDepositServices)
		expectedResult *DepositData
		expectedError  string
	}{
		{
			name: "successful data collection with FD type requested",
			params: options.DataCollectorParams{
				ActorId:     testDepositActorId,
				DepositType: "FIXED_DEPOSIT",
			},
			mocks: func(mocks *mockDepositServices) {
				principalAmount := money.ParseFloat(100000.0, "INR")
				maturityAmount := money.ParseFloat(106500.0, "INR")
				maturityDate := timestamppb.New(time.Date(2024, 12, 15, 0, 0, 0, 0, time.UTC))

				mocks.depositClient.EXPECT().ListDepositAccounts(gomock.Any(), &ArgMatcher{req: &depositPb.ListDepositAccountsRequest{
					ActorId: testDepositActorId,
					Types:   []accountsPb.Type{accountsPb.Type_FIXED_DEPOSIT},
				}}).Return(&depositPb.ListDepositAccountsResponse{
					Status: &rpcPb.Status{Code: 0},
					Accounts: []*depositPb.DepositAccount{
						{
							Id:              testDepositAccountId,
							Name:            testDepositName,
							Type:            accountsPb.Type_FIXED_DEPOSIT,
							State:           depositPb.DepositState_CREATED,
							SchemeCode:      depositPb.DepositScheme_FD_QUARTERLY_INTEREST,
							PrincipalAmount: principalAmount,
							RunningBalance:  principalAmount,
							MaturityAmount:  maturityAmount,
							MaturityDate:    maturityDate,
							InterestRate:    testDepositInterestRate,
							RenewInfo:       &depositPb.RenewInfo{IsAutoRenewable: false},
							Provenance:      depositPb.DepositAccountProvenance_CREDIT_CARD, // CC linked
							AccountNumber:   "**********",
							IfscCode:        "HDFC0000123",
						},
					},
				}, nil)

				// Mock payment instrument call (always called now)
				mocks.piClient.EXPECT().GetPi(gomock.Any(), gomock.Any()).Return(nil, errors.New("no payment instrument found"))
			},
			expectedResult: &DepositData{
				DepositList: []DepositInfo{
					{
						AccountID:               testDepositAccountId,
						DepositName:             testDepositName,
						Status:                  "CREATED",
						IsDepositLinkedToCC:     "true",
						IsDepositAutoRenewed:    "false",
						InterestPayoutFrequency: "FD_QUARTERLY_INTEREST",
						RunningBalance:          "₹100,000.00",
						MaturityDate:            "**********", // epoch timestamp for 2024-12-15
						IsTDSDeducted:           "false",
					},
				},
			},
		},
		{
			name: "successful data collection with SD type requested",
			params: options.DataCollectorParams{
				ActorId:     testDepositActorId,
				DepositType: "SMART_DEPOSIT",
			},
			mocks: func(mocks *mockDepositServices) {
				principalAmount := money.ParseFloat(50000.0, "INR")
				maturityDate := timestamppb.New(time.Date(2025, 6, 30, 0, 0, 0, 0, time.UTC))

				// Mock payment instrument call (always called now)
				mocks.piClient.EXPECT().GetPi(gomock.Any(), gomock.Any()).Return(nil, errors.New("no payment instrument found")).AnyTimes()

				mocks.depositClient.EXPECT().ListDepositAccounts(gomock.Any(), &ArgMatcher{req: &depositPb.ListDepositAccountsRequest{
					ActorId: testDepositActorId,
					Types:   []accountsPb.Type{accountsPb.Type_SMART_DEPOSIT},
				}}).Return(&depositPb.ListDepositAccountsResponse{
					Status: &rpcPb.Status{Code: 0},
					Accounts: []*depositPb.DepositAccount{
						{
							Id:              testDepositAccountId,
							Name:            testDepositName,
							Type:            accountsPb.Type_SMART_DEPOSIT,
							State:           depositPb.DepositState_CREATED,
							SchemeCode:      depositPb.DepositScheme_FD_MONTHLY_INTEREST,
							PrincipalAmount: principalAmount,
							RunningBalance:  principalAmount,
							MaturityDate:    maturityDate,
							InterestRate:    testDepositInterestRate,
							RenewInfo:       &depositPb.RenewInfo{IsAutoRenewable: true},
							Provenance:      depositPb.DepositAccountProvenance_USER_APP, // Not CC linked
							AccountNumber:   "**********",
							IfscCode:        "HDFC0000123",
						},
					},
				}, nil)
			},
			expectedResult: &DepositData{
				DepositList: []DepositInfo{
					{
						AccountID:               testDepositAccountId,
						DepositName:             testDepositName,
						Status:                  "CREATED",
						IsDepositLinkedToCC:     "false",
						IsDepositAutoRenewed:    "true",
						InterestPayoutFrequency: "FD_MONTHLY_INTEREST",
						RunningBalance:          "₹50,000.00",
						MaturityDate:            "**********", // epoch timestamp for 2025-06-30
						IsTDSDeducted:           "false",
					},
				},
			},
		},
		{
			name: "successful data collection with RD type requested",
			params: options.DataCollectorParams{
				ActorId:     testDepositActorId,
				DepositType: "RECURRING_DEPOSIT",
			},
			mocks: func(mocks *mockDepositServices) {
				principalAmount := money.ParseFloat(25000.0, "INR")
				maturityDate := timestamppb.New(time.Date(2026, 1, 15, 0, 0, 0, 0, time.UTC))

				// Mock payment instrument call (always called now)
				mocks.piClient.EXPECT().GetPi(gomock.Any(), gomock.Any()).Return(nil, errors.New("no payment instrument found")).AnyTimes()

				mocks.depositClient.EXPECT().ListDepositAccounts(gomock.Any(), &ArgMatcher{req: &depositPb.ListDepositAccountsRequest{
					ActorId: testDepositActorId,
					Types:   []accountsPb.Type{accountsPb.Type_RECURRING_DEPOSIT},
				}}).Return(&depositPb.ListDepositAccountsResponse{
					Status: &rpcPb.Status{Code: 0},
					Accounts: []*depositPb.DepositAccount{
						{
							Id:              testDepositAccountId,
							Name:            testDepositName,
							Type:            accountsPb.Type_RECURRING_DEPOSIT,
							State:           depositPb.DepositState_IN_PROGRESS,
							SchemeCode:      depositPb.DepositScheme_FD_INTEREST_ON_MATURITY,
							PrincipalAmount: principalAmount,
							RunningBalance:  principalAmount,
							MaturityDate:    maturityDate,
							InterestRate:    testDepositInterestRate,
							RenewInfo:       &depositPb.RenewInfo{IsAutoRenewable: true},
							Provenance:      depositPb.DepositAccountProvenance_CREDIT_CARD_NON_SA, // CC_NON_SA linked
							AccountNumber:   "**********",
							IfscCode:        "HDFC0000123",
						},
					},
				}, nil)
			},
			expectedResult: &DepositData{
				DepositList: []DepositInfo{
					{
						AccountID:               testDepositAccountId,
						DepositName:             testDepositName,
						Status:                  "IN_PROGRESS",
						IsDepositLinkedToCC:     "true",
						IsDepositAutoRenewed:    "true",
						InterestPayoutFrequency: "FD_INTEREST_ON_MATURITY",
						RunningBalance:          "₹25,000.00",
						MaturityDate:            "**********", // epoch timestamp for 2026-01-15
						IsTDSDeducted:           "false",
					},
				},
			},
		},
		{
			name: "successful data collection with no deposits of requested type",
			params: options.DataCollectorParams{
				ActorId:     testDepositActorId,
				DepositType: "FIXED_DEPOSIT",
			},
			mocks: func(mocks *mockDepositServices) {
				// Use gomock.Any() for provenances since the order may vary with dynamic generation
				mocks.depositClient.EXPECT().ListDepositAccounts(gomock.Any(), &ArgMatcher{req: &depositPb.ListDepositAccountsRequest{
					ActorId: testDepositActorId,
					Types:   []accountsPb.Type{accountsPb.Type_FIXED_DEPOSIT},
				}}).Return(&depositPb.ListDepositAccountsResponse{
					Status:   &rpcPb.Status{Code: 0},
					Accounts: []*depositPb.DepositAccount{},
				}, nil)
			},
			expectedResult: &DepositData{
				DepositList: []DepositInfo{},
			},
		},
		{
			name: "invalid deposit type parameter",
			params: options.DataCollectorParams{
				ActorId:     testDepositActorId,
				DepositType: "INVALID",
			},
			mocks:         func(mocks *mockDepositServices) {},
			expectedError: "invalid deposit_type parameter",
		},
		{
			name: "missing deposit type parameter",
			params: options.DataCollectorParams{
				ActorId: testDepositActorId,
				// DepositType missing
			},
			mocks:         func(mocks *mockDepositServices) {},
			expectedError: "deposit_type parameter is required",
		},
		{
			name: "missing actor ID",
			params: options.DataCollectorParams{
				DepositType: "FIXED_DEPOSIT",
				// ActorId missing
			},
			mocks:         func(mocks *mockDepositServices) {},
			expectedError: "actorId is required",
		},
		{
			name: "successful data collection with deposit limit applied",
			params: options.DataCollectorParams{
				ActorId:     testDepositActorId,
				DepositType: "FIXED_DEPOSIT",
			},
			mocks: func(mocks *mockDepositServices) {
				principalAmount := money.ParseFloat(10000.0, "INR")
				maturityDate := timestamppb.New(time.Date(2024, 12, 15, 0, 0, 0, 0, time.UTC))

				// Mock payment instrument call (always called now)
				mocks.piClient.EXPECT().GetPi(gomock.Any(), gomock.Any()).Return(nil, errors.New("no payment instrument found")).AnyTimes()

				// Create more than MaxDepositsToProcess deposits to test the limit
				var accounts []*depositPb.DepositAccount
				for i := 0; i < 15; i++ { // Create 15 deposits, expect only MaxDepositsToProcess to be processed
					accounts = append(accounts, &depositPb.DepositAccount{
						Id:              fmt.Sprintf("FD%03d", i+1),
						Name:            fmt.Sprintf("Fixed Deposit %d", i+1),
						Type:            accountsPb.Type_FIXED_DEPOSIT,
						State:           depositPb.DepositState_CREATED,
						SchemeCode:      depositPb.DepositScheme_FD_QUARTERLY_INTEREST,
						PrincipalAmount: principalAmount,
						RunningBalance:  principalAmount,
						MaturityDate:    maturityDate,
						InterestRate:    testDepositInterestRate,
						RenewInfo:       &depositPb.RenewInfo{IsAutoRenewable: false},
						Provenance:      depositPb.DepositAccountProvenance_USER_APP,
						AccountNumber:   "**********",
						IfscCode:        "HDFC0000123",
					})
				}

				// Use gomock.Any() for provenances since the order may vary with dynamic generation
				mocks.depositClient.EXPECT().ListDepositAccounts(gomock.Any(), &ArgMatcher{req: &depositPb.ListDepositAccountsRequest{
					ActorId: testDepositActorId,
					Types:   []accountsPb.Type{accountsPb.Type_FIXED_DEPOSIT},
				}}).Return(&depositPb.ListDepositAccountsResponse{
					Status:   &rpcPb.Status{Code: 0},
					Accounts: accounts, // Return 15 accounts
				}, nil)
			},
			expectedResult: func() *DepositData {
				// Expect only MaxDepositsToProcess (5) deposits in response
				var expectedList []DepositInfo
				for i := 0; i < 5; i++ { // Only first 5 should be processed
					expectedList = append(expectedList, DepositInfo{
						AccountID:               fmt.Sprintf("FD%03d", i+1),
						DepositName:             fmt.Sprintf("Fixed Deposit %d", i+1),
						Status:                  "CREATED",
						IsDepositLinkedToCC:     "false",
						IsDepositAutoRenewed:    "false",
						InterestPayoutFrequency: "FD_QUARTERLY_INTEREST",
						RunningBalance:          "₹10,000.00",
						MaturityDate:            "**********",
						IsTDSDeducted:           "false",
					})
				}
				return &DepositData{DepositList: expectedList}
			}(),
		},
		{
			name: "deposit service error",
			params: options.DataCollectorParams{
				ActorId:     testDepositActorId,
				DepositType: "FIXED_DEPOSIT",
			},
			mocks: func(mocks *mockDepositServices) {
				mocks.depositClient.EXPECT().ListDepositAccounts(gomock.Any(), &ArgMatcher{req: &depositPb.ListDepositAccountsRequest{
					ActorId: testDepositActorId,
					Types:   []accountsPb.Type{accountsPb.Type_FIXED_DEPOSIT},
				}}).Return(nil, errors.New("deposit service error"))
			},
			expectedError: "failed to get deposit list",
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			collector, mocks := getTestDepositDataCollectorWithMock(t)
			tc.mocks(mocks)

			result, err := collector.CollectData(ctx, tc.params)

			if tc.expectedError != "" {
				require.Error(t, err)
				require.Contains(t, err.Error(), tc.expectedError)
				require.Nil(t, result)
			} else {
				require.NoError(t, err)
				require.NotNil(t, result)

				depositData, ok := result.(*DepositData)
				require.True(t, ok)
				require.Equal(t, tc.expectedResult, depositData)
			}
		})
	}
}
