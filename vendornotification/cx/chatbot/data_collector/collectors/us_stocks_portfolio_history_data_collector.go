package collectors

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	usstocksPb "github.com/epifi/gamma/api/usstocks"
	ussOrderPb "github.com/epifi/gamma/api/usstocks/order"
	ussPortfolioPb "github.com/epifi/gamma/api/usstocks/portfolio"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type UsStocksPortfolioHistoryData struct {
	TradingHistory []TradingRecord `json:"trading_history"`
}

type TradingRecord struct {
	Symbol         string `json:"symbol"`
	OrderTimestamp string `json:"order_timestamp"`
	ActivityType   string `json:"activity_type"`
}

type UsStocksPortfolioHistoryDataCollector struct {
	usStocksPortfolioClient ussPortfolioPb.PortfolioManagerClient
	usStocksOrderClient     ussOrderPb.OrderManagerClient
}

func NewUsStocksPortfolioHistoryDataCollector(
	usStocksPortfolioClient ussPortfolioPb.PortfolioManagerClient,
	usStocksOrderClient ussOrderPb.OrderManagerClient,
) *UsStocksPortfolioHistoryDataCollector {
	return &UsStocksPortfolioHistoryDataCollector{
		usStocksPortfolioClient: usStocksPortfolioClient,
		usStocksOrderClient:     usStocksOrderClient,
	}
}

func (c *UsStocksPortfolioHistoryDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	portfolioResp, err := c.usStocksPortfolioClient.GetAllOpenPositions(ctx, &ussPortfolioPb.GetAllOpenPositionsRequest{
		ActorId: actorId,
		SortBy:  usstocksPb.PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_UNSPECIFIED,
	})

	if rpcErr := epifigrpc.RPCError(portfolioResp, err); rpcErr != nil {
		logger.Error(ctx, "error calling US stocks portfolio service", zap.String(logger.ACTOR_ID, actorId), zap.Error(rpcErr))
		return nil, errors.Wrap(rpcErr, "failed to get US stocks portfolio")
	}

	if len(portfolioResp.GetPositions()) == 0 {
		return &UsStocksPortfolioHistoryData{
			TradingHistory: []TradingRecord{},
		}, nil
	}

	var tradingHistory []TradingRecord

	for _, position := range portfolioResp.GetPositions() {
		symbol := position.GetSymbol()
		if symbol == "" {
			continue
		}

		symbolOrdersResp, err := c.usStocksOrderClient.GetOrders(ctx, &ussOrderPb.GetOrdersRequest{
			ActorId: actorId,
			Symbols: []string{symbol},
			PageContext: &rpc.PageContextRequest{
				PageSize: 100,
			},
		})

		if rpcErr := epifigrpc.RPCError(symbolOrdersResp, err); rpcErr != nil {
			logger.Error(ctx, "error calling US stocks order service for symbol",
				zap.String(logger.ACTOR_ID, actorId),
				zap.String("symbol", symbol),
				zap.Error(rpcErr))
			continue
		}

		for _, order := range symbolOrdersResp.GetOrders() {
			activityType := "buy"
			if order.GetSide() == usstocksPb.OrderSide_SELL {
				activityType = "sell"
			}
			tradingRecord := TradingRecord{
				Symbol:         symbol,
				OrderTimestamp: order.GetCreatedAt().AsTime().Format("2006-01-02T15:04:05Z"),
				ActivityType:   activityType,
			}
			tradingHistory = append(tradingHistory, tradingRecord)
		}
	}
	return &UsStocksPortfolioHistoryData{
		TradingHistory: tradingHistory,
	}, nil
}
