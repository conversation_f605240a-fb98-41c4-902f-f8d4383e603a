package collectors

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	ussAccPb "github.com/epifi/gamma/api/usstocks/account"
	ussAccMocks "github.com/epifi/gamma/api/usstocks/account/mocks"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

const testActorIdOnboarding = "test-actor-us-stocks-123"

type mockDependenciesOnboarding struct {
	accountClient *ussAccMocks.MockAccountManagerClient
}

func newOnboardingCollectorWithMocks(t *testing.T) (*UsStocksOnboardingStatusDataCollector, *mockDependenciesOnboarding) {
	ctrl := gomock.NewController(t)
	accountClient := ussAccMocks.NewMockAccountManagerClient(ctrl)

	collector := NewUsStocksOnboardingStatusDataCollector(accountClient)

	return collector, &mockDependenciesOnboarding{
		accountClient: accountClient,
	}
}

func createAccountResponse(actorId string, accountStatus ussAccPb.AccountStatus, vendorAccountId string) *ussAccPb.GetAccountResponse {
	return &ussAccPb.GetAccountResponse{
		Status: rpc.StatusOk(),
		Account: &ussAccPb.Account{
			Id:              "account-123",
			ActorId:         actorId,
			AccountStatus:   accountStatus,
			Vendor:          commonvgpb.Vendor_ALPACA,
			VendorAccountId: vendorAccountId,
		},
	}
}

func createExpectedResponse(state, stage string) *UsStocksOnboardingStatusData {
	return &UsStocksOnboardingStatusData{
		State: state,
		Stage: stage,
	}
}

func TestUsStocksOnboardingStatusDataCollector_CollectData(t *testing.T) {
	t.Parallel()
	logger.Init(cfg.TestEnv)

	type args struct {
		ctx         context.Context
		inputParams options.DataCollectorParams
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(md *mockDependenciesOnboarding)
		want    *UsStocksOnboardingStatusData
		wantErr error
	}{
		{
			name: "success: active account with vendor ID",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdOnboarding,
				},
			},
			mocks: func(md *mockDependenciesOnboarding) {
				md.accountClient.EXPECT().GetAccount(
					context.Background(),
					&ussAccPb.GetAccountRequest{
						ActorId: testActorIdOnboarding,
						Vendor:  commonvgpb.Vendor_ALPACA,
					},
				).Return(createAccountResponse(testActorIdOnboarding, ussAccPb.AccountStatus_ACTIVE, "alpaca-account-123"), nil)
			},
			want:    createExpectedResponse("ACTIVE", "COMPLETED"),
			wantErr: nil,
		},
		{
			name: "success: active account without vendor ID",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: testActorIdOnboarding,
				},
			},
			mocks: func(md *mockDependenciesOnboarding) {
				md.accountClient.EXPECT().GetAccount(
					context.Background(),
					&ussAccPb.GetAccountRequest{
						ActorId: testActorIdOnboarding,
						Vendor:  commonvgpb.Vendor_ALPACA,
					},
				).Return(createAccountResponse(testActorIdOnboarding, ussAccPb.AccountStatus_ACTIVE, ""), nil)
			},
			want:    createExpectedResponse("INACTIVE", "ACTIVE"),
			wantErr: nil,
		},
		{
			name: "error: missing actor id",
			args: args{
				ctx:         context.Background(),
				inputParams: options.DataCollectorParams{},
			},
			mocks:   func(md *mockDependenciesOnboarding) {},
			want:    nil,
			wantErr: fmt.Errorf("actorId is required"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			dataCollector, md := newOnboardingCollectorWithMocks(t)

			tt.mocks(md)

			got, err := dataCollector.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.wantErr != nil {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.wantErr.Error())
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				gotData, ok := got.(*UsStocksOnboardingStatusData)
				require.True(t, ok)
				require.Equal(t, tt.want.State, gotData.State)
				require.Equal(t, tt.want.Stage, gotData.Stage)
			}
		})
	}
}
