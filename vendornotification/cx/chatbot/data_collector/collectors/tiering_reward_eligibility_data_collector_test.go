package collectors

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	datetimeMocks "github.com/epifi/be-common/pkg/datetime/mocks"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	tieringMocks "github.com/epifi/gamma/api/tiering/mocks"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

// getPrevMonthStartAndEndFromCurrentTime calculates the start and end times of the previous month
// given a current time. This matches the same logic used in the TieringRewardEligibilityDataCollector implementation.
func getPrevMonthStartAndEndFromCurrentTime(currentTime time.Time) (previousMonthStart, previousMonthEnd time.Time) {
	currentTimeIST := currentTime.In(datetime.IST)
	currentMonthStart := datetime.StartOfMonth(currentTimeIST)
	previousMonth := currentMonthStart.AddDate(0, -1, 0)
	previousMonthStart = datetime.StartOfMonth(previousMonth)
	previousMonthEnd = datetime.EndOfMonth(previousMonth)
	return previousMonthStart, previousMonthEnd
}

func TestTieringRewardEligibilityDataCollector_CollectData(t *testing.T) {
	t.Parallel()

	type fields struct {
		tieringClient *tieringMocks.MockTieringClient
		timeImpl      *datetimeMocks.MockTime
	}
	type args struct {
		ctx         context.Context
		inputParams options.DataCollectorParams
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		setup   func(f *fields)
		want    any
		wantErr bool
	}{
		{
			name: "success - eligible for cashback reward with single tier",
			fields: fields{
				tieringClient: nil, // will be set in setup
				timeImpl:      nil, // will be set in setup
			},
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-123",
				},
			},
			setup: func(f *fields) {
				actorId := "test-actor-123"

				// Define the mock time for this test
				mockTime := time.Date(2024, 4, 15, 10, 30, 0, 0, datetime.IST) // April 15, 2024

				// Calculate expected dates based on the mock time (same logic as implementation)
				previousMonthStart, previousMonthEnd := getPrevMonthStartAndEndFromCurrentTime(mockTime)

				// Mock time.Now() call
				f.timeImpl.EXPECT().Now().Return(mockTime).Times(1)

				// Create expected requests using the calculated dates
				expectedDistinctTiersReq := &tieringPb.GetActorDistinctTiersRequest{
					ActorId:   actorId,
					TimeSince: timestamppb.New(previousMonthStart),
					TimeTill:  timestamppb.New(previousMonthEnd),
				}

				expectedCashbackReq := &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
					ActorId:     actorId,
					Tier:        tieringExtPb.Tier_TIER_FI_INFINITE,
					RewardMonth: timestamppb.New(previousMonthStart),
				}

				// Mock GetActorDistinctTiers with specific request
				f.tieringClient.EXPECT().
					GetActorDistinctTiers(context.Background(), expectedDistinctTiersReq).
					Return(&tieringPb.GetActorDistinctTiersResponse{
						Status:        rpc.StatusOk(),
						DistinctTiers: []tieringExtPb.Tier{tieringExtPb.Tier_TIER_FI_INFINITE},
					}, nil)

				// Mock CheckIfActorIsEligibleForCashbackReward with specific request
				f.tieringClient.EXPECT().
					CheckIfActorIsEligibleForCashbackReward(context.Background(), expectedCashbackReq).
					Return(&tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse{
						Status:     rpc.StatusOk(),
						IsEligible: true,
					}, nil)
			},
			want: &TieringRewardEligibilityData{
				IsEligible: "true",
			},
			wantErr: false,
		},
		{
			name: "success - March 31st edge case - eligible for cashback reward",
			fields: fields{
				tieringClient: nil, // will be set in setup
				timeImpl:      nil, // will be set in setup
			},
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-march31",
				},
			},
			setup: func(f *fields) {
				actorId := "test-actor-march31"

				// Define the mock time for this test - March 31st edge case
				mockTime := time.Date(2024, 3, 31, 23, 59, 59, 0, datetime.IST) // March 31, 2024

				// Calculate expected dates based on the mock time (same logic as implementation)
				// This tests the edge case where March 31 - 1 month should correctly give February
				previousMonthStart, previousMonthEnd := getPrevMonthStartAndEndFromCurrentTime(mockTime)

				// Mock time.Now() call
				f.timeImpl.EXPECT().Now().Return(mockTime).Times(1)

				// Expected dates: previous month should be February 2024
				// previousMonthStart should be Feb 1, 2024
				// previousMonthEnd should be Feb 29, 2024 (2024 is a leap year)
				expectedDistinctTiersReq := &tieringPb.GetActorDistinctTiersRequest{
					ActorId:   actorId,
					TimeSince: timestamppb.New(previousMonthStart),
					TimeTill:  timestamppb.New(previousMonthEnd),
				}

				expectedCashbackReq := &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
					ActorId:     actorId,
					Tier:        tieringExtPb.Tier_TIER_FI_INFINITE,
					RewardMonth: timestamppb.New(previousMonthStart),
				}

				// Mock GetActorDistinctTiers with specific request
				f.tieringClient.EXPECT().
					GetActorDistinctTiers(context.Background(), expectedDistinctTiersReq).
					Return(&tieringPb.GetActorDistinctTiersResponse{
						Status:        rpc.StatusOk(),
						DistinctTiers: []tieringExtPb.Tier{tieringExtPb.Tier_TIER_FI_INFINITE},
					}, nil)

				// Mock CheckIfActorIsEligibleForCashbackReward with specific request
				f.tieringClient.EXPECT().
					CheckIfActorIsEligibleForCashbackReward(context.Background(), expectedCashbackReq).
					Return(&tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse{
						Status:     rpc.StatusOk(),
						IsEligible: true,
					}, nil)
			},
			want: &TieringRewardEligibilityData{
				IsEligible: "true",
			},
			wantErr: false,
		},
		{
			name: "success - not eligible for cashback reward",
			fields: fields{
				tieringClient: nil, // will be set in setup
				timeImpl:      nil, // will be set in setup
			},
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-456",
				},
			},
			setup: func(f *fields) {
				actorId := "test-actor-456"

				// Define the mock time for this test
				mockTime := time.Date(2024, 5, 20, 14, 0, 0, 0, datetime.IST) // May 20, 2024

				// Calculate expected dates based on the mock time (same logic as implementation)
				previousMonthStart, previousMonthEnd := getPrevMonthStartAndEndFromCurrentTime(mockTime)

				// Mock time.Now() call
				f.timeImpl.EXPECT().Now().Return(mockTime).Times(1)

				// Create expected requests using the calculated dates
				expectedDistinctTiersReq := &tieringPb.GetActorDistinctTiersRequest{
					ActorId:   actorId,
					TimeSince: timestamppb.New(previousMonthStart),
					TimeTill:  timestamppb.New(previousMonthEnd),
				}

				expectedCashbackReq := &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
					ActorId:     actorId,
					Tier:        tieringExtPb.Tier_TIER_FI_INFINITE,
					RewardMonth: timestamppb.New(previousMonthStart),
				}

				// Mock GetActorDistinctTiers with specific request
				f.tieringClient.EXPECT().
					GetActorDistinctTiers(context.Background(), expectedDistinctTiersReq).
					Return(&tieringPb.GetActorDistinctTiersResponse{
						Status:        rpc.StatusOk(),
						DistinctTiers: []tieringExtPb.Tier{tieringExtPb.Tier_TIER_FI_INFINITE},
					}, nil)

				// Mock CheckIfActorIsEligibleForCashbackReward with specific request
				f.tieringClient.EXPECT().
					CheckIfActorIsEligibleForCashbackReward(context.Background(), expectedCashbackReq).
					Return(&tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse{
						Status:     rpc.StatusOk(),
						IsEligible: false,
					}, nil)
			},
			want: &TieringRewardEligibilityData{
				IsEligible: "false",
			},
			wantErr: false,
		},
		{
			name: "success - multiple tiers, first one eligible (early return)",
			fields: fields{
				tieringClient: nil, // will be set in setup
				timeImpl:      nil, // will be set in setup
			},
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-789",
				},
			},
			setup: func(f *fields) {
				actorId := "test-actor-789"

				// Define the mock time for this test
				mockTime := time.Date(2024, 7, 10, 9, 15, 0, 0, datetime.IST) // July 10, 2024

				// Calculate expected dates based on the mock time (same logic as implementation)
				previousMonthStart, previousMonthEnd := getPrevMonthStartAndEndFromCurrentTime(mockTime)

				// Mock time.Now() call
				f.timeImpl.EXPECT().Now().Return(mockTime).Times(1)

				// Create expected requests using the calculated dates
				expectedDistinctTiersReq := &tieringPb.GetActorDistinctTiersRequest{
					ActorId:   actorId,
					TimeSince: timestamppb.New(previousMonthStart),
					TimeTill:  timestamppb.New(previousMonthEnd),
				}

				expectedCashbackReq := &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
					ActorId:     actorId,
					Tier:        tieringExtPb.Tier_TIER_FI_INFINITE,
					RewardMonth: timestamppb.New(previousMonthStart),
				}

				// Mock GetActorDistinctTiers with specific request
				f.tieringClient.EXPECT().
					GetActorDistinctTiers(context.Background(), expectedDistinctTiersReq).
					Return(&tieringPb.GetActorDistinctTiersResponse{
						Status: rpc.StatusOk(),
						DistinctTiers: []tieringExtPb.Tier{
							tieringExtPb.Tier_TIER_FI_INFINITE,
							tieringExtPb.Tier_TIER_FI_PLUS,
						},
					}, nil)

				// Mock CheckIfActorIsEligibleForCashbackReward - only first call should happen due to early return
				f.tieringClient.EXPECT().
					CheckIfActorIsEligibleForCashbackReward(context.Background(), expectedCashbackReq).
					Return(&tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse{
						Status:     rpc.StatusOk(),
						IsEligible: true,
					}, nil).Times(1)
			},
			want: &TieringRewardEligibilityData{
				IsEligible: "true",
			},
			wantErr: false,
		},
		{
			name: "success - tier not cashback eligible",
			fields: fields{
				tieringClient: nil, // will be set in setup
				timeImpl:      nil, // will be set in setup
			},
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-basic",
				},
			},
			setup: func(f *fields) {
				actorId := "test-actor-basic"

				// Define the mock time for this test
				mockTime := time.Date(2024, 8, 5, 16, 45, 0, 0, datetime.IST) // August 5, 2024

				// Calculate expected dates based on the mock time (same logic as implementation)
				previousMonthStart, previousMonthEnd := getPrevMonthStartAndEndFromCurrentTime(mockTime)

				// Mock time.Now() call
				f.timeImpl.EXPECT().Now().Return(mockTime).Times(1)

				// Create expected request using the calculated dates
				expectedDistinctTiersReq := &tieringPb.GetActorDistinctTiersRequest{
					ActorId:   actorId,
					TimeSince: timestamppb.New(previousMonthStart),
					TimeTill:  timestamppb.New(previousMonthEnd),
				}

				// Mock GetActorDistinctTiers with a non-cashback eligible tier
				f.tieringClient.EXPECT().
					GetActorDistinctTiers(context.Background(), expectedDistinctTiersReq).
					Return(&tieringPb.GetActorDistinctTiersResponse{
						Status:        rpc.StatusOk(),
						DistinctTiers: []tieringExtPb.Tier{tieringExtPb.Tier_TIER_FI_BASIC},
					}, nil)

				// Should not call CheckIfActorIsEligibleForCashbackReward for non-cashback eligible tier
			},
			want: &TieringRewardEligibilityData{
				IsEligible: "false",
			},
			wantErr: false,
		},
		{
			name: "success - multiple tiers, mixed cashback eligible, second tier eligible",
			fields: fields{
				tieringClient: nil, // will be set in setup
				timeImpl:      nil, // will be set in setup
			},
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-mixed",
				},
			},
			setup: func(f *fields) {
				actorId := "test-actor-mixed"

				// Define the mock time for this test
				mockTime := time.Date(2024, 9, 12, 11, 30, 0, 0, datetime.IST) // September 12, 2024

				// Calculate expected dates based on the mock time (same logic as implementation)
				previousMonthStart, previousMonthEnd := getPrevMonthStartAndEndFromCurrentTime(mockTime)

				// Mock time.Now() call
				f.timeImpl.EXPECT().Now().Return(mockTime).Times(1)

				// Create expected requests using the calculated dates
				expectedDistinctTiersReq := &tieringPb.GetActorDistinctTiersRequest{
					ActorId:   actorId,
					TimeSince: timestamppb.New(previousMonthStart),
					TimeTill:  timestamppb.New(previousMonthEnd),
				}

				expectedCashbackReq := &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
					ActorId:     actorId,
					Tier:        tieringExtPb.Tier_TIER_FI_INFINITE,
					RewardMonth: timestamppb.New(previousMonthStart),
				}

				// Mock GetActorDistinctTiers with mix of cashback eligible and non-eligible tiers
				f.tieringClient.EXPECT().
					GetActorDistinctTiers(context.Background(), expectedDistinctTiersReq).
					Return(&tieringPb.GetActorDistinctTiersResponse{
						Status: rpc.StatusOk(),
						DistinctTiers: []tieringExtPb.Tier{
							tieringExtPb.Tier_TIER_FI_BASIC,    // not cashback eligible
							tieringExtPb.Tier_TIER_FI_INFINITE, // cashback eligible
						},
					}, nil)

				// Should only call CheckIfActorIsEligibleForCashbackReward for the cashback eligible tier
				f.tieringClient.EXPECT().
					CheckIfActorIsEligibleForCashbackReward(context.Background(), expectedCashbackReq).
					Return(&tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse{
						Status:     rpc.StatusOk(),
						IsEligible: true,
					}, nil).Times(1)
			},
			want: &TieringRewardEligibilityData{
				IsEligible: "true",
			},
			wantErr: false,
		},
		{
			name: "success - no distinct tiers found",
			fields: fields{
				tieringClient: nil, // will be set in setup
				timeImpl:      nil, // will be set in setup
			},
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-no-tiers",
				},
			},
			setup: func(f *fields) {
				actorId := "test-actor-no-tiers"

				// Define the mock time for this test
				mockTime := time.Date(2024, 10, 25, 8, 0, 0, 0, datetime.IST) // October 25, 2024

				// Calculate expected dates based on the mock time (same logic as implementation)
				previousMonthStart, previousMonthEnd := getPrevMonthStartAndEndFromCurrentTime(mockTime)

				// Mock time.Now() call
				f.timeImpl.EXPECT().Now().Return(mockTime).Times(1)

				// Create expected request using the calculated dates
				expectedDistinctTiersReq := &tieringPb.GetActorDistinctTiersRequest{
					ActorId:   actorId,
					TimeSince: timestamppb.New(previousMonthStart),
					TimeTill:  timestamppb.New(previousMonthEnd),
				}

				// Mock GetActorDistinctTiers with empty response
				f.tieringClient.EXPECT().
					GetActorDistinctTiers(context.Background(), expectedDistinctTiersReq).
					Return(&tieringPb.GetActorDistinctTiersResponse{
						Status:        rpc.StatusOk(),
						DistinctTiers: []tieringExtPb.Tier{},
					}, nil)
			},
			want: &TieringRewardEligibilityData{
				IsEligible: "false",
			},
			wantErr: false,
		},
		{
			name: "error - empty actor id",
			fields: fields{
				tieringClient: nil, // will be set in setup
				timeImpl:      nil, // will be set in setup
			},
			args: args{
				ctx:         context.Background(),
				inputParams: options.DataCollectorParams{},
			},
			setup: func(f *fields) {
				// No time mocking needed for this test case as it fails before time.Now() is called
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error - GetActorDistinctTiers fails",
			fields: fields{
				tieringClient: nil, // will be set in setup
				timeImpl:      nil, // will be set in setup
			},
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-error",
				},
			},
			setup: func(f *fields) {
				actorId := "test-actor-error"

				// Define the mock time for this test
				mockTime := time.Date(2024, 11, 15, 13, 45, 0, 0, datetime.IST) // November 15, 2024

				// Calculate expected dates based on the mock time (same logic as implementation)
				previousMonthStart, previousMonthEnd := getPrevMonthStartAndEndFromCurrentTime(mockTime)

				// Mock time.Now() call
				f.timeImpl.EXPECT().Now().Return(mockTime).Times(1)

				// Create expected request using the calculated dates
				expectedDistinctTiersReq := &tieringPb.GetActorDistinctTiersRequest{
					ActorId:   actorId,
					TimeSince: timestamppb.New(previousMonthStart),
					TimeTill:  timestamppb.New(previousMonthEnd),
				}

				// Mock GetActorDistinctTiers with error
				f.tieringClient.EXPECT().
					GetActorDistinctTiers(context.Background(), expectedDistinctTiersReq).
					Return(&tieringPb.GetActorDistinctTiersResponse{
						Status: rpc.StatusInternal(),
					}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error - CheckIfActorIsEligibleForCashbackReward fails",
			fields: fields{
				tieringClient: nil, // will be set in setup
				timeImpl:      nil, // will be set in setup
			},
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-eligibility-error",
				},
			},
			setup: func(f *fields) {
				actorId := "test-actor-eligibility-error"

				// Define the mock time for this test - year end edge case
				mockTime := time.Date(2024, 12, 31, 23, 30, 0, 0, datetime.IST) // December 31, 2024

				// Calculate expected dates based on the mock time (same logic as implementation)
				previousMonthStart, previousMonthEnd := getPrevMonthStartAndEndFromCurrentTime(mockTime)

				// Mock time.Now() call
				f.timeImpl.EXPECT().Now().Return(mockTime).Times(1)

				// Create expected requests using the calculated dates
				expectedDistinctTiersReq := &tieringPb.GetActorDistinctTiersRequest{
					ActorId:   actorId,
					TimeSince: timestamppb.New(previousMonthStart),
					TimeTill:  timestamppb.New(previousMonthEnd),
				}

				expectedCashbackReq := &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
					ActorId:     actorId,
					Tier:        tieringExtPb.Tier_TIER_FI_INFINITE,
					RewardMonth: timestamppb.New(previousMonthStart),
				}

				// Mock GetActorDistinctTiers
				f.tieringClient.EXPECT().
					GetActorDistinctTiers(context.Background(), expectedDistinctTiersReq).
					Return(&tieringPb.GetActorDistinctTiersResponse{
						Status:        rpc.StatusOk(),
						DistinctTiers: []tieringExtPb.Tier{tieringExtPb.Tier_TIER_FI_INFINITE},
					}, nil)

				// Mock CheckIfActorIsEligibleForCashbackReward with error
				f.tieringClient.EXPECT().
					CheckIfActorIsEligibleForCashbackReward(context.Background(), expectedCashbackReq).
					Return(&tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse{
						Status: rpc.StatusInternal(),
					}, nil)
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Initialize mocks
			tt.fields.tieringClient = tieringMocks.NewMockTieringClient(ctrl)
			tt.fields.timeImpl = datetimeMocks.NewMockTime(ctrl)

			// Setup mocks - each test calculates its own expected dates
			tt.setup(&tt.fields)

			// Create service with both dependencies
			c := NewTieringRewardEligibilityDataCollector(tt.fields.tieringClient, tt.fields.timeImpl)
			got, err := c.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.wantErr {
				require.Error(t, err)
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.want, got)
			}
		})
	}
}
