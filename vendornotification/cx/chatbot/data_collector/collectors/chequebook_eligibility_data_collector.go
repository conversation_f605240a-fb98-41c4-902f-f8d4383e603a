package collectors

import (
	"context"
	"strconv"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

type ChequeBookEligibilityDataCollector struct {
	alfredClient alfredPb.AlfredClient
}

func NewChequeBookEligibilityDataCollector(
	alfredClient alfredPb.AlfredClient,
) *ChequeBookEligibilityDataCollector {
	return &ChequeBookEligibilityDataCollector{
		alfredClient: alfredClient,
	}
}

func (c *ChequeBookEligibilityDataCollector) CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error) {
	actorId := inputParams.GetActorId()
	if actorId == "" {
		return nil, errors.New("actorId is required")
	}

	resp, err := c.alfredClient.IsEligibleForRequest(ctx, &alfredPb.IsEligibleForRequestRequest{
		ActorId:     actorId,
		RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
	})

	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error calling alfred service for chequebook eligibility", zap.String(logger.ACTOR_ID, actorId), zap.Error(te))
		return nil, te
	}

	eligibilityData := &ChequeBookEligibilityData{
		IsEligible: strconv.FormatBool(resp.IsEligible),
	}

	if !resp.GetIsEligible() {
		eligibilityData.ReasonForIneligibility = resp.GetFailureReason().String()
	}

	return eligibilityData, nil
}
