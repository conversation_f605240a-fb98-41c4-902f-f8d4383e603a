package collectors

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/tiering/mocks"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

func TestTierDataCollector_CollectData(t *testing.T) {
	t.Parallel()
	// Initialize logger for tests
	logger.Init(cfg.TestEnv)

	type args struct {
		ctx         context.Context
		inputParams options.DataCollectorParams
	}

	tests := []struct {
		name    string
		args    args
		mocks   func(mockTieringClient *mocks.MockTieringClient)
		want    any
		wantErr bool
	}{
		{
			name: "success - user with current tier and no downgrade",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-123",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-123",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:               rpc.StatusOk(),
					CurrentTier:          external.Tier_TIER_FI_PLUS,
					LastDowngradeDetails: nil,
				}, nil)
			},
			want: &TierBotData{
				CurrentTier:       "TIER_FI_PLUS",
				WasUserDowngraded: "false",
			},
			wantErr: false,
		},
		{
			name: "success - user downgraded from infinite to plus tier with US_STOCKS_SIP_AND_KYC reason",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-downgraded",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-downgraded",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: external.Tier_TIER_FI_PLUS,
					LastDowngradeDetails: &external.LatestMovementDetails{
						FromTier:                 external.Tier_TIER_FI_INFINITE,
						ToTier:                   external.Tier_TIER_FI_PLUS,
						MovementTimestamp:        timestamppb.Now(),
						LatestCriteriaOptionType: enums.CriteriaOptionType_US_STOCKS_SIP_AND_KYC,
					},
				}, nil)
			},
			want: &TierBotData{
				CurrentTier:        "TIER_FI_PLUS",
				WasUserDowngraded:  "true",
				ReasonForDowngrade: "US_STOCKS_SIP_AND_KYC",
			},
			wantErr: false,
		},
		{
			name: "success - user downgraded from salary tier due to salary criteria failure",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-salary-downgrade",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-salary-downgrade",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: external.Tier_TIER_FI_SALARY_BASIC,
					LastDowngradeDetails: &external.LatestMovementDetails{
						FromTier:                 external.Tier_TIER_FI_SALARY,
						ToTier:                   external.Tier_TIER_FI_SALARY_BASIC,
						MovementTimestamp:        timestamppb.Now(),
						LatestCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
					},
				}, nil)
			},
			want: &TierBotData{
				CurrentTier:        "TIER_FI_SALARY_BASIC",
				WasUserDowngraded:  "true",
				ReasonForDowngrade: "SALARY_AND_KYC",
			},
			wantErr: false,
		},
		{
			name: "success - user has downgrade details but current tier different from downgrade target",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-upgraded-after-downgrade",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-upgraded-after-downgrade",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: external.Tier_TIER_FI_INFINITE, // Current tier is different from downgrade target
					LastDowngradeDetails: &external.LatestMovementDetails{
						FromTier:                 external.Tier_TIER_FI_INFINITE,
						ToTier:                   external.Tier_TIER_FI_PLUS, // User was downgraded to PLUS but now at INFINITE
						MovementTimestamp:        timestamppb.Now(),
						LatestCriteriaOptionType: enums.CriteriaOptionType_US_STOCKS_SIP_AND_KYC,
					},
				}, nil)
			},
			want: &TierBotData{
				CurrentTier:       "TIER_FI_INFINITE",
				WasUserDowngraded: "false", // Should be false since current tier != downgrade target tier
			},
			wantErr: false,
		},
		{
			name: "failure - user with unspecified criteria option type",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-unspecified-reason",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-unspecified-reason",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: external.Tier_TIER_FI_REGULAR,
					LastDowngradeDetails: &external.LatestMovementDetails{
						FromTier:                 external.Tier_TIER_FI_PLUS,
						ToTier:                   external.Tier_TIER_FI_REGULAR,
						MovementTimestamp:        timestamppb.Now(),
						LatestCriteriaOptionType: enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED,
					},
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure - user with unspecified tier",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-unspecified-tier",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-unspecified-tier",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:               rpc.StatusOk(),
					CurrentTier:          external.Tier_TIER_UNSPECIFIED,
					LastDowngradeDetails: nil,
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure - missing actor id",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "",
				},
			},
			mocks:   func(mockTieringClient *mocks.MockTieringClient) {},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure - tiering service returns error",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-error",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-error",
					},
				).Return(nil, errors.New("tiering service unavailable"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failure - tiering disabled for user",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-disabled",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-disabled",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status: &rpc.Status{
						Code:         uint32(tieringPb.GetTieringPitchV2Response_DISABLED),
						ShortMessage: "tiering disabled",
					},
					CurrentTier: external.Tier_TIER_UNSPECIFIED,
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success - user downgraded from AA salary tier due to AA salary criteria failure",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-aa-salary",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-aa-salary",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: external.Tier_TIER_FI_SALARY_BASIC,
					LastDowngradeDetails: &external.LatestMovementDetails{
						FromTier:                 external.Tier_TIER_FI_AA_SALARY_BAND_1,
						ToTier:                   external.Tier_TIER_FI_SALARY_BASIC,
						MovementTimestamp:        timestamppb.Now(),
						LatestCriteriaOptionType: enums.CriteriaOptionType_AA_SALARY_AND_KYC,
					},
				}, nil)
			},
			want: &TierBotData{
				CurrentTier:        "TIER_FI_SALARY_BASIC",
				WasUserDowngraded:  "true",
				ReasonForDowngrade: "AA_SALARY_AND_KYC",
			},
			wantErr: false,
		},
		{
			name: "success - user with balance trial and kyc downgrade reason",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-balance-trial",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-balance-trial",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: external.Tier_TIER_FI_PLUS,
					LastDowngradeDetails: &external.LatestMovementDetails{
						FromTier:                 external.Tier_TIER_FI_INFINITE,
						ToTier:                   external.Tier_TIER_FI_PLUS,
						MovementTimestamp:        timestamppb.Now(),
						LatestCriteriaOptionType: enums.CriteriaOptionType_BALANCE_TRIAL_AND_KYC,
					},
				}, nil)
			},
			want: &TierBotData{
				CurrentTier:        "TIER_FI_PLUS",
				WasUserDowngraded:  "true",
				ReasonForDowngrade: "BALANCE_TRIAL_AND_KYC",
			},
			wantErr: false,
		},
		{
			name: "success - user downgraded from plus to basic tier due to balance criteria failure",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-balance-downgrade",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-balance-downgrade",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: external.Tier_TIER_FI_BASIC,
					LastDowngradeDetails: &external.LatestMovementDetails{
						FromTier:                 external.Tier_TIER_FI_PLUS,
						ToTier:                   external.Tier_TIER_FI_BASIC,
						MovementTimestamp:        timestamppb.Now(),
						LatestCriteriaOptionType: enums.CriteriaOptionType_BALANCE_V2_AND_KYC,
					},
				}, nil)
			},
			want: &TierBotData{
				CurrentTier:        "TIER_FI_BASIC",
				WasUserDowngraded:  "true",
				ReasonForDowngrade: "BALANCE_V2_AND_KYC",
			},
			wantErr: false,
		},
		{
			name: "success - user downgraded from infinite to plus tier due to deposits criteria failure",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-deposits-downgrade",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-deposits-downgrade",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: external.Tier_TIER_FI_PLUS,
					LastDowngradeDetails: &external.LatestMovementDetails{
						FromTier:                 external.Tier_TIER_FI_INFINITE,
						ToTier:                   external.Tier_TIER_FI_PLUS,
						MovementTimestamp:        timestamppb.Now(),
						LatestCriteriaOptionType: enums.CriteriaOptionType_DEPOSITS_AND_KYC,
					},
				}, nil)
			},
			want: &TierBotData{
				CurrentTier:        "TIER_FI_PLUS",
				WasUserDowngraded:  "true",
				ReasonForDowngrade: "DEPOSITS_AND_KYC",
			},
			wantErr: false,
		},
		{
			name: "success - user downgraded from salary lite to regular tier due to salary criteria failure",
			args: args{
				ctx: context.Background(),
				inputParams: options.DataCollectorParams{
					ActorId: "test-actor-salary-lite-downgrade",
				},
			},
			mocks: func(mockTieringClient *mocks.MockTieringClient) {
				mockTieringClient.EXPECT().GetTieringPitchV2(
					gomock.Any(),
					&tieringPb.GetTieringPitchV2Request{
						ActorId: "test-actor-salary-lite-downgrade",
					},
				).Return(&tieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: external.Tier_TIER_FI_REGULAR,
					LastDowngradeDetails: &external.LatestMovementDetails{
						FromTier:                 external.Tier_TIER_FI_SALARY_LITE,
						ToTier:                   external.Tier_TIER_FI_REGULAR,
						MovementTimestamp:        timestamppb.Now(),
						LatestCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
					},
				}, nil)
			},
			want: &TierBotData{
				CurrentTier:        "TIER_FI_REGULAR",
				WasUserDowngraded:  "true",
				ReasonForDowngrade: "SALARY_AND_KYC",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockTieringClient := mocks.NewMockTieringClient(ctrl)
			tt.mocks(mockTieringClient)

			c := NewTierDataCollector(mockTieringClient)
			got, err := c.CollectData(tt.args.ctx, tt.args.inputParams)

			if tt.wantErr {
				require.Error(t, err)
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.want, got)
			}
		})
	}
}
