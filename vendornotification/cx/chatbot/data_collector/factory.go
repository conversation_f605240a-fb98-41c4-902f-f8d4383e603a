//go:generate mockgen -source=factory.go -destination=../test/mocks/mock_data_collector_factory.go -package=mocks
package data_collector

import (
	"fmt"

	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/collectors"
)

type DataCollectorFactory interface {
	GetImpl(qcType typesv2.ChatbotRequestedDataField) (DataCollector, error)
}

type DataCollectorFactorySvc struct {
	freezeDataCollector                    *collectors.FreezeBotDataCollector
	transactionDataCollector               *collectors.TransactionDataCollector
	chequeBookEligibilityDataCollector     *collectors.ChequeBookEligibilityDataCollector
	accountClosureDataCollector            *collectors.AccountClosureDataCollector
	chequebookDataCollector                *collectors.ChequebookDataCollector
	tierDataCollector                      *collectors.TierDataCollector
	debitCardDeliveryDataCollector         *collectors.DebitCardDeliveryDataCollector
	afuUpdateEligibilityDataCollector      *collectors.AFUUpdateEligibilityCollector
	kycDataCollector                       *collectors.KycBotDataCollector
	depositDataCollector                   *collectors.DepositDataCollector
	b2bSalaryProgramDataCollector          *collectors.B2BSalaryProgramDataCollector
	powerUpRedemptionsDataCollector        *collectors.PowerUpRedemptionsDataCollector
	tieringEligibilityDataCollector        *collectors.TieringEligibilityDataCollector
	tieringRewardEligibilityDataCollector  *collectors.TieringRewardEligibilityDataCollector
	usStocksOnboardingStatusDataCollector  *collectors.UsStocksOnboardingStatusDataCollector
	usStocksWalletBalanceDataCollector     *collectors.UsStocksWalletBalanceDataCollector
	usStocksWalletTransactionDataCollector *collectors.UsStocksWalletTransactionDataCollector
	usStocksLastFiveTradesDataCollector    *collectors.UsStocksLastFiveTradesDataCollector
	usStocksPortfolioDataCollector         *collectors.UsStocksPortfolioDataCollector
	usStocksPortfolioHistoryDataCollector  *collectors.UsStocksPortfolioHistoryDataCollector
	fiStoreRedemptionsDataCollector        *collectors.FiStoreRedemptionsDataCollector
}

func NewDataCollectorFactorySvc(
	freezeDataCollector *collectors.FreezeBotDataCollector,
	transactionDataCollector *collectors.TransactionDataCollector,
	chequeBookEligibilityDataCollector *collectors.ChequeBookEligibilityDataCollector,
	accountClosureDataCollector *collectors.AccountClosureDataCollector,
	chequebookDataCollector *collectors.ChequebookDataCollector,
	tierDataCollector *collectors.TierDataCollector,
	debitCardDeliveryDataCollector *collectors.DebitCardDeliveryDataCollector,
	afuUpdateEligibilityDataCollector *collectors.AFUUpdateEligibilityCollector,
	kycDataCollector *collectors.KycBotDataCollector,
	depositDataCollector *collectors.DepositDataCollector,
	b2bSalaryProgramDataCollector *collectors.B2BSalaryProgramDataCollector,
	tieringEligibilityDataCollector *collectors.TieringEligibilityDataCollector,
	tieringRewardEligibilityDataCollector *collectors.TieringRewardEligibilityDataCollector,
	powerUpRedemptionsDataCollector *collectors.PowerUpRedemptionsDataCollector,
	usStocksOnboardingStatusDataCollector *collectors.UsStocksOnboardingStatusDataCollector,
	usStocksWalletBalanceDataCollector *collectors.UsStocksWalletBalanceDataCollector,
	usStocksWalletTransactionDataCollector *collectors.UsStocksWalletTransactionDataCollector,
	usStocksLastFiveTradesDataCollector *collectors.UsStocksLastFiveTradesDataCollector,
	usStocksPortfolioDataCollector *collectors.UsStocksPortfolioDataCollector,
	usStocksPortfolioHistoryDataCollector *collectors.UsStocksPortfolioHistoryDataCollector,
	fiStoreRedemptionsDataCollector *collectors.FiStoreRedemptionsDataCollector,
) *DataCollectorFactorySvc {
	return &DataCollectorFactorySvc{
		freezeDataCollector:                    freezeDataCollector,
		transactionDataCollector:               transactionDataCollector,
		chequeBookEligibilityDataCollector:     chequeBookEligibilityDataCollector,
		accountClosureDataCollector:            accountClosureDataCollector,
		chequebookDataCollector:                chequebookDataCollector,
		tierDataCollector:                      tierDataCollector,
		debitCardDeliveryDataCollector:         debitCardDeliveryDataCollector,
		afuUpdateEligibilityDataCollector:      afuUpdateEligibilityDataCollector,
		kycDataCollector:                       kycDataCollector,
		depositDataCollector:                   depositDataCollector,
		b2bSalaryProgramDataCollector:          b2bSalaryProgramDataCollector,
		powerUpRedemptionsDataCollector:        powerUpRedemptionsDataCollector,
		tieringEligibilityDataCollector:        tieringEligibilityDataCollector,
		tieringRewardEligibilityDataCollector:  tieringRewardEligibilityDataCollector,
		usStocksOnboardingStatusDataCollector:  usStocksOnboardingStatusDataCollector,
		usStocksWalletBalanceDataCollector:     usStocksWalletBalanceDataCollector,
		usStocksWalletTransactionDataCollector: usStocksWalletTransactionDataCollector,
		usStocksLastFiveTradesDataCollector:    usStocksLastFiveTradesDataCollector,
		usStocksPortfolioDataCollector:         usStocksPortfolioDataCollector,
		usStocksPortfolioHistoryDataCollector:  usStocksPortfolioHistoryDataCollector,
		fiStoreRedemptionsDataCollector:        fiStoreRedemptionsDataCollector,
	}
}

func (d *DataCollectorFactorySvc) GetImpl(qcType typesv2.ChatbotRequestedDataField) (DataCollector, error) {
	switch qcType {
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_DETAILS:
		return d.freezeDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_TRANSACTION_DETAILS:
		return d.transactionDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_CHEQUEBOOK_ELIGIBILITY:
		return d.chequeBookEligibilityDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_ACCOUNT_CLOSURE:
		return d.accountClosureDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_LAST_CHEQUEBOOK_REQUEST_DETAILS:
		return d.chequebookDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_CURRENT_TIER_DETAILS:
		return d.tierDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_LAST_DC_DELIVERY_DETAILS:
		return d.debitCardDeliveryDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_AFU_UPDATE_ELIGIBILITY:
		return d.afuUpdateEligibilityDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_KYC_COMPLAINT_STATUS:
		return d.kycDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_B2B_SALARY_PROGRAM:
		return d.b2bSalaryProgramDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_IS_ELIGIBLE_FOR_TIER:
		return d.tieringEligibilityDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_TIERING_REWARD_ELIGIBILITY:
		return d.tieringRewardEligibilityDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_DEPOSIT_DETAILS:
		return d.depositDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_POWER_UP_REDEMPTIONS:
		return d.powerUpRedemptionsDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_FI_STORE_REDEMPTIONS:
		return d.fiStoreRedemptionsDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_USSTOCKS_ONBOARDING_STATUS:
		return d.usStocksOnboardingStatusDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_USSTOCKS_CURRENT_AMOUNT:
		return d.usStocksWalletBalanceDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_USSTOCKS_WALLET_TXN:
		return d.usStocksWalletTransactionDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_USSTOCKS_LAST_FIVE_TRADES:
		return d.usStocksLastFiveTradesDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_USSTOCKS_PORTFOLIO:
		return d.usStocksPortfolioDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_USSTOCKS_POSITION_HISTORY:
		return d.usStocksPortfolioHistoryDataCollector, nil
	default:
		return nil, fmt.Errorf("data collector for %s is not implemented yet", qcType.String())
	}
}
