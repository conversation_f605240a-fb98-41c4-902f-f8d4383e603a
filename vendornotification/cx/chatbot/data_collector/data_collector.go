//go:generate mockgen -source=data_collector.go -destination=../test/mocks/mock_data_collector.go -package=mocks
package data_collector

import (
	"context"

	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/options"
)

// DataCollector will be used
type DataCollector interface {
	// CollectData collects data required by nugget service from different services
	CollectData(ctx context.Context, inputParams options.DataCollectorParams) (any, error)

	// TODO: Add validateRequest method to DataCollector interface for validation of incoming request. Move validations of existing data collectors into this method. Ticket - https://github.com/epiFi/tickets/issues/59016
}
