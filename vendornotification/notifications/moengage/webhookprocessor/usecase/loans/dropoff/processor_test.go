package dropoff

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	ticketMock "github.com/epifi/gamma/api/cx/ticket/mocks"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palMock "github.com/epifi/gamma/api/preapprovedloan/mocks"
	userPb "github.com/epifi/gamma/api/user"
	userMock "github.com/epifi/gamma/api/user/mocks"
	palPkg "github.com/epifi/gamma/preapprovedloan/pkg"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/webhookprocessor/usecase"
)

func TestDropOffOutcallUseCaseProcessor_Process(t *testing.T) {
	type args struct {
		request *usecase.ProcessRequest
	}
	type mockFields struct {
		palClient      *palMock.MockPreApprovedLoanClient
		cxTicketClient *ticketMock.MockTicketClient
		usersClient    *userMock.MockUsersClient
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockFields mockFields)
		wantErr    bool
	}{
		{
			name: "Success - Create Sales Outcall Ticket",
			args: args{
				request: &usecase.ProcessRequest{
					ActorId:      "test-actor-id",
					CampaignName: "SALES_S1",
					CampaignMeta: `{"action":"CREATE","loan_program":"PL","loan_vendor":"VENDOR_A"}`,
				},
			},
			setupMocks: func(mockFields mockFields) {
				mockFields.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							Email: "<EMAIL>",
						},
						AcquisitionInfo: &userPb.AcquisitionInfo{
							AcquisitionChannel: 3,
						},
					},
				}, nil)
				mockFields.cxTicketClient.EXPECT().GetSupportTickets(gomock.Any(), gomock.Any()).Return(&ticketPb.GetSupportTicketsResponse{
					Status:  rpc.StatusOk(),
					Tickets: []*ticketPb.Ticket{},
				}, nil)
				mockFields.palClient.EXPECT().GetOfferDetails(gomock.Any(), gomock.Any()).Return(&palPb.GetOfferDetailsResponse{
					Status:    rpc.StatusOk(),
					OfferInfo: &palPb.GetOfferDetailsResponse_OfferInfo{MaxLoanAmount: &moneyPb.Money{Units: 50000}},
				}, nil)
				mockFields.cxTicketClient.EXPECT().CreateTicketAsync(gomock.Any(), gomock.Any()).Return(&ticketPb.CreateTicketAsyncResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
		},
		{
			name: "Success - Close Sales Outcall Ticket",
			args: args{
				request: &usecase.ProcessRequest{
					ActorId:      "test-actor-id",
					CampaignName: "SALES_S1",
					CampaignMeta: `{"action":"CLOSE","loan_program":"PL","loan_vendor":"VENDOR_A"}`,
				},
			},
			setupMocks: func(mockFields mockFields) {
				mockFields.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							Email: "<EMAIL>",
						},
						AcquisitionInfo: &userPb.AcquisitionInfo{
							AcquisitionChannel: 1,
						},
					},
				}, nil)
				mockFields.cxTicketClient.EXPECT().GetSupportTickets(gomock.Any(), gomock.Any()).Return(&ticketPb.GetSupportTicketsResponse{
					Status: rpc.StatusOk(),
					Tickets: []*ticketPb.Ticket{{
						Id:     123,
						Status: ticketPb.Status_STATUS_OPEN,
						Tags:   []string{palPkg.LoansOfferDropOffOutcallTicketTag, "Stage:S1"},
						CustomFields: &ticketPb.CustomFields{
							LoanOutcallMetadata: &ticketPb.LoanOutcallMetadata{LoanProgram: "PL", LoanVendor: "VENDOR_A"},
						},
					}},
				}, nil)
				mockFields.cxTicketClient.EXPECT().UpdateTicketAsync(gomock.Any(), gomock.Any()).Return(&ticketPb.UpdateTicketAsyncResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
		},
		{
			name: "Success - Global Close Sales Outcall Tickets",
			args: args{
				request: &usecase.ProcessRequest{
					ActorId:      "test-actor-id",
					CampaignName: "SALES_S1",
					CampaignMeta: `{"action":"CLOSE_ALL"}`,
				},
			},
			setupMocks: func(mockFields mockFields) {
				mockFields.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							Email: "<EMAIL>",
						},
						AcquisitionInfo: &userPb.AcquisitionInfo{
							AcquisitionChannel: 3,
						},
					},
				}, nil)
				mockFields.cxTicketClient.EXPECT().GetSupportTickets(gomock.Any(), gomock.Any()).Return(&ticketPb.GetSupportTicketsResponse{
					Status: rpc.StatusOk(),
					Tickets: []*ticketPb.Ticket{{
						Id:     123,
						Status: ticketPb.Status_STATUS_OPEN,
						CustomFields: &ticketPb.CustomFields{
							LoanOutcallMetadata: &ticketPb.LoanOutcallMetadata{Disposition1: ""},
						},
					}},
				}, nil)
				mockFields.cxTicketClient.EXPECT().UpdateTicketAsync(gomock.Any(), gomock.Any()).Return(&ticketPb.UpdateTicketAsyncResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
		},
		{
			name: "Success - Create Service Outcall Ticket",
			args: args{
				request: &usecase.ProcessRequest{
					ActorId:      "test-actor-id",
					CampaignName: "SERVICE_START",
					CampaignMeta: `{"action":"CREATE"}`,
				},
			},
			setupMocks: func(mockFields mockFields) {
				mockFields.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							Email: "<EMAIL>",
						},
						AcquisitionInfo: &userPb.AcquisitionInfo{
							AcquisitionChannel: 3,
						},
					},
				}, nil)
				mockFields.palClient.EXPECT().GetActiveLoanRequests(gomock.Any(), gomock.Any()).Return(&palPb.GetActiveLoanRequestsResponse{
					Status: rpc.StatusOk(),
					ActiveLoanRequests: []*palPb.LoanRequest{{
						Id:          "lr-123",
						Vendor:      palPb.Vendor_FEDERAL,
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					}},
				}, nil)
				mockFields.cxTicketClient.EXPECT().GetSupportTickets(gomock.Any(), gomock.Any()).Return(&ticketPb.GetSupportTicketsResponse{
					Status:  rpc.StatusOk(),
					Tickets: []*ticketPb.Ticket{},
				}, nil)
				mockFields.palClient.EXPECT().GetOfferDetails(gomock.Any(), gomock.Any()).Return(&palPb.GetOfferDetailsResponse{
					Status:    rpc.StatusOk(),
					OfferInfo: &palPb.GetOfferDetailsResponse_OfferInfo{MaxLoanAmount: &moneyPb.Money{Units: 50000}},
				}, nil)
				mockFields.cxTicketClient.EXPECT().CreateTicketAsync(gomock.Any(), gomock.Any()).Return(&ticketPb.CreateTicketAsyncResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
		},
		{
			name: "Success - Close Service Outcall Ticket",
			args: args{
				request: &usecase.ProcessRequest{
					ActorId:      "test-actor-id",
					CampaignName: "SERVICE_START",
					CampaignMeta: `{"action":"CLOSE"}`,
				},
			},
			setupMocks: func(mockFields mockFields) {
				mockFields.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							Email: "<EMAIL>",
						},
						AcquisitionInfo: &userPb.AcquisitionInfo{
							AcquisitionChannel: 3,
						},
					},
				}, nil)
				mockFields.palClient.EXPECT().GetActiveLoanRequests(gomock.Any(), gomock.Any()).Return(&palPb.GetActiveLoanRequestsResponse{
					Status: rpc.StatusOk(),
					ActiveLoanRequests: []*palPb.LoanRequest{{
						Id: "lr-123",
					}},
				}, nil)
				mockFields.cxTicketClient.EXPECT().GetSupportTickets(gomock.Any(), gomock.Any()).Return(&ticketPb.GetSupportTicketsResponse{
					Status: rpc.StatusOk(),
					Tickets: []*ticketPb.Ticket{{
						Id:     456,
						Status: ticketPb.Status_STATUS_OPEN,
						Tags:   []string{palPkg.LoansApplicationDropOffOutcallTicketTag, "Stage:APPLICATION-START"},
						CustomFields: &ticketPb.CustomFields{
							LoanOutcallMetadata: &ticketPb.LoanOutcallMetadata{LoanRequestId: "lr-123"},
						},
					}},
				}, nil)
				mockFields.cxTicketClient.EXPECT().UpdateTicketAsync(gomock.Any(), gomock.Any()).Return(&ticketPb.UpdateTicketAsyncResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
		},
		{
			name: "Success - Global Close Service Outcall Tickets",
			args: args{
				request: &usecase.ProcessRequest{
					ActorId:      "test-actor-id",
					CampaignName: "SERVICE_START",
					CampaignMeta: `{"action":"CLOSE_ALL"}`,
				},
			},
			setupMocks: func(mockFields mockFields) {
				mockFields.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							Email: "<EMAIL>",
						},
						AcquisitionInfo: &userPb.AcquisitionInfo{
							AcquisitionChannel: 3,
						},
					},
				}, nil)
				mockFields.palClient.EXPECT().GetCompletedLoanRequests(gomock.Any(), gomock.Any()).Return(&palPb.GetCompletedLoanRequestsResponse{
					Status: rpc.StatusOk(),
					CompletedLoanRequests: []*palPb.LoanRequest{{
						Id: "lr-completed-123",
					}},
				}, nil)
				mockFields.cxTicketClient.EXPECT().GetSupportTickets(gomock.Any(), gomock.Any()).Return(&ticketPb.GetSupportTicketsResponse{
					Status: rpc.StatusOk(),
					Tickets: []*ticketPb.Ticket{{
						Id:     789,
						Status: ticketPb.Status_STATUS_OPEN,
						CustomFields: &ticketPb.CustomFields{
							LoanOutcallMetadata: &ticketPb.LoanOutcallMetadata{LoanRequestId: "lr-completed-123", Disposition1: ""},
						},
					}},
				}, nil)
				mockFields.cxTicketClient.EXPECT().UpdateTicketAsync(gomock.Any(), gomock.Any()).Return(&ticketPb.UpdateTicketAsyncResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
		},
		{
			name: "Error - GetUserProfile fails",
			args: args{
				request: &usecase.ProcessRequest{
					ActorId:      "test-actor-id",
					CampaignName: "SALES_S1",
					CampaignMeta: `{"action":"CREATE","loan_program":"PL","loan_vendor":"VENDOR_A"}`,
				},
			},
			setupMocks: func(mockFields mockFields) {
				mockFields.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(nil, errors.New("get user profile error"))
			},
			wantErr: true,
		},
		{
			name: "Error - ParseCampaignName fails",
			args: args{
				request: &usecase.ProcessRequest{
					ActorId:      "test-actor-id",
					CampaignName: "INVALIDCAMPAIGNNAME",
					CampaignMeta: `{"action":"CREATE","loan_program":"PL","loan_vendor":"VENDOR_A"}`,
				},
			},
			setupMocks: func(mockFields mockFields) {},
			wantErr:    true,
		},
		{
			name: "Error - ParseCampaignMeta fails",
			args: args{
				request: &usecase.ProcessRequest{
					ActorId:      "test-actor-id",
					CampaignName: "SALES_S1",
					CampaignMeta: `invalid_meta`,
				},
			},
			setupMocks: func(mockFields mockFields) {},
			wantErr:    true,
		},
		{
			name: "Error - Invalid action in campaign metadata for Sales",
			args: args{
				request: &usecase.ProcessRequest{
					ActorId:      "test-actor-id",
					CampaignName: "SALES_S1",
					CampaignMeta: `{"action":"INVALID_ACTION"}`,
				},
			},
			setupMocks: func(mockFields mockFields) {
				mockFields.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							Email: "<EMAIL>",
						},
						AcquisitionInfo: &userPb.AcquisitionInfo{
							AcquisitionChannel: 3,
						},
					},
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Error - Invalid action in campaign metadata for Service",
			args: args{
				request: &usecase.ProcessRequest{
					ActorId:      "test-actor-id",
					CampaignName: "SERVICE_START",
					CampaignMeta: `{"action":"INVALID_ACTION"}`,
				},
			},
			setupMocks: func(mockFields mockFields) {
				mockFields.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							Email: "<EMAIL>",
						},
						AcquisitionInfo: &userPb.AcquisitionInfo{
							AcquisitionChannel: 3,
						},
					},
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Error - Invalid drop off category",
			args: args{
				request: &usecase.ProcessRequest{
					ActorId:      "test-actor-id",
					CampaignName: "INVALIDCATEGORY_STAGE",
					CampaignMeta: `{"action":"CREATE"}`,
				},
			},
			setupMocks: func(mockFields mockFields) {
				mockFields.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							Email: "<EMAIL>",
						},
						AcquisitionInfo: &userPb.AcquisitionInfo{
							AcquisitionChannel: 3,
						},
					},
				}, nil)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mocks := mockFields{
				palClient:      palMock.NewMockPreApprovedLoanClient(ctrl),
				cxTicketClient: ticketMock.NewMockTicketClient(ctrl),
				usersClient:    userMock.NewMockUsersClient(ctrl),
			}
			tt.setupMocks(mocks)

			p := NewDropOffOutcallUseCaseProcessor(mocks.palClient, mocks.cxTicketClient, mocks.usersClient)
			err := p.Process(context.Background(), tt.args.request)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
