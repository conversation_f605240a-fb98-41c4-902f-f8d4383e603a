//nolint:gosec
package netcore

import (
	"context"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commonPb "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	usersPb "github.com/epifi/gamma/api/user"
	federalRedactor "github.com/epifi/gamma/api/vendors/redactor"
	palPkg "github.com/epifi/gamma/preapprovedloan/pkg"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"

	"github.com/epifi/gamma/vendornotification/config"

	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	netcoreWAPb "github.com/epifi/gamma/api/vendornotification/whatsapp/netcore"
	netcorePb "github.com/epifi/gamma/api/vendors/netcore"
)

type Service struct {
	conf         *config.Config
	ticketClient ticketPb.TicketClient
	usersClient  usersPb.UsersClient
}

func NewService(conf *config.Config, ticketClient ticketPb.TicketClient, usersClient usersPb.UsersClient) *Service {
	return &Service{
		conf:         conf,
		ticketClient: ticketClient,
		usersClient:  usersClient,
	}
}

var _ netcoreWAPb.NetCoreCallbackServer = &Service{}

const (
	NetCoreWhatsappIncomingMessageRequest = "NetCoreWhatsappIncomingMessageRequest" //nolint:gosec
	GetSupportTicketsFromTimeInHours      = 24 * 30
)

func (s *Service) NetCoreWhatsappIncomingMessage(ctx context.Context, req *netcorePb.NetCoreWhatsappIncomingMessageRequest) (*emptyPb.Empty, error) {
	if err := security.CheckWhiteList(ctx, s.conf.NetCoreWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		return nil, err
	}

	if len(req.GetIncomingMessage()) < 1 {
		logger.Error(ctx, "no incoming message received")
		return nil, status.Errorf(codes.InvalidArgument, "invalid request")
	}
	incomingMessage := req.GetIncomingMessage()[0]
	redactor.LogCallbackRequestData(ctx, req, NetCoreWhatsappIncomingMessageRequest, incomingMessage.GetMessageId(), federalRedactor.Config)

	/*
		Note: This callback api should ideally emit events that consumers should consume and perform business logic.
		But since loans out calling is the only use case for this at the moment, we're not setting up that pipeline yet.
	*/

	if len(incomingMessage.GetTo()) != 12 {
		logger.Error(ctx, "incoming message is not valid")
		return nil, status.Errorf(codes.InvalidArgument, "invalid request")
	}

	countryCode, err := strconv.ParseUint(incomingMessage.GetTo()[:2], 10, 64)
	if err != nil {
		logger.Error(ctx, "country code is not valid")
		return nil, status.Errorf(codes.InvalidArgument, "invalid request")
	}

	nationalNumber, err := strconv.ParseUint(incomingMessage.GetTo()[2:], 10, 64)
	if err != nil {
		logger.Error(ctx, "national number is not valid")
		return nil, status.Errorf(codes.InvalidArgument, "invalid request")
	}

	userRes, userErr := s.usersClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_PhoneNumber{
			PhoneNumber: &commonPb.PhoneNumber{
				CountryCode:    uint32(countryCode),
				NationalNumber: uint64(nationalNumber),
			},
		},
	})
	if te := epifigrpc.RPCError(userRes, userErr); te != nil && !rpcPb.StatusFromError(te).IsRecordNotFound() {
		logger.Error(ctx, "error while fetching user", zap.Error(te))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}

	// Get all tickets for the user
	ticketsResp, ticketsErr := s.ticketClient.GetSupportTickets(ctx, &ticketPb.GetSupportTicketsRequest{
		TicketFilters: &ticketPb.TicketFilters{
			ActorIdList: []string{userRes.GetUser().GetActorId()},
			FromTime:    timestampPb.New(time.Now().Add(-time.Hour * GetSupportTicketsFromTimeInHours)),
			StatusList:  ticketPb.GetActiveStatusList(),
		},
	})
	if te := epifigrpc.RPCError(ticketsResp, ticketsErr); te != nil && !rpcPb.StatusFromError(te).IsRecordNotFound() {
		logger.Error(ctx, "error while fetching ticket history", zap.Error(te))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}

	// find the first ticket that has "triggered" whatsapp status and consider that as the active ticket for the user
	for _, ticket := range ticketsResp.GetTickets() {
		if ticket.GetCustomFields().GetLoanOutcallMetadata().GetWhatsappStatus() == string(palPkg.TriggeredWhatsappStatus) {
			switch {
			case strings.Contains(strings.ToLower(incomingMessage.GetTextType().GetText()), "continue"):
				ticket.CustomFields.LoanOutcallMetadata.WhatsappStatus = string(palPkg.YesWhatsappStatus)
			case strings.Contains(strings.ToLower(incomingMessage.GetTextType().GetText()), "not interested"):
				ticket.CustomFields.LoanOutcallMetadata.WhatsappStatus = string(palPkg.NotInterestedWhatsappStatus)
			default:
				ticket.CustomFields.LoanOutcallMetadata.WhatsappStatus = string(palPkg.UnspecifiedWhatsappStatus)
			}

			// Update the ticket
			updateRes, updateErr := s.ticketClient.UpdateTicketAsync(ctx, &ticketPb.UpdateTicketAsyncRequest{
				Ticket: ticket,
			})
			if rpcErr := epifigrpc.RPCError(updateRes, updateErr); rpcErr != nil {
				logger.Error(ctx, "error updating ticket", zap.Int64(logger.TICKET_ID, ticket.GetId()), zap.Error(rpcErr))
				return nil, status.Errorf(codes.Internal, "internal server error")
			}
			break
		}
	}
	return &emptyPb.Empty{}, nil
}
