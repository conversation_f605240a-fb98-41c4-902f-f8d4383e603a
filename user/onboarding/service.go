package onboarding

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/types/known/timestamppb"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	txn "github.com/epifi/be-common/pkg/storage/v2"

	vgDocPb "github.com/epifi/gamma/api/vendorgateway/docs"
	"github.com/epifi/gamma/user/onboarding/stageproc/datavalidator"

	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/liveness"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/creditreportv2/notification"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	kycPb "github.com/epifi/gamma/api/kyc"
	kycDocsPb "github.com/epifi/gamma/api/kyc/docs"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	pQueuePb "github.com/epifi/gamma/api/persistentqueue"
	productPb "github.com/epifi/gamma/api/product"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/screener"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	onbDl "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	"github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/auth"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/user/config/genconf"
	events2 "github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/dao/model"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/helper/vkyc"
	"github.com/epifi/gamma/user/onboarding/metrics"
	"github.com/epifi/gamma/user/onboarding/resetter"
	"github.com/epifi/gamma/user/onboarding/stageproc"
	wireTypes "github.com/epifi/gamma/user/wire/types"
)

var (
	transactionMaxRetry                      = uint(3)
	statusUserNotOnboarded rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(uint32(onbPb.GetOnboardedUserResponse_USER_NOT_ONBOARDED), "user is not onboarded", "")
	}
	_                            onbPb.OnboardingServer = (*Service)(nil)
	resetOnboardingJourneyScreen                        = &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_ACCOUNT_DELETION_ACKNOWLEDGEMENT,
		ScreenOptions: &deeplinkPb.Deeplink_AccountDeletionAcknowledgementScreenOptions{
			AccountDeletionAcknowledgementScreenOptions: &deeplinkPb.AccountDeletionAcknowledgementScreenOptions{
				ImageUrl: ResetOnboardingImageURL,
				Title:    ResetOnboardingJourneyTitle,
				Subtitle: ResetOnboardingJourneySubtitle,
				Cta: &deeplinkPb.Cta{
					Text: ResetOnboardingJourneyCTAText,
				},
				DeletionReason: user.DeletionDetails_DELETION_REASON_OLD_USER_ONB_JOURNEY_RESET.String(),
			},
		},
	}
)

type Service struct {
	onbPb.UnimplementedOnboardingServer
	onboardingDao                           dao.OnboardingDao
	pinCodeDao                              dao.PinCodeDetailsDao
	dynConf                                 *genconf.OnboardingConfig
	userClient                              user.UsersClient
	savClient                               savings.SavingsClient
	actorClient                             actorPb.ActorClient
	consentClient                           consent.ConsentClient
	authClient                              authPb.AuthClient
	livenessClient                          liveness.LivenessClient
	EventBroker                             events.Broker
	Time                                    datetime.Time
	vkycClient                              vkycPb.VKYCClient
	syncOnbEventPublisher                   wireTypes.SyncOnboardingSqsPublisher
	stuckUserHandler                        StuckUserHandler
	orchestratorWrapper                     Orchestrator
	procOnbWrapper                          ProcessOnboarding
	commsClient                             commsPb.CommsClient
	troubleshooter                          *StageTroubleshooter
	persistentQueue                         persistentqueue.PersistentQueue
	locCheckStageProc                       *stageproc.LocationCheckStage
	referralFiniteCodeProc                  *stageproc.ReferralFiniteCodeStage
	tncConsentProc                          *stageproc.TncConsentStage
	motherFatherProc                        *stageproc.MotherFatherNameStage
	creditReportVerificationProc            *stageproc.CreditReportVerificationStage
	employmentVerificationProc              *stageproc.EmploymentVerificationStage
	workEmailOtpProc                        *stageproc.WorkEmailOtpStage
	heuristicBasedScreeningProc             *stageproc.HeuristicBasedScreening
	gmailVerificationStage                  *stageproc.GmailVerificationStage
	incomeEstimateCheckStage                *stageproc.IncomeEstimateCheckStage
	lendabilityCheckStage                   *stageproc.LendabilityCheckStage
	creditReportCheckStage                  *stageproc.CreditReportCheckStage
	itrIntimationVerificationStage          *stageproc.ITRIntimationVerificationStage
	appScreeningProc                        *stageproc.AppScreeningStage
	dobPanProc                              *stageproc.DobPanStage
	dedupeCheckProc                         *stageproc.DedupeCheckStage
	initiateCkycProc                        *stageproc.InitCkycStage
	ensureKycAvailabilityProc               *stageproc.EnsureKycAvailabilityStage
	updateCustomerDetailsProc               *stageproc.UpdateCustomerDetailsStage
	nrUpdateCustomerDetailsProc             *stageproc.NRUpdateCustomerDetailsStage
	panNameCheckProc                        *stageproc.PanNameCheckStage
	kycDedupeCheckProc                      *stageproc.KycDedupeCheckStage
	confirmCardMailingAddressProc           *stageproc.ConfirmCardMailingAddressStage
	debitCardPinSetProc                     *stageproc.DebitCardPinSetStage
	addMoneyProc                            *stageproc.AddMoneyStage
	preAccountCreationAddMoneyProc          *stageproc.PreAccountCreationAddMoneyStage
	permissionProc                          *stageproc.PermissionStage
	onboardingCompleteProc                  *stageproc.OnboardingCompleteStage
	panUniquenessCheckProc                  *stageproc.PanUniquenessCheckStage
	deviceRegistrationProc                  *stageproc.DeviceRegistration
	vkycOnboardingStageProc                 *stageproc.VKYCOnboardingStage
	unNameCheckStageProc                    *stageproc.UNNameCheckStage
	nrUNNameCheckStageProc                  *stageproc.NRUNNameCheckStage
	nrVKYCStageProc                         *stageproc.NRVKYCStage
	nrQatarStageProc                        *stageproc.NRQatarVKYCStage
	nroAccountCreationStage                 *stageproc.NROAccountCreationStage
	riskScreeningStageProc                  *stageproc.RiskScreeningStage
	uanPresenceCheckStageProc               *stageproc.UANPresenceCheckStage
	ckycProc                                *stageproc.CKYCStage
	ekycProc                                *stageproc.EKYCStage
	livenessProc                            *stageproc.LivenessStage
	customerCreationProc                    *stageproc.CustomerCreationStage
	accountCreationProc                     *stageproc.AccountCreationStage
	cardCreationProc                        *stageproc.CardCreationStage
	epfoCompanySearch                       *stageproc.EPFOCompanySearchStage
	caScreener                              *stageproc.CAScreenerStage
	preCustomerCreationCheckProc            *stageproc.PreCustomerCreationCheckStage
	bkycProc                                *stageproc.BKYC
	fiLiteRiskScreeningProc                 *stageproc.FiLiteRiskScreeningStage
	kycNameDobValidationProc                *stageproc.KYCNameDOBValidation
	softIntentSelectionProc                 *stageproc.SoftIntentSelectionStage
	collectAdditionalProfileDetailsProc     *stageproc.CollectAdditionalProfileDetailsForFederalLoansStage
	intentSelectionProc                     *stageproc.IntentSelectionStage
	crediCardOnbStatusProc                  *stageproc.CreditCardOnboardingStatusCheck
	plOnbStatusCheckProc                    *stageproc.PLOnboardingStatusCheck
	saIntroConsentStageProc                 *stageproc.SaIntroConsentStage
	aadharMobileValidationStageProc         *stageproc.AadharMobileValidationStage
	updateProfileDetails                    *stageproc.UpdateProfileDetails
	passportVerificationProc                *stageproc.PassportVerificationStage
	visaVerificationProc                    *stageproc.VisaVerificationStage
	countryIDVerificationProc               *stageproc.CountryIDVerificationStage
	qatarIdVerificationProc                 *stageproc.QatarIdVerificationStage
	nrOnboardingCrossDataValidationProc     *stageproc.NROnboardingCrossDataValidationStage
	nrKycDedupeCheckProc                    *stageproc.NRKycDedupeCheckStage
	nrLivenessProc                          *stageproc.NRLivenessStage
	nrCommunicationAddrProc                 *stageproc.NRCommunicationAddress
	nrCardCreationProc                      *stageproc.NRCardCreation
	nrPreCustomerCreationProc               *stageproc.NRPreCustomerCreationCheckStage
	nrLocationCheckProc                     *stageproc.NRLocationCheckStage
	nrEnsureKycAvailabilityStageProc        *stageproc.NREnsureKycAvailabilityStage
	orderPhysicalCardStageProc              *stageproc.OrderPhysicalCardStage
	smsParserConsentStageProc               *stageproc.SMSParserConsentStage
	userProcessor                           helper.UserProcessor
	nextActionDecisionCache                 dao.NextActionDecisionCache
	lockManager                             lock.ILockManager
	kychelper                               helper.IKYCHelper
	bankCustomerClient                      bankCustomerPb.BankCustomerServiceClient
	resetUserFactory                        *resetter.ResetUserFactory
	journeyResetter                         ResetOnboardingJourney
	eventLogger                             events2.EventLogger
	watsonClient                            watsonPb.WatsonClient
	dlHelper                                helper.DeeplinkHelper
	productClient                           productPb.ProductClient
	creditReportClient                      creditreportv2.CreditReportManagerClient
	screenerClient                          screener.ScreenerClient
	s3Client                                s3.S3Client
	kycDocsClient                           kycDocsPb.DocExtractionClient
	passportHelper                          helper.PassportHelper
	onbHelper                               helper.OnboardingHelper
	nrS3Client                              s3.S3Client
	omegleApplicationIDGen                  helper.OmegleApplicationIDGenerator
	openMinBalanceAccountStageProc          *stageproc.OpenMinBalanceAccountStage
	initiateCrReportFetchStageProc          *stageproc.InitiateCreditReportFetchStage
	wealthAnalyserStatusStageProc           *stageproc.WealthAnalyserOnboardingStatusCheck
	smsParserDataVerificationStageProc      *stageproc.SMSParserDataVerificationStage
	installedAppsCheckStageProc             *stageproc.InstalledAppsCheckStage
	vkycHelper                              helper.VKYCHelper
	vgDocClient                             vgDocPb.DocsClient
	dataValidator                           datavalidator.DataValidator
	waitForAutoPanProc                      *stageproc.WaitForAutoPan
	wbConnectedAccountsStageProc            *stageproc.WBConnectedAccounts
	wbOnboardingCompleteStageProc           *stageproc.WBOnboardingCompleteStage
	saDeclarationStageProc                  *stageproc.SADeclarationStage
	ensureCreditReportAvailabilityStageProc *stageproc.EnsureCreditReportAvailabilityStage
}

// nolint:funlen
func NewService(onboardingDao dao.OnboardingDao, pinCodeDao dao.PinCodeDetailsDao, dyncConf *genconf.OnboardingConfig,
	userClient user.UsersClient, savClient savings.SavingsClient, actorClient actorPb.ActorClient,
	consentClient consent.ConsentClient, authClient authPb.AuthClient, broker events.Broker, time datetime.Time,
	vkycClient vkycPb.VKYCClient, syncOnbEventPublisher wireTypes.SyncOnboardingSqsPublisher, stuckUserHandler StuckUserHandler,
	commsClient commsPb.CommsClient, troubleshooter *StageTroubleshooter, persistentQueue persistentqueue.PersistentQueue,
	motherFatherProc *stageproc.MotherFatherNameStage, locCheckStageProc *stageproc.LocationCheckStage, referralFiniteCodeProc *stageproc.ReferralFiniteCodeStage, tncConsentProc *stageproc.TncConsentStage,
	creditReportVerificationProc *stageproc.CreditReportVerificationStage, incomeEstimateCheckStage *stageproc.IncomeEstimateCheckStage, lendabilityCheckStage *stageproc.LendabilityCheckStage,
	creditScoreCheckStage *stageproc.CreditReportCheckStage, employmentVerificationProc *stageproc.EmploymentVerificationStage, itrIntimationVerificationStage *stageproc.ITRIntimationVerificationStage, appScreeningProc *stageproc.AppScreeningStage,
	dobPanProc *stageproc.DobPanStage, dedupeCheckProc *stageproc.DedupeCheckStage, initiateCkycProc *stageproc.InitCkycStage,
	ensureKycAvailabilityProc *stageproc.EnsureKycAvailabilityStage, updateCustomerDetailsProc *stageproc.UpdateCustomerDetailsStage, panNameCheckProc *stageproc.PanNameCheckStage,
	kycDedupeCheckProc *stageproc.KycDedupeCheckStage, plOnbStatusCheckProc *stageproc.PLOnboardingStatusCheck,
	confirmCardMailingAddressProc *stageproc.ConfirmCardMailingAddressStage, workEmailOtpProc *stageproc.WorkEmailOtpStage,
	gmailVerificationStage *stageproc.GmailVerificationStage,
	debitCardPinSetProc *stageproc.DebitCardPinSetStage, addMoneyProc *stageproc.AddMoneyStage,
	onboardingCompleteProc *stageproc.OnboardingCompleteStage, panUniquenessCheckProc *stageproc.PanUniquenessCheckStage, heuristicBasedScreeningProc *stageproc.HeuristicBasedScreening,
	deviceRegistrationProc *stageproc.DeviceRegistration, vkycOnboardingStageProc *stageproc.VKYCOnboardingStage, livenessClient liveness.LivenessClient,
	riskScreeningStageProc *stageproc.RiskScreeningStage, uanPresenceCheckStageProc *stageproc.UANPresenceCheckStage, permissionProc *stageproc.PermissionStage,
	ckycProc *stageproc.CKYCStage, ekycProc *stageproc.EKYCStage, livenessProc *stageproc.LivenessStage,
	customerCreationProc *stageproc.CustomerCreationStage, accountCreationProc *stageproc.AccountCreationStage,
	cardCreationProc *stageproc.CardCreationStage, unNameCheckStageProc *stageproc.UNNameCheckStage,
	userProcessor helper.UserProcessor, nextActionDecisionCache dao.NextActionDecisionCache,
	epfoCompanySearch *stageproc.EPFOCompanySearchStage, caScreener *stageproc.CAScreenerStage, preCustomerCreationCheckProc *stageproc.PreCustomerCreationCheckStage,
	bkycProc *stageproc.BKYC, kycNamDobValidation *stageproc.KYCNameDOBValidation, eventLogger events2.EventLogger,
	lockMgr lock.ILockManager, kychelper helper.IKYCHelper, bankCustomerClient bankCustomerPb.BankCustomerServiceClient,
	fiLiteRiskScreeningProc *stageproc.FiLiteRiskScreeningStage, resetUserFactory *resetter.ResetUserFactory, intentSelectionProc *stageproc.IntentSelectionStage,
	crediCardOnbStatusProc *stageproc.CreditCardOnboardingStatusCheck, saIntroConsentStageProc *stageproc.SaIntroConsentStage,
	watsonClient watsonPb.WatsonClient, dlHelper helper.DeeplinkHelper, productClient productPb.ProductClient,
	aadharMobileValidationStageProc *stageproc.AadharMobileValidationStage, updateProfileDetails *stageproc.UpdateProfileDetails,
	screenerClient screener.ScreenerClient, creditReportClient creditreportv2.CreditReportManagerClient,
	passportVerificationProc *stageproc.PassportVerificationStage, visaVerificationProc *stageproc.VisaVerificationStage, countryIDVerificationProc *stageproc.CountryIDVerificationStage, nrOnboardingCrossDataValidationProc *stageproc.NROnboardingCrossDataValidationStage,
	nrLivenessProc *stageproc.NRLivenessStage, nrKycDedupeCheckProc *stageproc.NRKycDedupeCheckStage, nrUnNameCheckProc *stageproc.NRUNNameCheckStage, softIntentSelectionProc *stageproc.SoftIntentSelectionStage,
	nrUpdateCustomerDetailsProc *stageproc.NRUpdateCustomerDetailsStage, nrCommunicationAddrProc *stageproc.NRCommunicationAddress, nrVKYCProc *stageproc.NRVKYCStage,
	s3Client s3.S3Client, nrCardCreationProc *stageproc.NRCardCreation, kycDocsClient kycDocsPb.DocExtractionClient, nrPreCustomerCreationProc *stageproc.NRPreCustomerCreationCheckStage,
	nrLocationCheckProc *stageproc.NRLocationCheckStage, passportHelper helper.PassportHelper, onbHelper helper.OnboardingHelper,
	nrEnsureKycAvailabilityStageProc *stageproc.NREnsureKycAvailabilityStage, nrS3Client wireTypes.NrS3Client,
	nroAccountCreationStage *stageproc.NROAccountCreationStage, orderPhysicalCardStageProc *stageproc.OrderPhysicalCardStage, omegleApplicationIDGen helper.OmegleApplicationIDGenerator,
	minBalanceAccountStageProc *stageproc.OpenMinBalanceAccountStage, smsParserConsentStageProc *stageproc.SMSParserConsentStage,
	initiateCrReportFetchStageProc *stageproc.InitiateCreditReportFetchStage, wealthAnalyserStatusStageProc *stageproc.WealthAnalyserOnboardingStatusCheck, qatarIdVerificationProc *stageproc.QatarIdVerificationStage,
	nrQatarStageProc *stageproc.NRQatarVKYCStage, smsParserDataVerificationStage *stageproc.SMSParserDataVerificationStage, installedAppsCheckStageProc *stageproc.InstalledAppsCheckStage,
	vkycHelper helper.VKYCHelper, preAccountCreationAddMoneyProc *stageproc.PreAccountCreationAddMoneyStage, vgDocClient vgDocPb.DocsClient, collectAdditionalProfileDetailsProc *stageproc.CollectAdditionalProfileDetailsForFederalLoansStage,
	dataValidator datavalidator.DataValidator, waitForAutoPanProc *stageproc.WaitForAutoPan, wbConnectedAccountsStageProc *stageproc.WBConnectedAccounts, wbOnboardingCompleteStageProc *stageproc.WBOnboardingCompleteStage, saDeclarationStageProc *stageproc.SADeclarationStage, ensureCreditReportAvailabilityStageProc *stageproc.EnsureCreditReportAvailabilityStage) *Service {
	s := &Service{
		onboardingDao:                           onboardingDao,
		pinCodeDao:                              pinCodeDao,
		userClient:                              userClient,
		savClient:                               savClient,
		actorClient:                             actorClient,
		consentClient:                           consentClient,
		authClient:                              authClient,
		livenessClient:                          livenessClient,
		dynConf:                                 dyncConf,
		EventBroker:                             broker,
		Time:                                    time,
		vkycClient:                              vkycClient,
		syncOnbEventPublisher:                   syncOnbEventPublisher,
		stuckUserHandler:                        stuckUserHandler,
		commsClient:                             commsClient,
		screenerClient:                          screenerClient,
		creditReportClient:                      creditReportClient,
		troubleshooter:                          troubleshooter,
		persistentQueue:                         persistentQueue,
		motherFatherProc:                        motherFatherProc,
		tncConsentProc:                          tncConsentProc,
		creditReportVerificationProc:            creditReportVerificationProc,
		employmentVerificationProc:              employmentVerificationProc,
		appScreeningProc:                        appScreeningProc,
		locCheckStageProc:                       locCheckStageProc,
		referralFiniteCodeProc:                  referralFiniteCodeProc,
		dobPanProc:                              dobPanProc,
		dedupeCheckProc:                         dedupeCheckProc,
		permissionProc:                          permissionProc,
		initiateCkycProc:                        initiateCkycProc,
		ensureKycAvailabilityProc:               ensureKycAvailabilityProc,
		updateCustomerDetailsProc:               updateCustomerDetailsProc,
		nrUpdateCustomerDetailsProc:             nrUpdateCustomerDetailsProc,
		panNameCheckProc:                        panNameCheckProc,
		kycDedupeCheckProc:                      kycDedupeCheckProc,
		confirmCardMailingAddressProc:           confirmCardMailingAddressProc,
		debitCardPinSetProc:                     debitCardPinSetProc,
		addMoneyProc:                            addMoneyProc,
		preAccountCreationAddMoneyProc:          preAccountCreationAddMoneyProc,
		workEmailOtpProc:                        workEmailOtpProc,
		gmailVerificationStage:                  gmailVerificationStage,
		onboardingCompleteProc:                  onboardingCompleteProc,
		panUniquenessCheckProc:                  panUniquenessCheckProc,
		heuristicBasedScreeningProc:             heuristicBasedScreeningProc,
		deviceRegistrationProc:                  deviceRegistrationProc,
		vkycOnboardingStageProc:                 vkycOnboardingStageProc,
		riskScreeningStageProc:                  riskScreeningStageProc,
		uanPresenceCheckStageProc:               uanPresenceCheckStageProc,
		ckycProc:                                ckycProc,
		ekycProc:                                ekycProc,
		livenessProc:                            livenessProc,
		customerCreationProc:                    customerCreationProc,
		accountCreationProc:                     accountCreationProc,
		cardCreationProc:                        cardCreationProc,
		userProcessor:                           userProcessor,
		nextActionDecisionCache:                 nextActionDecisionCache,
		epfoCompanySearch:                       epfoCompanySearch,
		caScreener:                              caScreener,
		lockManager:                             lockMgr,
		preCustomerCreationCheckProc:            preCustomerCreationCheckProc,
		bkycProc:                                bkycProc,
		kycNameDobValidationProc:                kycNamDobValidation,
		kychelper:                               kychelper,
		bankCustomerClient:                      bankCustomerClient,
		fiLiteRiskScreeningProc:                 fiLiteRiskScreeningProc,
		unNameCheckStageProc:                    unNameCheckStageProc,
		resetUserFactory:                        resetUserFactory,
		eventLogger:                             eventLogger,
		incomeEstimateCheckStage:                incomeEstimateCheckStage,
		softIntentSelectionProc:                 softIntentSelectionProc,
		intentSelectionProc:                     intentSelectionProc,
		crediCardOnbStatusProc:                  crediCardOnbStatusProc,
		watsonClient:                            watsonClient,
		plOnbStatusCheckProc:                    plOnbStatusCheckProc,
		saIntroConsentStageProc:                 saIntroConsentStageProc,
		itrIntimationVerificationStage:          itrIntimationVerificationStage,
		creditReportCheckStage:                  creditScoreCheckStage,
		dlHelper:                                dlHelper,
		productClient:                           productClient,
		aadharMobileValidationStageProc:         aadharMobileValidationStageProc,
		updateProfileDetails:                    updateProfileDetails,
		lendabilityCheckStage:                   lendabilityCheckStage,
		passportVerificationProc:                passportVerificationProc,
		visaVerificationProc:                    visaVerificationProc,
		countryIDVerificationProc:               countryIDVerificationProc,
		qatarIdVerificationProc:                 qatarIdVerificationProc,
		nrOnboardingCrossDataValidationProc:     nrOnboardingCrossDataValidationProc,
		nrLivenessProc:                          nrLivenessProc,
		nrKycDedupeCheckProc:                    nrKycDedupeCheckProc,
		nrUNNameCheckStageProc:                  nrUnNameCheckProc,
		nrCommunicationAddrProc:                 nrCommunicationAddrProc,
		nrVKYCStageProc:                         nrVKYCProc,
		s3Client:                                s3Client,
		nrCardCreationProc:                      nrCardCreationProc,
		kycDocsClient:                           kycDocsClient,
		nrPreCustomerCreationProc:               nrPreCustomerCreationProc,
		nrLocationCheckProc:                     nrLocationCheckProc,
		passportHelper:                          passportHelper,
		onbHelper:                               onbHelper,
		nrEnsureKycAvailabilityStageProc:        nrEnsureKycAvailabilityStageProc,
		orderPhysicalCardStageProc:              orderPhysicalCardStageProc,
		smsParserConsentStageProc:               smsParserConsentStageProc,
		nrS3Client:                              nrS3Client,
		nroAccountCreationStage:                 nroAccountCreationStage,
		omegleApplicationIDGen:                  omegleApplicationIDGen,
		openMinBalanceAccountStageProc:          minBalanceAccountStageProc,
		initiateCrReportFetchStageProc:          initiateCrReportFetchStageProc,
		wealthAnalyserStatusStageProc:           wealthAnalyserStatusStageProc,
		nrQatarStageProc:                        nrQatarStageProc,
		smsParserDataVerificationStageProc:      smsParserDataVerificationStage,
		installedAppsCheckStageProc:             installedAppsCheckStageProc,
		collectAdditionalProfileDetailsProc:     collectAdditionalProfileDetailsProc,
		vkycHelper:                              vkycHelper,
		vgDocClient:                             vgDocClient,
		dataValidator:                           dataValidator,
		waitForAutoPanProc:                      waitForAutoPanProc,
		wbConnectedAccountsStageProc:            wbConnectedAccountsStageProc,
		wbOnboardingCompleteStageProc:           wbOnboardingCompleteStageProc,
		saDeclarationStageProc:                  saDeclarationStageProc,
		ensureCreditReportAvailabilityStageProc: ensureCreditReportAvailabilityStageProc,
	}
	s.orchestratorWrapper = s.processOnboarding
	s.procOnbWrapper = s.orchestrator
	s.journeyResetter = s
	return s
}

var _ onbPb.OnboardingServer = (*Service)(nil)
var _ onbPb.ConsumerServer = (*Service)(nil)

func (s *Service) GetDetails(ctx context.Context, req *onbPb.GetDetailsRequest) (*onbPb.GetDetailsResponse, error) {
	onb, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, req.GetActorId(), &model.GetOnbDetailsByActorParams{
		UseCache: true,
	})
	if txn.IsRecordNotFoundError(err) {
		return &onbPb.GetDetailsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	if err != nil {
		logger.Error(ctx, "GetDetails error in get onboarding", zap.Error(err))
		return &onbPb.GetDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	if req.GetCachedData() {
		return &onbPb.GetDetailsResponse{
			Status:  rpc.StatusOk(),
			Details: onb,
		}, nil
	}

	// if onboarding not complete; sync latest states
	onbId := onb.OnboardingId
	if !isOnboardingComplete(onb) {
		if _, err = s.processOnboarding(ctx, &model.OrchestratorRequest{Onb: onb}); err != nil {
			logger.Error(ctx, "GetDetails error in processOnboarding", zap.Error(err), logOnb(onbId))
			return nil, err
		}

		// get updated onboarding details
		onb, err = s.onboardingDao.GetOnboardingDetailsByActor(ctx, req.GetActorId())
		if err != nil {
			logger.Error(ctx, "GetDetails error in get onboarding", zap.Error(err), logOnb(onbId))
			return nil, err
		}
	}

	// return success
	return &onbPb.GetDetailsResponse{
		Status:  rpc.StatusOk(),
		Details: onb,
	}, nil
}

func (s *Service) StartOnboarding(ctx context.Context, stOnb *onbPb.StartOnboardingRequest) (*onbPb.StartOnboardingResponse, error) {
	// TODO(nitesh.v) : Refactor this function to pass vendor as a parameter
	onb, err := s.createOnboarding(ctx, stOnb.GetActorId())
	if err != nil {
		return nil, err
	}

	return &onbPb.StartOnboardingResponse{
		Status:       rpc.StatusOk(),
		OnboardingId: onb.GetOnboardingId(),
	}, nil

}

func (s *Service) GetNextAction(ctx context.Context, req *onbPb.GetNextActionRequest) (*onbPb.GetNextActionResponse, error) {
	if req.GetActorId() == "" {
		return &onbPb.GetNextActionResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("ActorID is empty"),
		}, nil
	}

	if nextAction, err := s.getNextActionFromCache(ctx, req.GetActorId()); err == nil {
		return &onbPb.GetNextActionResponse{
			Status:     rpc.StatusOk(),
			NextAction: nextAction,
		}, nil
	}

	if req.GetForceCache() == commontypes.BooleanEnum_TRUE {
		logger.Debug(ctx, "returning GNOA screen")
		return &onbPb.GetNextActionResponse{
			Status: rpc.StatusOk(),
			NextAction: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
			},
		}, nil
	}

	onb, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, req.GetActorId())
	if txn.IsRecordNotFoundError(err) {
		onb, err = s.createOnboarding(ctx, req.GetActorId())
		if err != nil {
			return nil, err
		}
	}
	if err != nil {
		logger.Error(ctx, "error in get onboarding details by id", zap.Error(err))
		return nil, err
	}

	onbId := onb.GetOnboardingId()

	feat := req.GetOnboardingFeature()
	if onb.GetFiLiteDetails().GetIsEnabled() != commontypes.BooleanEnum_TRUE {
		feat = onb.GetFeature()
	}

	// If the feature is unspecified update the feature
	// This is required because GetNextAction can be triggered with an UNSPECIFIED feature - which can lead to user landing on SA flows (default)
	if req.GetOnboardingFeature() == onbPb.Feature_FEATURE_UNSPECIFIED {
		feat = onb.GetFeature()
	}

	if errFeat := updateOnbFeature(ctx, s.onboardingDao, onb, feat); errFeat != nil {
		logger.Error(ctx, "failed to update feature in onboarding details", zap.Error(errFeat), zap.String("Feature", req.GetOnboardingFeature().String()))
		return nil, errFeat
	}

	onb, err = updateFeatureOnbEntryPoint(ctx, s.onboardingDao, onb, feat, req.GetFeatureOnboardingEntryPoint())
	if err != nil {
		logger.Error(ctx, "failed to update feature onb entry point in onb details", zap.Error(err), zap.String("Feature", req.GetOnboardingFeature().String()), zap.String("EntryPoint", req.GetFeatureOnboardingEntryPoint().String()))
	}

	res, err := s.orchestratorWrapper(ctx, &model.OrchestratorRequest{
		Onb:     onb,
		Feature: feat,
	})
	if err != nil {
		logger.Error(ctx, "error in process state machine", zap.Error(err), logOnb(onbId))
		return nil, err
	}

	// call get post onboarding next action and return if available
	if postOnbNextAction := s.getPostOnboardingNextAction(ctx, req.GetActorId(), onb); postOnbNextAction != nil {
		return &onbPb.GetNextActionResponse{
			Status:     rpc.StatusOk(),
			NextAction: postOnbNextAction,
		}, nil
	}

	if allowReset, _ := s.journeyResetter.IsEligibleForOnbJourneyReset(ctx, onb); allowReset {
		if s.dynConf.Flags().EnableHomeRedirectForSAOnbExpiredUsers() {
			fiLiteAction, resetErr := s.resetExpiredSAOnboardingJourney(ctx, onb)
			if resetErr != nil {
				logger.Error(ctx, "error in reset expired SA onboarding journey", zap.Error(err), logOnb(onbId))
				return nil, resetErr
			}
			logger.Info(ctx, "allowing SA expired user to home screen")
			return &onbPb.GetNextActionResponse{
				Status:     rpc.StatusOk(),
				NextAction: fiLiteAction,
			}, nil
		}
		logger.Info(ctx, "allowing old user to reset onboarding journey")
		return &onbPb.GetNextActionResponse{
			Status:     rpc.StatusOk(),
			NextAction: resetOnboardingJourneyScreen,
		}, nil
	}

	return &onbPb.GetNextActionResponse{
		Status:     rpc.StatusOk(),
		NextAction: res.NextAction,
	}, nil
}

func (s *Service) resetExpiredSAOnboardingJourney(ctx context.Context, onb *onbPb.OnboardingDetails) (*deeplinkPb.Deeplink, error) {
	if err := s.journeyResetter.ResetJourney(ctx, onb); err != nil {
		logger.Error(ctx, "error in resetting onboarding journey for SA expired user", zap.Error(err), logOnb(onb.GetOnboardingId()))
		return nil, err
	}

	resp, err := s.UpdateFiLiteAccessibility(ctx, &onbPb.UpdateFiLiteAccessibilityRequest{
		ActorId:   onb.GetActorId(),
		IsEnabled: commontypes.BooleanEnum_TRUE,
		Source:    onbPb.FiLiteSource_FI_LITE_SOURCE_SA_ONBOARDING_EXPIRED,
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		logger.Error(ctx, "error in updating fi lite accessibility", zap.Error(grpcErr))
		return nil, grpcErr
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
		ScreenOptions: &deeplinkPb.Deeplink_GetNextOnboardingActionScreenOptions{
			GetNextOnboardingActionScreenOptions: &deeplinkPb.GetNextOnboardingActionScreenOptions{
				Title:    "Taking you to home!",
				ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite_dino.png",
				Feature:  onbPb.Feature_FEATURE_FI_LITE.String(),
			},
		},
	}, nil
}

func (s *Service) getPostOnboardingNextAction(ctx context.Context, actorId string, onb *onbPb.OnboardingDetails) *deeplinkPb.Deeplink {
	if s.shouldShowPostOnboardingSoftIntentScreen(ctx, actorId, onb, onb.GetCompletedAt().AsTime()) {
		return getHomeSoftIntentSelectionScreen()
	}

	if s.dynConf.Flags().EnableSecureUsageGuidelinesConsent() && onb.GetCompletedAt() != nil {
		resp, er := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
			ConsentType: consent.ConsentType_SECURE_USAGE_GUIDELINES,
			ActorId:     onb.GetActorId(),
			Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
		})
		if rpcErr := epifigrpc.RPCError(resp, er); rpcErr != nil {
			if resp.GetStatus().IsRecordNotFound() {
				return getSecureGuidelinesScreen(s.dynConf.SecureUsageGuidelineVersion())
			}
			logger.Error(ctx, "error in fetching consent", zap.Error(rpcErr))
		} else if !s.hasUserAckMinVersionGuidelines(resp.GetVersion()) || time.Since(resp.GetCreatedAt().AsTime()) > s.dynConf.SecureUsageGuidelineConsentInterval() {
			// Consent version stored in SecureUsageTnC in user config
			logger.Info(ctx, "require ack for secure guidelines", zap.Int("userConsentVersion", int(resp.GetVersion())), zap.Int("desiredConsentVersion", int(s.dynConf.SecureUsageGuidelineVersion())))
			return getSecureGuidelinesScreen(s.dynConf.SecureUsageGuidelineVersion())
		}

		// consent found, update in cache
		_ = s.updateDecisionInfoCache(ctx, true, onb.GetCompletedAt().AsTime(), false, actorId)
	}
	return nil
}

func (s *Service) hasUserAckMinVersionGuidelines(consentVersion int32) bool {
	return consentVersion >= s.dynConf.SecureUsageGuidelineVersion()
}

func updateOnbFeature(ctx context.Context, dao dao.OnboardingDao, onbDetails *onbPb.OnboardingDetails, feature onbPb.Feature) error {
	// If the feature is unspecified or same as the current feature, do not update
	if feature == onbPb.Feature_FEATURE_UNSPECIFIED ||
		onbDetails.Feature == feature {
		return nil
	}
	if // If the feature is set as Non-Resident Savings Account onboarding, throw error.
	onbDetails.GetFeature().IsNonResidentUserOnboarding() &&
		!feature.IsNonResidentUserOnboarding() {
		return fmt.Errorf("feature update attempted for non resident savings account onboarding")
	}
	onbDetails.Feature = feature
	return dao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_FEATURE}, onbDetails)
}

func updateFeatureOnbEntryPoint(ctx context.Context, dao dao.OnboardingDao, onbDetails *onbPb.OnboardingDetails, feature onbPb.Feature, entryPoint onboarding.FeatureOnboardingEntryPoint) (*onbPb.OnboardingDetails, error) {
	// If the entry point is unspecified or same as the current entry point, do not update
	if entryPoint == onboarding.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_UNSPECIFIED ||
		onbDetails.GetFeatureDetails().GetFeatureInfo()[feature.String()].GetFeatureOnboardingEntryPoint() == entryPoint {
		return onbDetails, nil
	}

	switch {
	case onbDetails.GetFeatureDetails() == nil:
		onbDetails.FeatureDetails = &onbPb.FeatureDetails{
			FeatureInfo: map[string]*onbPb.FeatureInfo{
				feature.String(): {},
			},
		}
	case onbDetails.GetFeatureDetails().GetFeatureInfo() == nil:
		onbDetails.FeatureDetails.FeatureInfo = map[string]*onbPb.FeatureInfo{
			feature.String(): {},
		}
	case onbDetails.GetFeatureDetails().GetFeatureInfo()[feature.String()] == nil:
		onbDetails.GetFeatureDetails().GetFeatureInfo()[feature.String()] = &onbPb.FeatureInfo{}
	}

	onbDetails.GetFeatureDetails().GetFeatureInfo()[feature.String()].FeatureOnboardingEntryPoint = entryPoint
	errUpdate := dao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_FEATURE_DETAILS}, onbDetails)
	return onbDetails, errUpdate
}

func (s *Service) CheckAccountSetupStatus(ctx context.Context, req *onbPb.CheckAccountSetupStatusRequest) (*onbPb.CheckAccountSetupStatusResponse, error) {
	// sync latest onboarding states & get next action
	naRes, err := s.GetNextAction(ctx, &onbPb.GetNextActionRequest{
		ActorId: req.GetActorId(),
	})
	if grpcErr := epifigrpc.RPCError(naRes, err); grpcErr != nil {
		logger.Error(ctx, "error in get next action", zap.Error(grpcErr))
		return nil, grpcErr
	}

	// Fetching onboarding details from db
	onb, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "failed to fetch onboarding details by id", zap.Error(err))
		return nil, err
	}

	nextAction := naRes.GetNextAction()
	isUserStuck := stageproc.IsUserStuck(ctx, onb, s.dynConf.AccountSetupMaxStuckDuration())
	if isUserStuck {
		nextAction = stageproc.ActionForUserStuckOnAcctCreation(ctx, onb)
	}

	res := s.evaluateAccountSetupProgress(ctx, onb)
	res.NextAction = nextAction
	res.IsUserStuck = isUserStuck
	res.AccountInfo = marshalAccountInfo(onb.GetAccountInfo())
	res.CardInfo = marshalCardInfo(onb.GetCardInfo().GetCardDetails())
	return res, nil
}

func (s *Service) ResetUser(ctx context.Context, req *onbPb.ResetUserRequest) (*onbPb.ResetUserResponse, error) {
	res, err := s.resetUser(ctx, req)
	metrics.RecordResetUserMetrics(req.GetDeletionDetails().GetDeletionReason(), res.GetStatus(), "")
	return res, err
}

// nolint: unparam
func (s *Service) resetUser(ctx context.Context, req *onbPb.ResetUserRequest) (*onbPb.ResetUserResponse, error) {
	logger.Info(ctx, "ResetUser request received", zap.String(logger.REASON, req.GetDeletionDetails().GetDeletionReason().String()))
	handler, err := s.getResetUserHandler(ctx, req.GetDeletionDetails().GetDeletionReason())
	if err != nil {
		return &onbPb.ResetUserResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	if err = s.syncOnboarding(ctx, req.GetActorId()); err != nil {
		if errors.Is(epifierrors.ErrRecordNotFound, err) {
			// returning success as onboarding details record is already deleted
			return &onbPb.ResetUserResponse{
				Status: rpc.StatusOk(),
			}, nil
		}
		return &onbPb.ResetUserResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	if err = handler.IsResetAllowed(ctx, &resetter.IsResetAllowedRequest{
		ActorId:         req.GetActorId(),
		DeletionDetails: req.GetDeletionDetails(),
	}); err != nil {
		st := handleResetHandlerResp(ctx, err)
		return &onbPb.ResetUserResponse{
			Status: st,
		}, nil
	}

	logger.Info(ctx, "reset user is allowed")
	if err = handler.DeleteUserDetails(ctx, &resetter.DeleteUserDetailsRequest{
		ActorId:         req.GetActorId(),
		DeletionDetails: req.GetDeletionDetails(),
	}); err != nil {
		st := handleResetHandlerResp(ctx, err)
		return &onbPb.ResetUserResponse{
			Status: st,
		}, nil
	}

	logger.Info(ctx, "reset user successful", zap.String(logger.REASON, req.GetDeletionDetails().GetDeletionReason().String()))
	return &onbPb.ResetUserResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func handleResetHandlerResp(ctx context.Context, err error) *rpc.Status {
	switch {
	case errors.Is(epifierrors.ErrPermissionDenied, err):
		return rpc.NewStatus(uint32(onbPb.ResetUserResponse_PERMISSION_DENIED), "Not Allowed", "Current stage of user does not allow resetting")
	case errors.Is(epifierrors.ErrRecordNotFound, err):
		return rpc.StatusRecordNotFound()
	case !rpc.StatusFromError(err).IsUnknown():
		return rpc.StatusFromError(err)
	default:
		logger.Error(ctx, "error in reset handler", zap.Error(err))
		return rpc.StatusInternalWithDebugMsg(err.Error())
	}
}

// Deprecated: DO NOT USE
func (s *Service) ResetStage(_ context.Context, _ *onbPb.ResetStageRequest) (*onbPb.ResetStageResponse, error) {
	return &onbPb.ResetStageResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) RegisterUserForVKYC(ctx context.Context, req *onbPb.RegisterUserForVKYCRequest) (
	*onbPb.RegisterUserForVKYCResponse, error) {
	err := s.vkycHelper.RegisterUserVKYC(ctx, req.GetActorId())
	if errors.Is(err, vkyc.InvalidUserError) {
		return &onbPb.RegisterUserForVKYCResponse{Status: rpc.StatusFailedPrecondition()}, nil
	}
	if errors.Is(err, vkyc.EkycNameMisMatchError) {
		return &onbPb.RegisterUserForVKYCResponse{Status: rpc.NewStatusWithoutDebug(uint32(kycPb.GetKYCRecordResponse_NAME_MISMATCH), "name mismatch")}, nil
	}
	if err != nil {
		// returning err.Error here since underlying service ensures error is trimmed
		logger.Error(ctx, "Error in register user for vkyc", zap.Error(err))
		return &onbPb.RegisterUserForVKYCResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	return &onbPb.RegisterUserForVKYCResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) UpdatePanNameVerdict(ctx context.Context, req *onbPb.UpdatePanNameVerdictRequest) (*onbPb.UpdatePanNameVerdictResponse, error) {
	var err error

	if err = s.updatePANReviewAnnotationInTx(ctx, req); err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "user has been deleted, ignoring the verdict")
			return &onbPb.UpdatePanNameVerdictResponse{
				Status: rpc.StatusOk(),
			}, nil
		}
		logger.Error(ctx, "Error updating onboarding details", zap.Error(err))
		return &onbPb.UpdatePanNameVerdictResponse{
			Status: rpc.StatusFromError(err),
		}, nil
	}
	switch req.GetAnnotation().GetVerdict() {
	case onbPb.Verdict_VERDICT_PASS:
		_, err = s.UpdateStage(ctx, &onbPb.UpdateStageRequest{
			ActorId:  req.GetActorId(),
			Stage:    onbPb.OnboardingStage_PAN_NAME_CHECK,
			NewState: onbPb.OnboardingState_SUCCESS,
		})

	case onbPb.Verdict_VERDICT_FAIL:
		_, err = s.UpdateStage(ctx, &onbPb.UpdateStageRequest{
			ActorId:  req.GetActorId(),
			Stage:    onbPb.OnboardingStage_PAN_NAME_CHECK,
			NewState: onbPb.OnboardingState_FAILURE,
		})

	default:
		logger.Info(ctx, fmt.Sprintf("received invalid verdict value: %v", req.GetAnnotation().GetVerdict()))
		return nil, fmt.Errorf("verdict not specified")
	}

	if err != nil {
		logger.Error(ctx, "Error in updating stage for the user", zap.Error(err))
		return &onbPb.UpdatePanNameVerdictResponse{
			Status: rpc.StatusFromError(err),
		}, nil
	}

	return &onbPb.UpdatePanNameVerdictResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) updatePANReviewAnnotationInTx(ctx context.Context, req *onbPb.UpdatePanNameVerdictRequest) error {
	if err := txn.RunCRDBIdempotentTxn(ctx, transactionMaxRetry, func(ctx context.Context) error {
		return s.updatePANReviewAnnotation(ctx, req)
	}); err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error in running txn to update pan review annotation", zap.Error(err))
		}
		return err
	}
	return nil
}

func (s *Service) updatePANReviewAnnotation(ctx context.Context, req *onbPb.UpdatePanNameVerdictRequest) error {
	// get latest onb details
	onb, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, req.GetActorId())
	if err != nil {
		// There is a possibility that the user has been deleted by the time verdict comes. We can ignore such updates.
		if txn.IsRecordNotFoundError(err) {
			return epifierrors.ErrRecordNotFound
		}
		return err
	}

	// set annotation in metadata
	if onb.GetStageMetadata() == nil {
		onb.StageMetadata = &onbPb.StageMetadata{
			PanManualReviewAnnotation: req.GetAnnotation(),
		}
	} else {
		onb.StageMetadata.PanManualReviewAnnotation = req.GetAnnotation()
	}

	// update in DB
	return s.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
	}, onb)
}

// GetOnboardedUser returns user details along with onboarding completion timestamp, for a given user
// identifier
func (s *Service) GetOnboardedUser(ctx context.Context, req *onbPb.GetOnboardedUserRequest) (*onbPb.GetOnboardedUserResponse, error) {
	var (
		res         = &onbPb.GetOnboardedUserResponse{}
		fetchedUser *user.User
		getUserReq  *user.GetUserRequest
		err         error
	)

	switch req.GetIdentifier().(type) {
	case *onbPb.GetOnboardedUserRequest_Email:
		getUserReq = &user.GetUserRequest{Identifier: &user.GetUserRequest_EmailId{EmailId: req.GetEmail()}}
	case *onbPb.GetOnboardedUserRequest_PhoneNumber:
		getUserReq = &user.GetUserRequest{Identifier: &user.GetUserRequest_PhoneNumber{PhoneNumber: req.GetPhoneNumber()}}
	default:
		logger.Error(ctx, fmt.Sprintf("invalid user identifier: %T", req.GetIdentifier()))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	// step 1: fetch user for given identifier
	userRes, err := s.userClient.GetUser(ctx, getUserReq)
	switch {
	case err != nil:
		logger.Error(ctx, "failed to get user details")
		res.Status = rpc.StatusInternal()
		return res, nil
	case userRes.GetStatus().IsRecordNotFound():
		logger.Info(ctx, "user not found")
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case !userRes.GetStatus().IsSuccess():
		logger.Error(ctx, "failed to get user details")
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	fetchedUser = userRes.GetUser()

	// step 2: fetch on-boarding details by actorId
	onb, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, fetchedUser.GetActorId())
	switch {
	case txn.IsRecordNotFoundError(err):
		logger.Error(ctx, "on-boarding details not found", zap.String(logger.USER_ID, fetchedUser.GetId()))
		res.Status = statusUserNotOnboarded()
		return res, nil
	case err != nil:
		logger.Error(ctx, "failed to on-boarding details", zap.String(logger.USER_ID, fetchedUser.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if onb.GetCompletedAt() == nil {
		res.Status = statusUserNotOnboarded()
		return res, nil
	}

	res.User = fetchedUser
	res.OnboardedAt = onb.GetCompletedAt()
	res.ActorId = onb.GetActorId()
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) UpdateStage(ctx context.Context, req *onbPb.UpdateStageRequest) (*onbPb.UpdateStageResponse, error) {
	// get onboarding details
	onb, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, req.GetActorId())
	if txn.IsRecordNotFoundError(err) {
		return &onbPb.UpdateStageResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	if err != nil {
		logger.Error(ctx, "error in get onboarding by actor", zap.Error(err))
		return &onbPb.UpdateStageResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	var status *rpc.Status
	onb, err = s.onbHelper.UpdateStageStatus(ctx, req.GetActorId(), req.GetStage(), req.GetNewState(), onb)
	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		status = rpc.StatusInvalidArgument()
	case err != nil:
		status = rpc.StatusInternalWithDebugMsg(err.Error())
	default:
		status = rpc.StatusOk()
	}

	return &onbPb.UpdateStageResponse{
		Status:            status,
		OnboardingDetails: onb,
	}, nil
}

func (s *Service) GetUnonboardedUsers(ctx context.Context, req *onbPb.GetUnonboardedUsersRequest) (*onbPb.GetUnonboardedUsersResponse, error) {
	response := &onbPb.GetUnonboardedUsersResponse{}
	offset := 0
	for {
		unOnbUsersPage, err := s.onboardingDao.GetUnOnboardedUsers(ctx, offset)
		if err != nil {
			logger.Error(ctx, "Error in getting unonboarded users from DAO", zap.Error(err))
			response.Status = rpc.StatusFromError(err)
			return response, nil
		}
		if len(unOnbUsersPage) == 0 {
			break
		}
		offset += len(unOnbUsersPage)
		for _, curUser := range unOnbUsersPage {
			stuckDuration, lastSuccessStage := s.stuckUserHandler.GetStuckDuration(curUser, curUser.GetCurrentOnboardingStage())
			if req.FromTime != nil && req.ToTime != nil {
				// Adding 24Hr as ToTime is upto 00:00 of the given date
				// E.g. if FromTime is 14th June, ToTime is 15th June, the request is from 14th June 00:00 IST to 15th June 00:00 IST which is essentially only 1 day
				req.ToTime = timestamppb.New(req.ToTime.AsTime().Add(time.Hour * 24))
				stageUpdateTS := curUser.GetStageDetails().GetStageMapping()[lastSuccessStage.String()].GetLastUpdatedAt()
				if stageUpdateTS.AsTime().Before(req.FromTime.AsTime()) || stageUpdateTS.AsTime().After(req.ToTime.AsTime()) {
					continue
				}
			}
			if req.GetStageList() != nil {
				shouldCurrentStageBeFiltered := false
				for _, stage := range req.GetStageList() {
					if stage == curUser.GetCurrentOnboardingStage() {
						shouldCurrentStageBeFiltered = true
						break
					}
				}
				if !shouldCurrentStageBeFiltered {
					continue
				}
			}

			stuckDurationString := fmt.Sprintf("%v", stuckDuration.Round(time.Minute))
			response.Unonboardedusers = append(response.Unonboardedusers, &onbPb.GetUnonboardedUsersResponse_UnonboardedUser{
				Stage:         curUser.GetCurrentOnboardingStage(),
				UserId:        curUser.GetUserId(),
				ActorId:       curUser.GetActorId(),
				StuckDuration: stuckDurationString,
			})
		}
	}
	response.Status = rpc.StatusOk()

	return response, nil
}

func (s *Service) GetTroubleshootingDetails(ctx context.Context, req *onbPb.GetTroubleshootingDetailsRequest) (*onbPb.GetTroubleshootingDetailsResponse, error) {
	var tsList []*onbPb.StageTroubleshootingDetails
	for _, actorId := range req.GetActorIds() {
		var cancelCtx func()
		ctx, cancelCtx = context.WithDeadline(epificontext.CloneCtx(ctx), time.Now().Add(60*time.Second))
		defer cancelCtx()
		ts, _ := s.getTroubleshootingDetails(ctx, actorId)
		if ts != nil {
			tsList = append(tsList, ts)
		}
	}
	return &onbPb.GetTroubleshootingDetailsResponse{
		Status:  rpc.StatusOk(),
		Details: tsList,
	}, nil
}

func (s *Service) getTroubleshootingDetails(ctx context.Context, actorId string) (*onbPb.StageTroubleshootingDetails, error) {
	var nextAction *deeplinkPb.Deeplink
	onb, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in get onboarding details by id", zap.Error(err))
		return nil, err
	}

	onbId := onb.GetOnboardingId()
	orchRes, err := s.processOnboarding(ctx, &model.OrchestratorRequest{Onb: onb})
	if err != nil {
		logger.Error(ctx, "error in process state machine", zap.Error(err), logOnb(onbId))
		nextAction = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_NO_ACTION,
		}
	}
	stage := onb.GetCurrentOnboardingStage()
	if orchRes != nil {
		stage = orchRes.Stage
	}
	tsRes, err := s.troubleshooter.Troubleshoot(ctx, &StageTroubleshootingRequest{
		Stage:     stage,
		Onb:       onb,
		NexAction: nextAction,
	})
	if err != nil {
		logger.Error(ctx, "error in getting troubleshooting details", zap.Error(err), logOnb(onbId))
		return nil, err
	}
	return tsRes.Details, nil
}

func (s *Service) GetQueueElements(ctx context.Context, req *onbPb.GetQueueElementsRequest) (*onbPb.GetQueueElementsResponse, error) {
	elements, err := s.persistentQueue.GetElements(ctx, req.GetPayloadType(), int(req.GetLimit()), int(req.GetPageNum()), req.GetFromTime(), req.GetToTime(), req.GetIsFifo(), nil)
	if rpc.StatusFromError(err).IsRecordNotFound() {
		return &onbPb.GetQueueElementsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	if err != nil {
		return nil, err
	}

	var payloads []*onbPb.QueueElement
	for _, element := range elements {
		var currentPayload *onbPb.QueueElement
		switch req.GetPayloadType() {
		case pQueuePb.PayloadType_PAYLOAD_TYPE_LIVENESS, pQueuePb.PayloadType_PAYLOAD_TYPE_L2_LIVENESS, pQueuePb.PayloadType_PAYLOAD_TYPE_RETROSPECTIVE_LIVENESS:
			currentPayload = &onbPb.QueueElement{
				Id: element.GetID(),
				Payload: &onbPb.QueueElement_LivenessReview{
					LivenessReview: &pQueuePb.LivenessReview{
						ActorId:       element.GetActorID(),
						RequestId:     element.GetPayload().GetLivenessPayload().GetRequestId(),
						VideoLocation: element.GetPayload().GetLivenessPayload().GetVideoLocation(),
						CreatedAt:     element.GetCreatedAt(),
					},
				},
			}

		case pQueuePb.PayloadType_PAYLOAD_TYPE_FACEMATCH, pQueuePb.PayloadType_PAYLOAD_TYPE_L2_FACEMATCH:
			currentPayload = &onbPb.QueueElement{
				Id: element.GetID(),
				Payload: &onbPb.QueueElement_FacematchReview{
					FacematchReview: &pQueuePb.FacematchReview{
						ActorId:            element.GetActorID(),
						RequestId:          element.GetPayload().GetFacematchPayload().GetRequestId(),
						VideoFrame:         element.GetPayload().GetFacematchPayload().GetVideoFrame(),
						KycImage:           element.GetPayload().GetFacematchPayload().GetKycImage(),
						CreatedAt:          element.GetCreatedAt(),
						Base64KycImageData: element.GetPayload().GetFacematchPayload().GetBase64KycImageData(),
					},
				},
			}
		case pQueuePb.PayloadType_PAYLOAD_TYPE_LIVENESS_SAMPLE:
			currentPayload = &onbPb.QueueElement{
				Id: element.GetID(),
				Payload: &onbPb.QueueElement_LivenessSampleReview{
					LivenessSampleReview: &pQueuePb.LivenessSampleReview{
						ActorId:       element.GetActorID(),
						RequestId:     element.GetPayload().GetLivenessSamplePayload().GetRequestId(),
						VideoLocation: element.GetPayload().GetLivenessSamplePayload().GetVideoLocation(),
						CreatedAt:     element.GetCreatedAt(),
						AttemptDate:   element.GetPayload().GetLivenessSamplePayload().GetAttemptDate(),
						SubSample:     element.GetPayload().GetLivenessSamplePayload().GetSubSample(),
					},
				},
			}

		case pQueuePb.PayloadType_PAYLOAD_TYPE_AFU_LIVENESS:
			currentPayload = &onbPb.QueueElement{
				Id: element.GetID(),
				Payload: &onbPb.QueueElement_AfuLivenessReview{
					AfuLivenessReview: &pQueuePb.AFULivenessReview{
						ActorId:       element.GetActorID(),
						RequestId:     element.GetPayload().GetAfuLivenessReview().GetRequestId(),
						VideoLocation: element.GetPayload().GetAfuLivenessReview().GetVideoLocation(),
						CreatedAt:     element.GetCreatedAt(),
					},
				},
			}

		case pQueuePb.PayloadType_PAYLOAD_TYPE_AFU_FACEMATCH:
			currentPayload = &onbPb.QueueElement{
				Id: element.GetID(),
				Payload: &onbPb.QueueElement_AfuFacematchReview{
					AfuFacematchReview: &pQueuePb.AFUFacematchReview{
						ActorId:            element.GetActorID(),
						RequestId:          element.GetPayload().GetAfuFacematchReview().GetRequestId(),
						VideoFrame:         element.GetPayload().GetAfuFacematchReview().GetVideoFrame(),
						KycImage:           element.GetPayload().GetAfuFacematchReview().GetKycImage(),
						CreatedAt:          element.GetCreatedAt(),
						Base64KycImageData: element.GetPayload().GetAfuFacematchReview().GetBase64KycImageData(),
					},
				},
			}

		case pQueuePb.PayloadType_PAYLOAD_TYPE_PAN_NAME_MATCH:
			currentPayload = &onbPb.QueueElement{
				Id: element.GetID(),
				Payload: &onbPb.QueueElement_NamematchReview{
					NamematchReview: &pQueuePb.NamematchReview{
						ActorId:     element.GetActorID(),
						ProfileName: element.GetPayload().GetNamematchReview().GetProfileName(),
						PANName:     element.GetPayload().GetNamematchReview().GetPANName(),
						KYCName:     element.GetPayload().GetNamematchReview().GetKYCName(),
						CreatedAt:   element.GetCreatedAt(),
					},
				},
			}

		case pQueuePb.PayloadType_PAYLOAD_TYPE_FACEMATCH_SAMPLE:
			currentPayload = &onbPb.QueueElement{
				Id: element.GetID(),
				Payload: &onbPb.QueueElement_FacematchSampleReview{
					FacematchSampleReview: &pQueuePb.FacematchSampleReview{
						ActorId:     element.GetActorID(),
						RequestId:   element.GetPayload().GetFacematchSamplePayload().GetRequestId(),
						VideoFrame:  element.GetPayload().GetFacematchSamplePayload().GetVideoFrame(),
						KycImage:    element.GetPayload().GetFacematchSamplePayload().GetKycImage(),
						CreatedAt:   element.GetCreatedAt(),
						AttemptDate: element.GetPayload().GetFacematchSamplePayload().GetAttemptDate(),
					},
				},
			}

		case pQueuePb.PayloadType_PAYLOAD_TYPE_PAN_NAME_SAMPLE:
			currentPayload = &onbPb.QueueElement{
				Id: element.GetID(),
				Payload: &onbPb.QueueElement_PanNameSampleReview{
					PanNameSampleReview: &pQueuePb.PanNameSampleReview{
						ActorId:   element.GetActorID(),
						PanName:   element.GetPayload().GetPanNameSampleReview().GetPanName(),
						KycName:   element.GetPayload().GetPanNameSampleReview().GetKycName(),
						CreatedAt: element.GetCreatedAt(),
					},
				},
			}

		case pQueuePb.PayloadType_PAYLOAD_TYPE_LIVENESS_AND_FACEMATCH:
			currentPayload = &onbPb.QueueElement{
				Id: element.GetID(),
				Payload: &onbPb.QueueElement_LivenessAndFacematchReview{
					LivenessAndFacematchReview: &pQueuePb.LivenessAndFacematchReview{
						ActorId:            element.GetActorID(),
						LivenessRequestId:  element.GetPayload().GetLivenessAndFacematchReviewPayload().GetLivenessRequestId(),
						FacematchRequestId: element.GetPayload().GetLivenessAndFacematchReviewPayload().GetFacematchRequestId(),
						VideoLocation:      element.GetPayload().GetLivenessAndFacematchReviewPayload().GetVideoLocation(),
						VideoFrame:         element.GetPayload().GetLivenessAndFacematchReviewPayload().GetVideoFrame(),
						KycImage:           element.GetPayload().GetLivenessAndFacematchReviewPayload().GetKycImage(),
						ReviewType:         element.GetPayload().GetLivenessAndFacematchReviewPayload().GetReviewType(),
						CreatedAt:          element.GetCreatedAt(),
					},
				},
			}

		case pQueuePb.PayloadType_PAYLOAD_TYPE_AFU_LIVENESS_AND_FACEMATCH:
			currentPayload = &onbPb.QueueElement{
				Id: element.GetID(),
				Payload: &onbPb.QueueElement_AfuLivenessAndFacematchReview{
					AfuLivenessAndFacematchReview: &pQueuePb.AFULivenessAndFacematchReview{
						ActorId:            element.GetActorID(),
						LivenessRequestId:  element.GetPayload().GetAfuLivenessFacematchReviewPayload().GetLivenessRequestId(),
						FacematchRequestId: element.GetPayload().GetAfuLivenessFacematchReviewPayload().GetFacematchRequestId(),
						VideoLocation:      element.GetPayload().GetAfuLivenessFacematchReviewPayload().GetVideoLocation(),
						VideoFrame:         element.GetPayload().GetAfuLivenessFacematchReviewPayload().GetVideoFrame(),
						KycImage:           element.GetPayload().GetAfuLivenessFacematchReviewPayload().GetKycImage(),
						ReviewType:         element.GetPayload().GetAfuLivenessFacematchReviewPayload().GetReviewType(),
						CreatedAt:          element.GetCreatedAt(),
						Base64KycImageData: element.GetPayload().GetAfuLivenessFacematchReviewPayload().GetBase64KycImageData(),
					},
				},
			}

		case pQueuePb.PayloadType_PAYLOAD_TYPE_USER_REVIEW:
			currentPayload = &onbPb.QueueElement{
				Id: element.GetID(),
				Payload: &onbPb.QueueElement_UserReview{
					UserReview: &pQueuePb.UserReview{
						ActorId:       element.GetActorID(),
						ReviewReasons: element.GetPayload().GetUserReviewPayload().GetReviewReasons(),
					},
				},
			}

		case pQueuePb.PayloadType_PAYLOAD_TYPE_STOCKGUARDIAN_CKYC_DOCUMENTS:
			currentPayload = &onbPb.QueueElement{
				Id: element.GetID(),
				Payload: &onbPb.QueueElement_StockGuardianCkycDocuments{
					StockGuardianCkycDocuments: element.GetPayload().GetStockGuardianCkycDocuments(),
				},
			}
		default:
			logger.Error(ctx, "Unknown payload type received", zap.String("PayloadType", req.GetPayloadType().String()))
			continue
		}

		payloads = append(payloads, currentPayload)
	}
	return &onbPb.GetQueueElementsResponse{
		Status:   rpc.StatusOk(),
		Elements: payloads,
	}, nil
}

func (s *Service) DeleteQueueElement(ctx context.Context, req *onbPb.DeleteQueueElementRequest) (*onbPb.DeleteQueueElementResponse, error) {

	err := s.persistentQueue.DeleteElement(ctx, &persistentqueue.QueueElement{
		ID:          req.GetId(),
		ActorID:     req.GetActorId(),
		PayloadType: req.GetPayloadType(),
	})
	if err != nil {
		return nil, err
	}

	return &onbPb.DeleteQueueElementResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) CountQueueElements(ctx context.Context, req *onbPb.CountQueueElementsRequest) (*onbPb.CountQueueElementsResponse, error) {
	if req.GetPayloadType() == pQueuePb.PayloadType_PAYLOAD_TYPE_UNSPECIFIED {
		logger.Error(ctx, "Invalid payload type received")
		return &onbPb.CountQueueElementsResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("Invalid payload type received"),
		}, nil
	}

	count, err := s.persistentQueue.CountElements(ctx, req.GetPayloadType())
	if err != nil {
		logger.Error(ctx, "Failed to get count of queue elements", zap.Error(err))
		return nil, err
	}

	return &onbPb.CountQueueElementsResponse{
		Status: rpc.StatusOk(),
		Count:  count,
	}, nil
}

func (s *Service) markStartedAtForStage(ctx context.Context, onb *onbPb.OnboardingDetails, stage onbPb.OnboardingStage) error {
	onbId := onb.GetOnboardingId()
	stageMap := onb.GetStageDetails().GetStageMapping()
	if stageMap == nil {
		return nil
	}
	stageStartedAt := stageMap[stage.String()].GetStartedAt()
	if stageStartedAt == nil || !stageStartedAt.IsValid() {
		stageState := stageStatus(onb, stage)
		// logging events for initiated state to help identify stage drop offs in moengage comms setup
		s.eventLogger.LogOnboardingStageStateUpdate(ctx, onb.GetActorId(), stage, onbPb.OnboardingState_INITIATED, onb.GetFeature())
		if err := s.onboardingDao.MarkStageStartedAt(ctx, onbId, stage, stageState); err != nil {
			logger.Error(ctx, "error marking started at", zap.Error(err))
			return err
		}
	}

	return nil
}

func (s *Service) UpdateUserDOB(ctx context.Context, onb *onbPb.OnboardingDetails, dob *date.Date) error {
	userId := onb.GetUserId()

	userResp, err := s.userProcessor.GetUserByUserId(ctx, userId)
	if err != nil {
		logger.Error(ctx, "error in getting user", zap.Error(err))
		return err
	}

	updateUser := userResp
	updateUser.GetProfile().DateOfBirth = dob
	updateResp, err := s.userClient.UpdateUser(ctx, &user.UpdateUserRequest{
		User:       updateUser,
		UpdateMask: []user.UserFieldMask{user.UserFieldMask_DOB},
	})
	if er := epifigrpc.RPCError(updateResp, err); er != nil {
		logger.Error(ctx, "error in updating user", zap.Error(er))
		return er
	}

	return nil
}

func getHomeSoftIntentSelectionScreen() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_ONBOARDING_SOFT_INTENT_SELECTION,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&onbDl.OnboardingSoftIntentSelectionScreenOptions{
			EntryPoint: onbPb.SoftIntentSelectionEntryPoint_SOFT_INTENT_SELECTION_ENTRY_POINT_HOME_BANNER.String(),
		}),
	}
}

func getSecureGuidelinesScreen(consentVersion int32) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_GENERIC_RECORD_CONSENT,
		ScreenOptions: &deeplinkPb.Deeplink_GenericRecordConsentOptions{
			GenericRecordConsentOptions: &deeplinkPb.GenericRecordConsentOptions{
				ConsentVersion: consentVersion,
				Title:          "Some quick tips to keep your money secure",
				Subtitle:       "",
				TitleImageUrl:  "https://epifi-icons.pointz.in/secureusageicons/titleicon/Lock-Check%20%284%29%201.png",
				ContentList: []*deeplinkPb.GenericRecordConsentOptions_ContentObject{
					{
						ContentText: "Do not share OTPs, PINs or Card details with anyone. We never ask for it. Also, don't write them anywhere",
						IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/icons/Group%20174963370.png",
					},
					{
						ContentText: "UPI PIN, QR code, ATM PIN & Collect Requests are only for making payments. Never use them to receive money",
						IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/icons/qr-code.png",
					},
					{
						ContentText: "Always check the beneficiary name & UPI ID on collect requests. And ignore requests from unknown merchants",
						IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/icons/UPI.png",
					},
					{
						ContentText: "Avoid opening any link in SMSs/Emails from unknown sources. Block & report such messages immediately",
						IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/icons/link.png",
					},
					{
						ContentText: "Check your bank statements! In case you find incorrect transactions, contact Customer Care",
						IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/icons/statement.png",
					},
					{
						ContentText: "Never send money to unknown organisations/individuals who keep promising unrealistic & huge returns",
						IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/icons/user-cross.png",
					},
					{
						ContentText: "Before selling/exchanging your phone, delete all financial apps. Also, clear device cache, browsing history, etc.",
						IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/icons/trash.png",
					},
					{
						ContentText: "If a website feels unauthentic, don't download anything from it ",
						IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/icons/download.png",
					},
				},
				Consents:    []string{consent.ConsentType_SECURE_USAGE_GUIDELINES.String()},
				ConsentData: getMultiLangConsent(),
			},
		},
	}
}

// nolint: funlen
func getMultiLangConsent() map[string]*deeplinkPb.GenericRecordConsentOptions_ConsentData {
	return map[string]*deeplinkPb.GenericRecordConsentOptions_ConsentData{
		types.Language_LANGUAGE_ENGLISH.String(): {
			ContentList: []*deeplinkPb.GenericRecordConsentOptions_ContentObject{
				{
					ContentText: "Do not share OTPs, PINs or Card details with anyone. <font color='#D9F2CC'>We never ask for it</font>. Also, don't write them anywhere",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/lock_v2.png",
				},
				{
					ContentText: "UPI PIN, QR code, ATM PIN & Collect Requests are only for <font color='#D9F2CC'>making payments</font>. Never use them to receive money",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/qr_code_v2.png",
				},
				{
					ContentText: "Always check the <font color='#D9F2CC'>beneficiary name & UPI ID</font> on collect requests. And ignore requests from unknown merchants",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/UPI_v2.png",
				},
				{
					ContentText: "Avoid opening <font color='#D9F2CC'>any link in SMSs/Emails</font> from unknown sources. Block & report such messages immediately",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/link_v2.png",
				},
				{
					ContentText: "Check your <font color='#D9F2CC'>bank statements!</font> In case you find incorrect transactions, contact Customer Care",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/statement_v2.png",
				},
				{
					ContentText: "Never send money to unknown organisations/individuals who keep <font color='#D9F2CC'>promising unrealistic & huge returns</font>",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/user_cross_v2.png",
				},
				{
					ContentText: "Before selling/exchanging your phone, <font color='#D9F2CC'>delete all financial apps</font>. Also, clear device cache, browsing history, etc.",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/trash_v2.png",
				},
				{
					ContentText: "If a <font color='#D9F2CC'>website feels unauthentic</font>, don't download anything from it",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/download_v2.png",
				},
				{
					ContentText: "For protection from potential threats, always update your phone & apps",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/virus_v2.png",
				},
				{
					ContentText: "As a precaution, install anti-malware/anti-virus software on your device",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/shield_tick_v2.png",
				},
				{
					ContentText: "To reduce the risk of installing malicious software, only download applications from trusted sources",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/secure_flag_v2.png",
				},
			},
			Title:              "Some quick tips to keep your money secure",
			LangSelectedText:   "English",
			LangUnselectedText: "Read in English",
			Cta: &deeplinkPb.Cta{
				Text: "I understand",
			},
		},
		types.Language_LANGUAGE_HINDI.String(): {
			ContentList: []*deeplinkPb.GenericRecordConsentOptions_ContentObject{
				{
					ContentText: "ओटीपी, पिन या कार्ड की जानकारी किसी से शेयर न करें। हम यह जानकारी कभी नहीं मांगेंगे। साथ ही उन्हें कहीं भी न लिखें",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/lock_v2.png",
				},
				{
					ContentText: "UPI पिन, QR कोड और एटीएम पिन केवल भुगतान करने के लिए हैं। धन प्राप्त करने के लिए उनका उपयोग कभी न करें",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/qr_code_v2.png",
				},
				{
					ContentText: "संग्रह अनुरोधों पर हमेशा प्राप्तकर्ता का नाम और UPI आईडी की जांच करें। अज्ञात व्यापारियों के अनुरोधों पर ध्यान न दें",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/UPI_v2.png",
				},
				{
					ContentText: "अज्ञात स्रोतों से आए एसएमएस/ईमेल में किसी भी लिंक को न खोलें। ऐसे संदेशों को तुरंत ब्लॉक करें और रिपोर्ट करें",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/link_v2.png",
				},
				{
					ContentText: "अपने बैंक स्टेटमेंट जाँच करें! यदि आपको गलत जानकारी मिलती हैं, तो कस्टमर केयर से संपर्क करें",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/statement_v2.png",
				},
				{
					ContentText: "कभी भी ऐसे अनजान संगठनों/व्यक्तियों को पैसे न भेजें जो अवास्तविक और भारी रिटर्न का वादा करते रहते हैं",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/user_cross_v2.png",
				},
				{
					ContentText: "अपना फ़ोन बेचने/बदलने से पहले, सभी बैंकिंग ऐप्स हटा दें। साथ ही, डिवाइस कैशे, ब्राउज़िंग इतिहास इत्यादि साफ़ करें।",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/trash_v2.png",
				},
				{
					ContentText: "यदि कोई वेबसाइट अप्रामाणिक लगती है, तो उससे कुछ भी डाउनलोड न करें",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/download_v2.png",
				},
				{
					ContentText: "संभावित ख़तरों से सुरक्षा के लिए, हमेशा अपने फ़ोन और ऐप्स को अपडेट करें",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/virus_v2.png",
				},
				{
					ContentText: "अधिक सुरक्षा के लिए, अपने डिवाइस पर एंटी-मैलवेयर/एंटी-वायरस सॉफ़्टवेयर इंस्टॉल करें",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/shield_tick_v2.png",
				},
				{
					ContentText: "दुर्भावनापूर्ण सॉफ़्टवेयर इंस्टॉल करने के जोखिम को कम करने के लिए, केवल विश्वसनीय स्रोतों से ही एप्लिकेशन डाउनलोड करें",
					IconUrl:     "https://epifi-icons.pointz.in/secureusageicons/secure_flag_v2.png",
				},
			},
			Title:              "अपने पैसे को सुरक्षित रखने के लिए कुछ सुझाव",
			LangSelectedText:   "हिन्दी",
			LangUnselectedText: "हिंदी में पढ़ें",
			Cta: &deeplinkPb.Cta{
				Text: "मुझे समझ आ गया",
			},
		},
	}
}

func (s *Service) updateDecisionInfoCache(ctx context.Context, isOnbCompleted bool, onbCompletedAt time.Time, showSecureUsageGuidelinesScreen bool, actorId string) error {
	if err := s.nextActionDecisionCache.UpdateNextActionDecisionInfo(ctx, actorId, &model.NextActionDecisionInfo{
		IsOnboardingCompleted:           isOnbCompleted,
		OnboardingCompletedAt:           onbCompletedAt,
		ShowSecureUsageGuidelinesScreen: showSecureUsageGuidelinesScreen,
	}); err != nil {
		logger.Error(ctx, "error in UpdateNextActionDecisionInfo", zap.Error(err))
		return err
	}

	return nil
}

func (s *Service) getOnbCompletedNextAction(ctx context.Context, actorId string, decisionInfo *model.NextActionDecisionInfo) *deeplinkPb.Deeplink {
	switch {
	case s.shouldShowPostOnboardingSoftIntentScreen(ctx, actorId, nil, decisionInfo.OnboardingCompletedAt):
		return getHomeSoftIntentSelectionScreen()
	case s.dynConf.Flags().EnableSecureUsageGuidelinesConsent() && decisionInfo.ShowSecureUsageGuidelinesScreen:
		return getSecureGuidelinesScreen(s.dynConf.SecureUsageGuidelineVersion())
	default:
		return actionAfterOnboardingComplete
	}
}

// shouldShowPostOnboardingSoftIntentScreen checks if the post onboarding soft intent screen should be shown to the user
// following checks are performed -
// 1. Feature flag for soft intent selection screen is enabled
// 2. Feature flag for post onboarding soft intent screen is enabled
// 3. Time since onboarding completion is greater than the wait duration
// 4. Time since onboarding completion is less than the max wait duration
// 5. if onb details is not provided, fetch it from db
// 6. if soft intent selection metadata is not already set
func (s *Service) shouldShowPostOnboardingSoftIntentScreen(ctx context.Context, actorId string, onbDetails *onbPb.OnboardingDetails, onbCompletedAt time.Time) bool {
	if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dynConf.SoftIntentSelectionConfig().SoftIntentCollectionScreenFeatureConfig()) {
		logger.Debug(ctx, "soft intent selection screen feature is disabled")
		return false
	}
	if !s.dynConf.SoftIntentSelectionConfig().PostOnboardingSoftIntentScreenConfig().Enabled(ctx) {
		logger.Debug(ctx, "post onboarding soft intent screen is disabled")
		return false
	}
	if time.Since(onbCompletedAt) < s.dynConf.SoftIntentSelectionConfig().PostOnboardingSoftIntentScreenConfig().WaitDuration(ctx) {
		logger.Debug(ctx, "time since onboarding completion is less than wait duration")
		return false
	}
	if time.Since(onbCompletedAt) > s.dynConf.SoftIntentSelectionConfig().PostOnboardingSoftIntentScreenConfig().MaxWaitDuration() {
		logger.Debug(ctx, "time since onboarding completion is greater than max wait duration")
		return false
	}

	if onbDetails == nil {
		var err error
		onbDetails, err = s.onboardingDao.GetOnboardingDetailsByActor(ctx, actorId)
		if txn.IsRecordNotFoundError(err) {
			logger.Debug(ctx, "onboarding details not found for actor", zap.String("actorId", actorId))
			return false
		}
		if err != nil {
			logger.Error(ctx, "error in get onboarding details by id", zap.Error(err))
			return false
		}
	}

	if onbDetails.GetStageMetadata().GetSoftIntentSelectionMetadata() != nil {
		logger.Debug(ctx, "soft intent selection metadata already set")
		return false
	}

	return true
}

func (s *Service) getNextActionFromCache(ctx context.Context, actorId string) (*deeplinkPb.Deeplink, error) {
	if !s.dynConf.NextActionDecisionCacheConfig().IsCacheEnabled() {
		return nil, errors.New("NextActionDecisionCache is disabled")
	}
	decisionInfo, err := s.nextActionDecisionCache.GetNextActionDecisionInfo(ctx, actorId)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Debug(ctx, "cache entry not found for actor", zap.Error(err))
			return nil, err
		}

		logger.Error(ctx, "error in getting next action decision from cache", zap.Error(err))
		return nil, err
	}

	if !decisionInfo.IsOnboardingCompleted {
		logger.Info(ctx, "onboarding not completed")
		return nil, errors.New("onboarding not completed")
	}

	nextAction := s.getOnbCompletedNextAction(ctx, actorId, decisionInfo)
	return nextAction, nil
}

func (s *Service) GetOnboardingDetailsMin(ctx context.Context, req *onbPb.GetOnboardingDetailsMinRequest) (*onbPb.GetOnboardingDetailsMinResponse, error) {
	onb, err := s.onboardingDao.GetOnboardingDetailsMinByActorId(ctx, req.GetActorId())
	if txn.IsRecordNotFoundError(err) {
		return &onbPb.GetOnboardingDetailsMinResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	if err != nil {
		logger.Error(ctx, "GetDetails error in get onboarding", zap.Error(err))
		return &onbPb.GetOnboardingDetailsMinResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &onbPb.GetOnboardingDetailsMinResponse{
		Status:               rpc.StatusOk(),
		OnboardingDetailsMin: onb,
	}, nil
}

func (s *Service) ProcessRiskVerdict(ctx context.Context, req *onbPb.ProcessRiskVerdictRequest) (*onbPb.ProcessRiskVerdictResponse, error) {
	if req.GetVerdict() == onbPb.Verdict_VERDICT_UNSPECIFIED {
		logger.Error(ctx, "invalid verdict")
		return &onbPb.ProcessRiskVerdictResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid verdict"),
		}, nil
	}
	getDetailsRes, err := s.GetDetails(ctx, &onbPb.GetDetailsRequest{
		ActorId:    req.GetActorId(),
		CachedData: true,
	})
	if rpcErr := epifigrpc.RPCError(getDetailsRes, err); rpcErr != nil {
		logger.Error(ctx, "error in fetching onboarding details", zap.Error(rpcErr))
		return &onbPb.ProcessRiskVerdictResponse{
			Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
		}, nil
	}

	getSummaryRes, err := s.livenessClient.GetLivenessSummary(ctx, &liveness.GetLivenessSummaryRequest{
		ActorId:      req.GetActorId(),
		RequestId:    auth.GetLivenessSummaryReqId(getDetailsRes.GetDetails()),
		LivenessFlow: liveness.LivenessFlow_ONBOARDING,
	})
	if rpcErr := epifigrpc.RPCError(getSummaryRes, err); rpcErr != nil {
		if getSummaryRes.GetStatus().IsRecordNotFound() {
			return &onbPb.ProcessRiskVerdictResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error in fetching liveness summary details", zap.Error(rpcErr))
		return &onbPb.ProcessRiskVerdictResponse{
			Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
		}, nil
	}

	livSummary := getSummaryRes.GetSummary()
	// mark both liveness and facematch as failed
	if req.GetVerdict() == onbPb.Verdict_VERDICT_FAIL {
		if err = s.annotateLivenessAttempt(ctx, livSummary, req.GetVerdict(), req.GetCaseId()); err != nil {
			return &onbPb.ProcessRiskVerdictResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}

		if err = s.annotateFMAttempt(ctx, livSummary, req.GetVerdict(), req.GetCaseId()); err != nil {
			return &onbPb.ProcessRiskVerdictResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
		// annotating only in best effort scneario as we don't want to mark as manually_passed for already passed liveness and facematch
	} else if req.GetVerdict() == onbPb.Verdict_VERDICT_PASS {
		// annotate only if facematch has failed
		if livSummary.IsSummaryFMFailed() {
			if err = s.annotateFMAttempt(ctx, livSummary, req.GetVerdict(), req.GetCaseId()); err != nil {
				return &onbPb.ProcessRiskVerdictResponse{
					Status: rpc.StatusInternalWithDebugMsg(err.Error()),
				}, nil
			}
		}
		// annotate liveness as this could be a liveness failure case getting passed/ risk screening case getting passed.
		if err = s.annotateLivenessAttempt(ctx, livSummary, req.GetVerdict(), req.GetCaseId()); err != nil {
			return &onbPb.ProcessRiskVerdictResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	}

	return &onbPb.ProcessRiskVerdictResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) annotateLivenessAttempt(ctx context.Context, livSummary *liveness.LivenessSummary, verdict onbPb.Verdict, caseId string) error {
	annotateLivRes, err := s.livenessClient.AnnotateLivenessAttempt(ctx, &liveness.AnnotateLivenessAttemptRequest{
		ActorId:   livSummary.GetActorId(),
		RequestId: livSummary.GetLivenessAttemptId(),
		Annotation: &liveness.Annotation{
			CaseId:        caseId,
			ReviewVerdict: getLivenessVerdict(ctx, verdict),
		},
	})
	if rpcErr := epifigrpc.RPCError(annotateLivRes, err); rpcErr != nil {
		logger.Error(ctx, "error in annotating liveness attempt", zap.Error(rpcErr))
		return rpcErr
	}
	return nil
}

func (s *Service) annotateFMAttempt(ctx context.Context, livSummary *liveness.LivenessSummary, verdict onbPb.Verdict, caseId string) error {
	annotateFMRes, err := s.livenessClient.AnnotateFacematchAttempt(ctx, &liveness.AnnotateFacematchAttemptRequest{
		ActorId:   livSummary.GetActorId(),
		RequestId: livSummary.GetFacematchAttemptId(),
		Annotation: &liveness.FaceMatchAnnotation{
			CaseId:        caseId,
			ReviewVerdict: getFaceMatchVerdict(ctx, verdict),
		},
		ReviewVerdict: getLivenessVerdict(ctx, verdict),
	})
	if rpcErr := epifigrpc.RPCError(annotateFMRes, err); rpcErr != nil {
		logger.Error(ctx, "error in annotating facematch attempt", zap.Error(rpcErr))
		return rpcErr
	}
	return nil
}

func (s *Service) syncOnboarding(ctx context.Context, actorId string) error {
	userRes, err := s.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userRes, err); rpcErr != nil {
		if userRes.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "user record not found in sync onboarding")
			return epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error in getting user", zap.Error(rpcErr))
		return rpcErr
	}

	if userRes.GetUser().GetDeletedAt().IsValid() {
		logger.Info(ctx, "user is already deleted, skipping sync onboarding")
		return nil
	}
	onb, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, actorId)
	if err != nil {
		if txn.IsRecordNotFoundError(err) {
			logger.Info(ctx, "record not found")
			return epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error in getting onboarding details", zap.Error(err))
		return err
	}
	// Syncing states for every feature before attempting to reset as feature status in onboarding details is updated aysnc
	for _, featureValue := range onbPb.Feature_value {
		feature := onbPb.Feature(featureValue)
		// todo(saiteja): fix this
		// syncing SA syncs TPAP feature status
		// ingoring TPAP to avoid discrepancy in stage group metrics
		if feature == onbPb.Feature_FEATURE_UNSPECIFIED || feature == onbPb.Feature_FEATURE_UPI_TPAP {
			continue
		}
		// ignoring to avoid drop off alerts
		// since we are getting PASSPORT_VERIFICATION drop off alerts as it is triggered in reset user and sync onboarding
		if feature.IsNonResidentUserOnboarding() && !onb.GetFeature().IsNonResidentUserOnboarding() {
			continue
		}

		if _, err = s.processOnboarding(ctx, &model.OrchestratorRequest{
			Onb:     onb,
			Feature: feature,
		}); err != nil {
			if rpc.StatusFromError(err).IsRecordNotFound() {
				return epifierrors.ErrRecordNotFound
			}
			return err
		}
	}
	return nil
}

func getLivenessVerdict(ctx context.Context, verdict onbPb.Verdict) liveness.Verdict {
	switch verdict {
	case onbPb.Verdict_VERDICT_UNSPECIFIED:
		return liveness.Verdict_VERDICT_UNSPECIFIED
	case onbPb.Verdict_VERDICT_PASS:
		return liveness.Verdict_VERDICT_PASS
	case onbPb.Verdict_VERDICT_FAIL:
		return liveness.Verdict_VERDICT_FAIL
	default:
		logger.Info(ctx, "unexpected verdict")
		return liveness.Verdict_VERDICT_UNSPECIFIED
	}
}

func getFaceMatchVerdict(ctx context.Context, verdict onbPb.Verdict) liveness.FaceMatchVerdict {
	switch verdict {
	case onbPb.Verdict_VERDICT_UNSPECIFIED:
		return liveness.FaceMatchVerdict_FACE_MATCH_VERDICT_UNSPECIFIED
	case onbPb.Verdict_VERDICT_PASS:
		return liveness.FaceMatchVerdict_FACE_MATCH_VERDICT_PASS
	case onbPb.Verdict_VERDICT_FAIL:
		return liveness.FaceMatchVerdict_FACE_MATCH_VERDICT_FAIL
	default:
		logger.Info(ctx, "unexpected verdict")
		return liveness.FaceMatchVerdict_FACE_MATCH_VERDICT_UNSPECIFIED
	}
}

func (s *Service) ProcessCreditReportVerificationEvent(ctx context.Context, event *notification.CreditReportDownloadEvent) (*onbPb.ProcessCreditReportVerificationEventResponse, error) {
	var (
		queueRes = func(err error) (*onbPb.ProcessCreditReportVerificationEventResponse, error) {
			return &onbPb.ProcessCreditReportVerificationEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queue.GetStatusFromErr(err),
				},
			}, nil
		}
	)
	actorId := event.GetActorId()
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	resp, errResp := s.creditReportClient.GetCreditReport(ctx, &creditreportv2.GetCreditReportRequest{
		ActorId: actorId,
	})
	if err := epifigrpc.RPCError(resp, errResp); err != nil {
		logger.Error(ctx, "error in getting credit report", zap.Error(err))
		return queueRes(err)
	}
	if resp.GetCreditReport().GetDownloadStatus() != creditreportv2.DownloadStatus_DOWNLOAD_STATUS_DOWNLOAD_SUCCESSFUL {
		logger.Info(ctx, "credit report is not downloaded successfully, dropping packet")
		return queueRes(nil)
	}
	if resp.GetCreditReport().GetVerificationStatus() == creditreportv2.VerificationStatus_VERIFICATION_STATUS_UNSPECIFIED {
		logger.Info(ctx, "credit report is not verified successfully, triggering verification")
		_, _ = s.creditReportClient.InitiateCreditReportVerification(ctx, &creditreportv2.InitiateCreditReportVerificationRequest{
			ActorId: actorId,
		})
		return queueRes(epifierrors.ErrTransient)
	}
	if resp.GetCreditReport().GetVerificationStatus() == creditreportv2.VerificationStatus_VERIFICATION_STATUS_IN_PROGRESS {
		logger.Info(ctx, "credit report verification not successful yet", zap.String(logger.STATUS, resp.GetCreditReport().GetVerificationStatus().String()))
		return queueRes(epifierrors.ErrTransient)
	}
	if resp.GetCreditReport().GetVerificationStatus() != creditreportv2.VerificationStatus_VERIFICATION_STATUS_SUCCESS {
		logger.Info(ctx, "credit report verification failed", zap.String(logger.STATUS, resp.GetCreditReport().GetVerificationStatus().String()))
		return queueRes(nil)
	}
	onbResp, errResp := s.GetDetails(ctx, &onbPb.GetDetailsRequest{
		ActorId:    actorId,
		CachedData: true,
	})
	if err := epifigrpc.RPCError(onbResp, errResp); err != nil {
		logger.Error(ctx, "error in getting onboarding details", zap.Error(err))
		return queueRes(err)
	}
	if onbResp.GetDetails().GetStageDetails().GetStageMapping()[onbPb.OnboardingStage_APP_SCREENING.String()].GetState() != onbPb.OnboardingState_UNSPECIFIED {
		logger.Info(ctx, "app screening is already run, ignore the packet")
		return queueRes(nil)
	}
	scrResp, errResp := s.screenerClient.RunCheck(ctx, &screener.RunCheckRequest{
		ActorId:   actorId,
		CheckType: screener.CheckType_CHECK_TYPE_INCOME_ESTIMATE,
	})
	if err := epifigrpc.RPCError(scrResp, errResp); err != nil {
		logger.Error(ctx, "error in running screener check", zap.Error(err))
		return queueRes(err)
	}
	scrResp, errResp = s.screenerClient.RunCheck(ctx, &screener.RunCheckRequest{
		ActorId:   actorId,
		CheckType: screener.CheckType_CHECK_TYPE_LENDABILITY,
	})
	if err := epifigrpc.RPCError(scrResp, errResp); err != nil {
		logger.Error(ctx, "error in running screener check", zap.Error(err))
		return queueRes(err)
	}
	checkStatusResp, errResp := s.screenerClient.GetScreenerAttemptsByActorId(ctx, &screener.GetScreenerAttemptsByActorIdRequest{
		ActorId: actorId,
	})
	if err := epifigrpc.RPCError(checkStatusResp, errResp); err != nil {
		logger.Error(ctx, "error in fetching screener check status", zap.Error(err))
		return queueRes(nil)
	}
	for _, check := range checkStatusResp.GetChecksMap() {
		if check.GetCheckType() != screener.CheckType_CHECK_TYPE_LENDABILITY ||
			check.GetCheckType() != screener.CheckType_CHECK_TYPE_INCOME_ESTIMATE {
			continue
		}
		logger.Info(ctx, "screener check status on credit report verification", zap.String(logger.STATUS, check.GetCheckType().String()))
	}
	return queueRes(nil)
}
