package stageproc

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/onboarding/dao"
)

type WBOnboardingCompleteStage struct {
	onbDao     dao.OnboardingDao
	timeClient datetime.Time
}

func NewWBOnboardingCompleteStage(
	onbDao dao.OnboardingDao,
	timeClient datetime.Time,
) *WBOnboardingCompleteStage {
	return &WBOnboardingCompleteStage{
		onbDao:     onbDao,
		timeClient: timeClient,
	}
}

func (s *WBOnboardingCompleteStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	onb := req.GetOnb()
	if updateErr := updateFeatureOnbStatus(ctx, s.onbDao, onb, onbPb.Feature_FEATURE_WEALTH_ANALYSER, onbPb.FeatureStatus_FEATURE_STATUS_ACTIVE, s.timeClient); updateErr != nil {
		logger.Error(ctx, "error in updateFeatureOnbStatus", zap.Error(updateErr))
		return nil, updateErr
	}

	if onbErr := updateFiLiteAccessibility(ctx, s.onbDao, onb, s.timeClient); onbErr != nil {
		logger.Error(ctx, "error in updateFiLiteAccessibility", zap.Error(onbErr))
		return nil, onbErr
	}

	return &StageProcessorResponse{}, NoActionError
}
