package stageproc

import (
	"context"

	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/user/onboarding/dao"

	"go.uber.org/zap"
)

func updateStageMetadata(ctx context.Context, onboardingDao dao.OnboardingDao, onbId string, md *onbPb.StageMetadata) error {
	if err := onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
	}, &onbPb.OnboardingDetails{
		OnboardingId:  onbId,
		StageMetadata: md,
	}); err != nil {
		logger.Error(ctx, "error while updating stage metadata", logOnb(onbId), zap.Error(err))
		return err
	}
	return nil
}

func getOnboardingDetails(ctx context.Context, onboardingDao dao.OnboardingDao, onboardingId string) (*onbPb.OnboardingDetails, error) {
	onboardingDetails, err := onboardingDao.GetOnboardingDetailsById(ctx, onboardingId)
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			logger.Info(ctx, "onboarding details not found", logOnb(onboardingId))
		} else {
			logger.Error(ctx, "error in get onboarding details", zap.Error(err), logOnb(onboardingId))
		}
		return nil, err
	}
	return onboardingDetails, nil
}

func updateEkycNameDobValidationDataInTx(ctx context.Context, onboardingDao dao.OnboardingDao, onbId string, data *onbPb.EKYCNameDOBValidationData) error {
	if err := storagev2.RunCRDBIdempotentTxn(ctx, TxnRetries, func(ctx context.Context) error {
		return updateEkycNameDobValidationData(ctx, onboardingDao, onbId, data)
	}); err != nil {
		logger.Error(ctx, "error in running txn to save ekyc name dob data", zap.Error(err))
		return err
	}
	return nil
}

func updateEkycNameDobValidationData(ctx context.Context, onboardingDao dao.OnboardingDao, onbId string, data *onbPb.EKYCNameDOBValidationData) error {
	// get latest Onb details
	onb, err := getOnboardingDetails(ctx, onboardingDao, onbId)
	if err != nil {
		return err
	}

	// set ekyc name dob validation in metadata
	if onb.GetStageMetadata() == nil {
		onb.StageMetadata = &onbPb.StageMetadata{}
	}
	onb.StageMetadata.EkycNameDobValidation = data

	// update in DB
	return updateStageMetadata(ctx, onboardingDao, onbId, onb.StageMetadata)
}

// nolint: unparam
func updateStatus(ctx context.Context, onbDao dao.OnboardingDao, onboardingId string, stage onbPb.OnboardingStage, state onbPb.OnboardingState) (*onbPb.OnboardingDetails, error) {
	onb, err := onbDao.UpdateStatus(ctx, onboardingId, stage, state)
	if err != nil {
		logger.Error(ctx, "failed to update onboarding status", zap.Error(err), logOnb(onboardingId))
		return nil, err
	}
	return onb, nil
}
