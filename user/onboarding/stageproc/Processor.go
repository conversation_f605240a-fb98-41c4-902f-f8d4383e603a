// nolint
package stageproc

import (
	"context"

	"github.com/epifi/gamma/api/frontend/deeplink"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
)

/*
What do we want to achieve in this refactor ?
Stage 1:
1. New stage should be easy to add
2. Anyone should be able to add a stage, following structure makes sure that adding a stage will not break the previous structure
3. Ensure stage updates are happening in a transaction

Stage 2:
4. Clean out the feature flags (optional - and separate out feature flag for IOS and android if possible)
5. Backward compatibility when a new stage is added
	5.1 Basic need - When new stage is added and user is stuck in a stage ahead of this, they come back
	5.2 Can adding a version number per ordering of stage ensures that ?
6. Ensure tests are written for each processor

What all is done in a processor today ?
1. Validations whether a stage should be processed or not
2. Get status of a stage
3. Update stage status, meta-data
*/

type StageProcessorType interface {
	StageProcessor(context.Context, *StageProcessorRequest) (*StageProcessorResponse, error)
}

type StageProcessorRequest struct {
	Onb *onbPb.OnboardingDetails
}

func (s *StageProcessorRequest) GetOnb() *onbPb.OnboardingDetails {
	if s != nil {
		return s.Onb
	}
	return nil
}

type StageProcessorResponse struct {
	NextAction     *deeplink.Deeplink
	AnalyticsProps map[string]string
}

func (s *StageProcessorResponse) GetNextAction() *deeplink.Deeplink {
	if s != nil {
		return s.NextAction
	}
	return nil
}

func (s *StageProcessorResponse) GetAnalyticsProps() map[string]string {
	if s != nil {
		return s.AnalyticsProps
	}
	return nil
}
