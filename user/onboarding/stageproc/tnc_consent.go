package stageproc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/user/onboarding/pkg/nri"

	"github.com/epifi/gamma/user/onboarding/pkg/constant"

	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	colorPkg "github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/comms"
	upPb "github.com/epifi/gamma/api/comms/user_preference"
	"github.com/epifi/gamma/api/consent"
	consentPb "github.com/epifi/gamma/api/consent"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	consentDlPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/consent"
	"github.com/epifi/gamma/api/typesv2/form"
	"github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	pkgDl "github.com/epifi/gamma/pkg/deeplink"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/user/config/genconf"
)

var (
	mapping = map[consent.ConsentType]string{
		consent.ConsentType_FI_TNC:                      "Fi Terms",
		consent.ConsentType_CONSENT_FI_NRE_NRO_ACCOUNTS: "Fi Terms",
		consent.ConsentType_FI_PRIVACY_POLICY:           "Fi Secure Privacy Policy",
		// Removing below TNCs based on https://monorail.pointz.in/p/fi-app/issues/detail?id=67196
		// consent.ConsentType_FED_TNC:           "Federal Bank Terms and Conditions",
		// consent.ConsentType_FI_WEALTH_TNC:     "Fi Wealth Tnc",
	}
)

type TncConsentStage struct {
	consentClient  consentPb.ConsentClient
	userPrefClient upPb.UserPreferenceClient
	conf           *genconf.OnboardingConfig
	// AB evaluator with AB experiment type as `string` to evaluate custom experiments whose variants are not defined in protos
	abEvaluatorGeneric *release.ABEvaluator[string]
}

func NewTncConsentStage(
	consentClient consentPb.ConsentClient,
	userPrefClient upPb.UserPreferenceClient,
	actorClient actorPb.ActorClient,
	userClient userPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	conf *genconf.OnboardingConfig,
) *TncConsentStage {
	return &TncConsentStage{
		consentClient:      consentClient,
		userPrefClient:     userPrefClient,
		conf:               conf,
		abEvaluatorGeneric: getABEvaluatorOfFeature[string](actorClient, userClient, userGroupClient, conf.ABFeatureReleaseConfig(), func(str string) string { return str }),
	}
}

// nolint:funlen
func (s *TncConsentStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	isConsentScreenV2Enabled := s.isConsentScreenV2Enabled(ctx, req.GetOnb().GetActorId())
	if req.GetOnb().GetFeature().IsNonResidentUserOnboarding() {
		logger.Info(ctx, "running tnc stage proc for NRI onb")
		// TODO: Currently, the new consent screen is not enabled for NRI onboarding since we don't have the copy for it yet.
		return s.stageProcessorForNROnb(ctx, req, false, nri.IsFeatureRequiredNfcCheck(req.GetOnb().GetFeature()))
	}
	logger.Info(ctx, fmt.Sprintf("running tnc stage proc for regular onb: %v", req.GetOnb().GetFeature()))

	// Check consent requirement
	isMinimalConsentFlagEnabled := apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.conf.Flags().EnableSavingsIntroScreen())
	consentList := s.getConsentList(isMinimalConsentFlagEnabled)

	consentRes, err := s.consentClient.CheckConsentRequirement(ctx, &consent.CheckConsentRequirementRequest{
		ActorId:      req.GetOnb().GetActorId(),
		ConsentTypes: consentList,
		Owner:        commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if err != nil {
		logger.Error(ctx, "failed to fetch consent requirement from consent service", zap.Error(err))
		return nil, err
	}
	if !consentRes.GetIsConsentRequired() {
		return s.CheckAndReturnWAConsentBasedResponse(ctx, req.Onb.GetActorId())
	}
	var consentUrls []*dlPb.ConsentTypeUrl
	consentTypeUrlMap := make(map[dlPb.ConsentTypeUrl_ConsentType]string)
	for _, consentUrl := range consentRes.ConsentTypeUrls {
		temp := &dlPb.ConsentTypeUrl{
			ConsentType: dlPb.ConsentTypeUrl_ConsentType(consentUrl.ConsentType),
			ConsentUrl:  consentUrl.ConsentUrl,
			Name:        mapping[consentUrl.ConsentType],
		}
		consentTypeUrlMap[temp.GetConsentType()] = temp.GetConsentUrl()
		consentUrls = append(consentUrls, temp)
	}
	if isConsentScreenV2Enabled {
		return &StageProcessorResponse{
			NextAction: s.consentScreenV2(consentTypeUrlMap),
		}, nil
	}
	return &StageProcessorResponse{
		NextAction: &dlPb.Deeplink{
			Screen: dlPb.Screen_CONSENT,
			ScreenOptions: &dlPb.Deeplink_ConsentScreenOptions{
				ConsentScreenOptions: &dlPb.ConsentScreenOptions{
					Title:          constant.TnCTitle,
					Subtitle:       constant.TnCSubtitle,
					ConsentTypeUrl: consentUrls,
					CheckboxConsent1: &dlPb.ConsentScreenOptions_CheckboxConsent{
						ConsentType:  consentPb.ConsentType_FI_TNC.String(),
						ConsentTypes: []string{consentPb.ConsentType_FI_TNC.String()},
						Text:         constant.TnCConsentIndianResidentText,
					},
					CheckboxConsent2: &dlPb.ConsentScreenOptions_CheckboxConsent{
						ConsentType:  consentPb.ConsentType_FI_PRIVACY_POLICY.String(),
						ConsentTypes: []string{consentPb.ConsentType_FI_PRIVACY_POLICY.String()},
						Text:         constant.TnCConsentPersonalDataText,
					},
					CheckboxConsent3: &dlPb.ConsentScreenOptions_CheckboxConsent{
						ConsentType:  consentPb.ConsentType_CREDIT_REPORT_TNC.String(),
						ConsentTypes: []string{consentPb.ConsentType_CREDIT_REPORT_TNC.String()},
						Text:         constant.TnCConsentExperianText,
					},
					ShowMinimalScreen: isMinimalConsentFlagEnabled,
				},
			},
		},
	}, nil
}

func (s *TncConsentStage) getConsentList(isMinimalConsent bool) []consentPb.ConsentType {
	if isMinimalConsent {
		return []consent.ConsentType{
			consent.ConsentType_FI_PRIVACY_POLICY,
			consent.ConsentType_FI_TNC,
		}
	}
	return []consent.ConsentType{
		consent.ConsentType_FED_TNC,
		consent.ConsentType_FI_PRIVACY_POLICY,
		consent.ConsentType_FI_TNC,
		consent.ConsentType_FI_WEALTH_TNC,
	}
}

func (s *TncConsentStage) CheckIfWAConsentTaken(ctx context.Context, actorId string) (bool, error) {
	res, err := s.userPrefClient.GetPreference(ctx, &upPb.GetPreferenceRequest{
		ActorId:  actorId,
		Medium:   comms.Medium_WHATSAPP,
		Category: comms.Category_CATEGORY_PROMOTIONAL,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsRecordNotFound() {
			return false, nil
		}
		return false, err
	}

	res, err = s.userPrefClient.GetPreference(ctx, &upPb.GetPreferenceRequest{
		ActorId:  actorId,
		Medium:   comms.Medium_WHATSAPP,
		Category: comms.Category_CATEGORY_TRANSACTIONAL,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsRecordNotFound() {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// nolint:funlen,funlen
func (s *TncConsentStage) stageProcessorForNROnb(ctx context.Context, req *StageProcessorRequest, isConsentScreenV2Enabled bool, isNfcCheckRequired bool) (*StageProcessorResponse, error) {
	consentList := []consent.ConsentType{
		consent.ConsentType_CONSENT_FI_NRE_NRO_ACCOUNTS,
		consent.ConsentType_FI_PRIVACY_POLICY,
		consent.ConsentType_CONSENT_NOT_POLITICALLY_EXPOSED,
		consent.ConsentType_CONSENT_NON_INDIAN_RESIDENCY,
		consent.ConsentType_CONSENT_NON_RESIDENT_ACCOUNT_TESTING,
		consent.ConsentType_CONSENT_FATCA_CRS,
	}

	consentRes, err := s.consentClient.CheckConsentRequirement(ctx, &consent.CheckConsentRequirementRequest{
		ActorId:      req.GetOnb().GetActorId(),
		ConsentTypes: consentList,
		Owner:        commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if err != nil {
		logger.Error(ctx, "failed to fetch consent requirement from consent service", zap.Error(err))
		return nil, err
	}
	if !consentRes.GetIsConsentRequired() {
		return s.CheckAndReturnWAConsentBasedResponse(ctx, req.GetOnb().GetActorId())
	}
	var consentUrls []*dlPb.ConsentTypeUrl
	for _, consentUrl := range consentRes.GetConsentTypeUrls() {
		temp := &dlPb.ConsentTypeUrl{
			ConsentType: dlPb.ConsentTypeUrl_ConsentType(consentUrl.GetConsentType()),
			ConsentUrl:  consentUrl.GetConsentUrl(),
			Name:        mapping[consentUrl.GetConsentType()],
		}

		// Hack to show the "Fi Terms" block for NRI onboarding on TNC screen
		if consentUrl.GetConsentType() == consentPb.ConsentType_CONSENT_FI_NRE_NRO_ACCOUNTS {
			temp.ConsentType = dlPb.ConsentTypeUrl_FI_TNC
		}
		consentUrls = append(consentUrls, temp)
	}
	if isConsentScreenV2Enabled {
		return &StageProcessorResponse{
			NextAction: s.consentScreenV2ForNR(isNfcCheckRequired),
		}, nil
	}
	return &StageProcessorResponse{
		NextAction: &dlPb.Deeplink{
			Screen: dlPb.Screen_CONSENT,
			ScreenOptions: &dlPb.Deeplink_ConsentScreenOptions{
				ConsentScreenOptions: &dlPb.ConsentScreenOptions{
					Title:          constant.TnCTitle,
					Subtitle:       constant.TnCSubtitle,
					ConsentTypeUrl: consentUrls,
					CheckboxConsent1: &dlPb.ConsentScreenOptions_CheckboxConsent{
						ConsentTypes: []string{
							consentPb.ConsentType_CONSENT_NON_RESIDENT_ACCOUNT_TESTING.String(),
						},
						Text: constant.ConsentText1,
					},
					CheckboxConsent2: &dlPb.ConsentScreenOptions_CheckboxConsent{
						ConsentTypes: []string{
							consentPb.ConsentType_CONSENT_NON_INDIAN_RESIDENCY.String(),
							consentPb.ConsentType_CONSENT_NOT_POLITICALLY_EXPOSED.String(),
							consentPb.ConsentType_CONSENT_FATCA_CRS.String(),
						},
						Text: constant.ConsentText2,
					},
					CheckboxConsent3: &dlPb.ConsentScreenOptions_CheckboxConsent{
						ConsentTypes: []string{
							consentPb.ConsentType_CONSENT_FI_NRE_NRO_ACCOUNTS.String(),
							consentPb.ConsentType_FI_PRIVACY_POLICY.String(),
						},
						Text: constant.ConsentText3,
					},
					ShowMinimalScreen: true, // for the new UI
					EnableNfcCheck:    isNfcCheckRequired,
				},
			},
		},
	}, nil
}

// nolint:dupl,funlen
func (s *TncConsentStage) consentScreenV2(consentTypeUrlMap map[dlPb.ConsentTypeUrl_ConsentType]string) *dlPb.Deeplink {
	return deeplinkv3.GetDeeplinkV3WithoutError(dlPb.Screen_CONSENT_V2, &consentDlPb.ConsentV2ScreenOptions{
		HeaderBar: pkgDl.HeaderBarForFederalOwnedScreen(),
		Title:     commontypes.GetTextFromStringFontColourFontStyle(constant.TncV2Title, colorPkg.ColorNight, commontypes.FontStyle_HEADLINE_XL),
		Subtitle: commontypes.GetTextFromStringFontColourFontStyle(constant.TncV2Subtitle,
			colorPkg.ColorSlate, commontypes.FontStyle_BODY_S),
		Cards: []*consentDlPb.ConsentCard{
			{
				Header: &consentDlPb.ConsentCard_Header{
					IconText: ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(constant.TncIndianResidentImgV2, 20, 31)).
						WithTexts(commontypes.GetTextFromStringFontColourFontStyle(constant.TncIndianResidentV2Title, colorPkg.ColorCharcoal, commontypes.FontStyle_HEADLINE_S)).
						WithLeftImagePadding(17),
					BgColor: widget.GetBlockBackgroundColour("#E6E9ED"),
				},
				CheckboxItem: &consentDlPb.ConsentCard_CheckBoxItem{
					Consents: &form.Consent{
						Text: commontypes.GetTextFromHtmlStringFontColourFontStyle(constant.TncIndianResidentConsentV2,
							colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_HEADLINE_XS),
						IsMandatory: true,
						ConsentIds:  []string{consentPb.ConsentType_CONSENT_INDIAN_RESIDENCY.String()},
					},
					BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
				},
			},
			{
				Header: &consentDlPb.ConsentCard_Header{
					IconText: ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(constant.TncTermsAndConditionImgV2, 22, 23)).
						WithTexts(commontypes.GetTextFromStringFontColourFontStyle(constant.TncTermsAndConditionTitleV2, colorPkg.ColorCharcoal, commontypes.FontStyle_HEADLINE_S)).
						WithLeftImagePadding(17),
					BgColor: widget.GetBlockBackgroundColour("#E6E9ED"),
				},
				CheckboxItem: &consentDlPb.ConsentCard_CheckBoxItem{
					Consents: &form.Consent{
						Text: commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf(constant.TncTermsAndConditionConsentV2, consentTypeUrlMap[dlPb.ConsentTypeUrl_FI_TNC], consentTypeUrlMap[dlPb.ConsentTypeUrl_FI_PRIVACY_POLICY]),
							colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_HEADLINE_XS),
						IsMandatory: true,
						ConsentIds:  []string{consentPb.ConsentType_FI_TNC.String(), consentPb.ConsentType_FI_PRIVACY_POLICY.String()},
					},
					BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
				},
			},
			{
				Header: &consentDlPb.ConsentCard_Header{
					IconText: ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(constant.TncCreditInfoImgV2, 17, 27)).
						WithTexts(commontypes.GetTextFromStringFontColourFontStyle(constant.TncCreditInfoTitleV2, colorPkg.ColorCharcoal, commontypes.FontStyle_HEADLINE_S)).
						WithLeftImagePadding(17),
					BgColor: widget.GetBlockBackgroundColour("#E6E9ED"),
				},
				CheckboxItem: &consentDlPb.ConsentCard_CheckBoxItem{
					Consents: &form.Consent{
						Text: commontypes.GetTextFromHtmlStringFontColourFontStyle(constant.TncCreditInfoConsentV2,
							colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_HEADLINE_XS),
						IsMandatory: true,
						ConsentIds:  []string{consentPb.ConsentType_CREDIT_REPORT_TNC.String()},
					},
					BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
				},
			},
		},
		Ctas: []*dlPb.Cta{
			{
				Type:         dlPb.Cta_CONTINUE,
				Text:         "Continue",
				DisplayTheme: dlPb.Cta_PRIMARY,
			},
		},
		Footer: ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(constant.TncFiSecurityImgV2, 24, 24)).
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle(constant.TncFiSecurityTitleV2, colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_XS)).
			WithLeftImagePadding(4).WithContainerBackgroundColor("#EFF2F6").WithContainerPadding(10, 0, 10, 0),
		EnableNfcCheck: false,
	})
}

// TODO (RISHU SAHU): Change the copy for NR consent screen
// nolint:dupl,funlen
func (s *TncConsentStage) consentScreenV2ForNR(isNfcCheckRequired bool) *dlPb.Deeplink {
	return deeplinkv3.GetDeeplinkV3WithoutError(dlPb.Screen_CONSENT_V2, &consentDlPb.ConsentV2ScreenOptions{
		HeaderBar: pkgDl.HeaderBarForFederalOwnedScreen(),
		Title:     commontypes.GetTextFromStringFontColourFontStyle(constant.TncV2Title, colorPkg.ColorNight, commontypes.FontStyle_HEADLINE_XL),
		Subtitle: commontypes.GetTextFromStringFontColourFontStyle(constant.TncV2Subtitle,
			colorPkg.ColorSlate, commontypes.FontStyle_BODY_S),
		Cards: []*consentDlPb.ConsentCard{
			{
				Header: &consentDlPb.ConsentCard_Header{
					IconText: ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(constant.TncIndianResidentImgV2, 20, 31)).
						WithTexts(commontypes.GetTextFromStringFontColourFontStyle(constant.TncIndianResidentV2Title, colorPkg.ColorCharcoal, commontypes.FontStyle_HEADLINE_S)).
						WithLeftImagePadding(17),
					BgColor: widget.GetBlockBackgroundColour("#E6E9ED"),
				},
				CheckboxItem: &consentDlPb.ConsentCard_CheckBoxItem{
					Consents: &form.Consent{
						Text: commontypes.GetTextFromHtmlStringFontColourFontStyle(constant.ConsentText1,
							colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_HEADLINE_XS),
						IsMandatory: true,
						ConsentIds:  []string{consentPb.ConsentType_CONSENT_NON_RESIDENT_ACCOUNT_TESTING.String()},
					},
					BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
				},
			},
			{
				Header: &consentDlPb.ConsentCard_Header{
					IconText: ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(constant.TncTermsAndConditionImgV2, 22, 23)).
						WithTexts(commontypes.GetTextFromStringFontColourFontStyle(constant.TncTermsAndConditionTitleV2, colorPkg.ColorCharcoal, commontypes.FontStyle_HEADLINE_S)).
						WithLeftImagePadding(21),
					BgColor: widget.GetBlockBackgroundColour("#E6E9ED"),
				},
				CheckboxItem: &consentDlPb.ConsentCard_CheckBoxItem{
					Consents: &form.Consent{
						Text: commontypes.GetTextFromHtmlStringFontColourFontStyle(constant.ConsentText2,
							colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_HEADLINE_XS),
						IsMandatory: true,
						ConsentIds: []string{consentPb.ConsentType_CONSENT_NON_INDIAN_RESIDENCY.String(),
							consentPb.ConsentType_CONSENT_NOT_POLITICALLY_EXPOSED.String(),
							consentPb.ConsentType_CONSENT_FATCA_CRS.String(),
						},
					},
					BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
				},
			},
			{
				Header: &consentDlPb.ConsentCard_Header{
					IconText: ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(constant.TncCreditInfoImgV2, 17, 27)).
						WithTexts(commontypes.GetTextFromStringFontColourFontStyle(constant.TncCreditInfoTitleV2, colorPkg.ColorCharcoal, commontypes.FontStyle_HEADLINE_S)).
						WithLeftImagePadding(19),
					BgColor: widget.GetBlockBackgroundColour("#E6E9ED"),
				},
				CheckboxItem: &consentDlPb.ConsentCard_CheckBoxItem{
					Consents: &form.Consent{
						Text: commontypes.GetTextFromHtmlStringFontColourFontStyle(constant.ConsentText3,
							colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_HEADLINE_XS),
						IsMandatory: true,
						ConsentIds: []string{consentPb.ConsentType_CONSENT_FI_NRE_NRO_ACCOUNTS.String(),
							consentPb.ConsentType_FI_PRIVACY_POLICY.String(),
						},
					},
					BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
				},
			},
		},
		Ctas: []*dlPb.Cta{
			{
				Type:         dlPb.Cta_CONTINUE,
				Text:         "Continue",
				DisplayTheme: dlPb.Cta_PRIMARY,
			},
		},
		Footer: ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(constant.TncFiSecurityImgV2, 24, 24)).
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle(constant.TncFiSecurityTitleV2, colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_XS)).
			WithLeftImagePadding(4).WithContainerBackgroundColor("#EFF2F6").WithContainerPadding(10, 0, 10, 0),
		EnableNfcCheck: isNfcCheckRequired,
	})
}

func (s *TncConsentStage) CheckAndReturnWAConsentBasedResponse(ctx context.Context, actorId string) (*StageProcessorResponse, error) {
	isWAConsentTaken, err := s.CheckIfWAConsentTaken(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "failed to check for WA consent of user", zap.Error(err))
		// gracefully handling userPrefClient.GetPreference outage as it's not a critical API
		return nil, SkipStageError
	}
	if isWAConsentTaken {
		return nil, NoActionError
	}
	dl, analyticsProps := NewPhonePermissionDl(ctx, s.isATTPromptEnabledForActor(ctx, actorId), apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.conf.Flags().EnableContactPermissionInOnb()), true, true, nil)
	return &StageProcessorResponse{
		NextAction:     dl,
		AnalyticsProps: analyticsProps,
	}, nil
}

// isATTPromptEnabledForActor checks whether ATT permission prompt is enabled / to-be-shown to the actor or not in iOS (Only for iOS)
func (s *TncConsentStage) isATTPromptEnabledForActor(ctx context.Context, actorId string) bool {
	screenEnabled, variant, err := s.abEvaluatorGeneric.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_ATT_IOS_PERMISSION_PROMPT).WithActorId(actorId))
	if err != nil {
		logger.WarnWithCtx(ctx, "error evaluating AB for ATT iOS permission prompt. ignoring silently", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	return screenEnabled && variant == "ONE"
}

func (s *TncConsentStage) isConsentScreenV2Enabled(ctx context.Context, actorId string) bool {
	commonConstraintsData := release.NewCommonConstraintData(types.Feature_FEATURE_ENABLE_CONSENT_SCREEN_V2).WithActorId(actorId)
	isPriorities, variant, err := s.abEvaluatorGeneric.Evaluate(ctx, commonConstraintsData)
	if err != nil {
		logger.WarnWithCtx(ctx, "error evaluating AB variant for consent screen v2", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	return isPriorities && variant == "ONE"
}
