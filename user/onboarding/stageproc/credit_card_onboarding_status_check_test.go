package stageproc

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	mockDateTime "github.com/epifi/be-common/pkg/datetime/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"

	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	"github.com/epifi/gamma/api/firefly/v2/mocks"
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	mockUser "github.com/epifi/gamma/api/user/mocks"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	mockDao "github.com/epifi/gamma/user/onboarding/dao/mocks"
	onbHelperMocks "github.com/epifi/gamma/user/onboarding/helper/mocks"
)

func TestCreditCardOnboardingStatusCheck_StageProcessor(t *testing.T) {
	t.Parallel()
	var (
		timeA = &timestampPb.Timestamp{
			Seconds: 100000,
			Nanos:   100000,
		}
		dl = &deepLinkPb.Deeplink{
			Screen: deepLinkPb.Screen_PARENTS_NAME_GETTER,
		}
	)

	type mockStruct struct {
		fireflyV2Client *mocks.MockFireflyV2Client
		onbDao          *mockDao.MockOnboardingDao
		timeClient      *mockDateTime.MockTime
		dlHelper        *onbHelperMocks.MockDeeplinkHelper
		userClient      *mockUser.MockUsersClient
	}

	tests := []struct {
		name     string
		req      *StageProcessorRequest
		want     *StageProcessorResponse
		wantErr  error
		mockFunc func(mocks *mockStruct)
	}{
		{
			name: "card request in progress",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId:      "actor-1",
					OnboardingId: "onb-1",
					FiLiteDetails: &onbPb.FiLiteDetails{
						IsEnabled: commontypes.BooleanEnum_FALSE,
					},
				},
			},
			want: &StageProcessorResponse{
				NextAction: dl,
			},
			mockFunc: func(mocks *mockStruct) {
				// Mock GetCardRequest call
				mocks.fireflyV2Client.EXPECT().GetCardRequest(context.Background(), &ffBeV2Pb.GetCardRequestRequest{
					Identifier: &ffBeV2Pb.GetCardRequestRequest_ActorId{
						ActorId: "actor-1",
					},
					RequestType: ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING,
				}).Return(&ffBeV2Pb.GetCardRequestResponse{
					Status: rpc.StatusOk(),
					CardRequest: &ffBeV2Pb.CardRequest{
						Id:     "req-1",
						Status: ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
					},
				}, nil)

				// Mock GetLandingInfo call
				mocks.fireflyV2Client.EXPECT().GetLandingInfo(context.Background(), &ffBeV2Pb.GetLandingInfoRequest{
					ActorId: "actor-1",
				}).Return(&ffBeV2Pb.GetLandingInfoResponse{
					Status:     rpc.StatusOk(),
					NextAction: dl,
				}, nil)
			},
		},
		{
			name: "card request success",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId:      "actor-2",
					OnboardingId: "onb-2",
					FiLiteDetails: &onbPb.FiLiteDetails{
						IsEnabled: commontypes.BooleanEnum_FALSE,
					},
				},
			},
			want:    nil,
			wantErr: NoActionError,
			mockFunc: func(mocks *mockStruct) {
				// Mock GetCardRequest call
				mocks.fireflyV2Client.EXPECT().GetCardRequest(context.Background(), &ffBeV2Pb.GetCardRequestRequest{
					Identifier: &ffBeV2Pb.GetCardRequestRequest_ActorId{
						ActorId: "actor-2",
					},
					RequestType: ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING,
				}).Return(&ffBeV2Pb.GetCardRequestResponse{
					Status: rpc.StatusOk(),
					CardRequest: &ffBeV2Pb.CardRequest{
						Id:     "req-2",
						Status: ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS,
					},
				}, nil)

				mocks.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(
					context.Background(),
					[]onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_FEATURE_DETAILS},
					gomock.Any(),
				).Return(nil)

				mocks.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(
					context.Background(),
					[]onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_FI_LITE_DETAILS},
					gomock.Any(),
				).Return(nil)

				// Mock Now() calls - expect 2 calls (one for each UpdateOnboardingDetailsByColumns)
				mocks.timeClient.EXPECT().Now().Return(timeA.AsTime()).Times(2)
			},
		},
		{
			name: "card request failed",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId:      "actor-3",
					OnboardingId: "onb-3",
					FiLiteDetails: &onbPb.FiLiteDetails{
						IsEnabled: commontypes.BooleanEnum_FALSE,
					},
				},
			},
			want: &StageProcessorResponse{
				NextAction: getCreditCardUserIneligibleToOnboardScreen(dl),
			},
			mockFunc: func(mocks *mockStruct) {
				// Mock GetCardRequest call
				mocks.fireflyV2Client.EXPECT().GetCardRequest(context.Background(), &ffBeV2Pb.GetCardRequestRequest{
					Identifier: &ffBeV2Pb.GetCardRequestRequest_ActorId{
						ActorId: "actor-3",
					},
					RequestType: ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING,
				}).Return(&ffBeV2Pb.GetCardRequestResponse{
					Status: rpc.StatusOk(),
					CardRequest: &ffBeV2Pb.CardRequest{
						Id:     "req-3",
						Status: ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED,
					},
				}, nil)

				// Mock GetIntentSelectionDL call
				mocks.dlHelper.EXPECT().GetIntentSelectionDL(
					context.Background(),
					"actor-3",
					onbPb.IntentSelectionEntryPoint_INTENT_SELECTION_ENTRY_POINT_CC_INELIGIBLE.String(),
				).Return(dl, nil)
			},
		},
		{
			name: "no card request found",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId:      "actor-4",
					OnboardingId: "onb-4",
					FiLiteDetails: &onbPb.FiLiteDetails{
						IsEnabled: commontypes.BooleanEnum_FALSE,
					},
				},
			},
			want: &StageProcessorResponse{
				NextAction: dl,
			},
			mockFunc: func(mocks *mockStruct) {
				// Mock GetCardRequest call with record not found
				mocks.fireflyV2Client.EXPECT().GetCardRequest(context.Background(), &ffBeV2Pb.GetCardRequestRequest{
					Identifier: &ffBeV2Pb.GetCardRequestRequest_ActorId{
						ActorId: "actor-4",
					},
					RequestType: ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING,
				}).Return(&ffBeV2Pb.GetCardRequestResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				// Mock UpdateOnboardingDetailsByColumns calls
				mocks.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(
					context.Background(),
					[]onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_FEATURE_DETAILS},
					gomock.Any(),
				).Return(nil)

				// Mock GetLandingInfo call
				mocks.fireflyV2Client.EXPECT().GetLandingInfo(context.Background(), &ffBeV2Pb.GetLandingInfoRequest{
					ActorId: "actor-4",
				}).Return(&ffBeV2Pb.GetLandingInfoResponse{
					Status:     rpc.StatusOk(),
					NextAction: dl,
				}, nil)
			},
		},
		{
			name: "invalid argument error from DAO",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId:      "actor-5",
					OnboardingId: "onb-5",
					FiLiteDetails: &onbPb.FiLiteDetails{
						IsEnabled: commontypes.BooleanEnum_FALSE,
					},
				},
			},
			want:    nil,
			wantErr: epifierrors.ErrInvalidArgument,
			mockFunc: func(mocks *mockStruct) {
				// Mock GetCardRequest call with invalid argument error
				// Return the error directly instead of through the response status
				mocks.fireflyV2Client.EXPECT().GetCardRequest(context.Background(), &ffBeV2Pb.GetCardRequestRequest{
					Identifier: &ffBeV2Pb.GetCardRequestRequest_ActorId{
						ActorId: "actor-5",
					},
					RequestType: ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING,
				}).Return(nil, epifierrors.ErrInvalidArgument)
			},
		},
		{
			name: "invalid argument error when updating feature status",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId:      "actor-5",
					OnboardingId: "onb-5",
					FiLiteDetails: &onbPb.FiLiteDetails{
						IsEnabled: commontypes.BooleanEnum_FALSE,
					},
				},
			},
			want:    nil,
			wantErr: epifierrors.ErrInvalidArgument,
			mockFunc: func(mocks *mockStruct) {
				// Mock GetCardRequest call
				mocks.fireflyV2Client.EXPECT().GetCardRequest(context.Background(), &ffBeV2Pb.GetCardRequestRequest{
					Identifier: &ffBeV2Pb.GetCardRequestRequest_ActorId{
						ActorId: "actor-5",
					},
					RequestType: ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING,
				}).Return(&ffBeV2Pb.GetCardRequestResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				// Mock UpdateOnboardingDetailsByColumns call with invalid argument error
				mocks.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(
					context.Background(),
					[]onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_FEATURE_DETAILS},
					gomock.Any(), // Using gomock.Any() to avoid comparison issues
				).Return(epifierrors.ErrInvalidArgument)
			},
		},
		{
			name: "invalid argument error when updating FiLite details",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId:      "actor-6",
					OnboardingId: "onb-6",
					FiLiteDetails: &onbPb.FiLiteDetails{
						IsEnabled: commontypes.BooleanEnum_FALSE,
					},
				},
			},
			want:    nil,
			wantErr: epifierrors.ErrInvalidArgument,
			mockFunc: func(mocks *mockStruct) {
				// Mock GetCardRequest call
				mocks.fireflyV2Client.EXPECT().GetCardRequest(context.Background(), &ffBeV2Pb.GetCardRequestRequest{
					Identifier: &ffBeV2Pb.GetCardRequestRequest_ActorId{
						ActorId: "actor-6",
					},
					RequestType: ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING,
				}).Return(&ffBeV2Pb.GetCardRequestResponse{
					Status: rpc.StatusOk(),
					CardRequest: &ffBeV2Pb.CardRequest{
						Id:     "req-6",
						Status: ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS,
					},
				}, nil)

				// Mock UpdateOnboardingDetailsByColumns call for active status
				mocks.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(
					context.Background(),
					[]onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_FEATURE_DETAILS},
					gomock.Any(), // Using gomock.Any() to avoid comparison issues
				).Return(nil)

				// Mock UpdateFiliteEnabled with invalid argument error
				mocks.timeClient.EXPECT().Now().Return(timeA.AsTime()).Times(2) // Specific number of calls expected
				mocks.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(
					context.Background(),
					[]onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_FI_LITE_DETAILS},
					gomock.Any(), // Using gomock.Any() for FiLite details
				).Return(epifierrors.ErrInvalidArgument)
			},
		},
		{
			name: "invalid argument error when getting intent selection deeplink",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId:      "actor-7",
					OnboardingId: "onb-7",
					FiLiteDetails: &onbPb.FiLiteDetails{
						IsEnabled: commontypes.BooleanEnum_FALSE,
					},
				},
			},
			want:    nil,
			wantErr: epifierrors.ErrInvalidArgument,
			mockFunc: func(mocks *mockStruct) {
				// Mock GetCardRequest call
				mocks.fireflyV2Client.EXPECT().GetCardRequest(context.Background(), &ffBeV2Pb.GetCardRequestRequest{
					Identifier: &ffBeV2Pb.GetCardRequestRequest_ActorId{
						ActorId: "actor-7",
					},
					RequestType: ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING,
				}).Return(&ffBeV2Pb.GetCardRequestResponse{
					Status: rpc.StatusOk(),
					CardRequest: &ffBeV2Pb.CardRequest{
						Id:     "req-7",
						Status: ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED,
					},
				}, nil)

				// Mock GetIntentSelectionDL call with invalid argument error
				mocks.dlHelper.EXPECT().GetIntentSelectionDL(
					context.Background(),
					"actor-7",
					onbPb.IntentSelectionEntryPoint_INTENT_SELECTION_ENTRY_POINT_CC_INELIGIBLE.String(),
				).Return(nil, epifierrors.ErrInvalidArgument)
			},
		},
		{
			name: "invalid argument error when getting landing info",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId:      "actor-8",
					OnboardingId: "onb-8",
					FiLiteDetails: &onbPb.FiLiteDetails{
						IsEnabled: commontypes.BooleanEnum_FALSE,
					},
				},
			},
			want:    nil,
			wantErr: epifierrors.ErrInvalidArgument,
			mockFunc: func(mocks *mockStruct) {
				// Mock GetCardRequest call with record not found
				mocks.fireflyV2Client.EXPECT().GetCardRequest(context.Background(), &ffBeV2Pb.GetCardRequestRequest{
					Identifier: &ffBeV2Pb.GetCardRequestRequest_ActorId{
						ActorId: "actor-8",
					},
					RequestType: ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING,
				}).Return(&ffBeV2Pb.GetCardRequestResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				// Mock UpdateOnboardingDetailsByColumns calls with AnyTimes()
				mocks.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(
					context.Background(),
					[]onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_FEATURE_DETAILS},
					gomock.Any(),
				).Return(nil)

				// Mock GetLandingInfo call with invalid argument error directly
				mocks.fireflyV2Client.EXPECT().GetLandingInfo(context.Background(), &ffBeV2Pb.GetLandingInfoRequest{
					ActorId: "actor-8",
				}).Return(nil, epifierrors.ErrInvalidArgument)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			defer ctr.Finish()
			mockFFV2Client := mocks.NewMockFireflyV2Client(ctr)
			mockOnbDao := mockDao.NewMockOnboardingDao(ctr)
			timeClient := mockDateTime.NewMockTime(ctr)
			dlHelper := onbHelperMocks.NewMockDeeplinkHelper(ctr)
			userClient := mockUser.NewMockUsersClient(ctr)
			s := NewCreditCardOnboardingStatusCheck(mockFFV2Client, mockOnbDao, timeClient, dlHelper, userClient)

			mockDeps := &mockStruct{
				fireflyV2Client: mockFFV2Client,
				onbDao:          mockOnbDao,
				timeClient:      timeClient,
				dlHelper:        dlHelper,
				userClient:      userClient,
			}
			if tt.mockFunc != nil {
				tt.mockFunc(mockDeps)
			}
			got, err := s.StageProcessor(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.ErrorIs(t, err, tt.wantErr)
			} else {
				require.NoError(t, err)
			}
			assert.Equal(t, tt.want, got)
		})
	}
}
