package stageproc

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/bankcust"

	"github.com/epifi/be-common/api/rpc"

	bankCustMocks "github.com/epifi/gamma/api/bankcust/mocks"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/mocks"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
)

func Test_getKYCLevel(t *testing.T) {
	kycLevel := kyc.KYCLevel_FULL_KYC
	actId := "dad0dd6a-622f-11ec-90d6-0242ac120003"
	type args struct {
		actorId string
		mocks   func(uc *bankCustMocks.MockBankCustomerServiceClient, kc *mocks.MockKycClient)
	}
	tests := []struct {
		name    string
		args    args
		want    kyc.KYCLevel
		wantErr error
	}{
		{
			name: "kyc level found in bank customer",
			args: args{
				actorId: actId,
				mocks: func(bc *bankCustMocks.MockBankCustomerServiceClient, kc *mocks.MockKycClient) {
					bc.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankcust.GetBankCustomerRequest_ActorId{
							ActorId: actId,
						},
					}).Return(&bankcust.GetBankCustomerResponse{
						Status: rpc.StatusOk(),
						BankCustomer: &bankcust.BankCustomer{
							Id:      "bc-1",
							ActorId: actId,
							KycInfo: &bankcust.KYCInfo{
								KycLevel: kycLevel,
							},
						},
					}, nil)
				},
			},
			want:    kycLevel,
			wantErr: nil,
		},
		{
			name: "kyc level found in kyc",
			args: args{
				actorId: actId,
				mocks: func(bc *bankCustMocks.MockBankCustomerServiceClient, kc *mocks.MockKycClient) {
					bc.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankcust.GetBankCustomerRequest_ActorId{
							ActorId: actId,
						},
					}).Return(&bankcust.GetBankCustomerResponse{
						Status: rpc.StatusOk(),
						BankCustomer: &bankcust.BankCustomer{
							Id:      "bc-1",
							ActorId: actId,
							KycInfo: &bankcust.KYCInfo{
								KycLevel: 0,
							},
						},
					}, nil)

					kc.EXPECT().CheckKYCStatus(gomock.Any(), gomock.Eq(&kyc.CheckKYCStatusRequest{
						ActorId: actId,
					})).Return(&kyc.CheckKYCStatusResponse{
						Status:   rpc.StatusOk(),
						KycLevel: kycLevel,
					}, nil)
				},
			},
			want:    kycLevel,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			kycClient := mocks.NewMockKycClient(ctrl)
			bcClient := bankCustMocks.NewMockBankCustomerServiceClient(ctrl)

			tt.args.mocks(bcClient, kycClient)
			got, err := getKYCLevel(context.Background(), bcClient, kycClient, tt.args.actorId, "")
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("getKYCLevel Error expected: %v, got: %v", tt.wantErr, err)
				return
			}
			if err != nil && !errors.Is(err, tt.wantErr) {
				t.Errorf("unexpected error msg\n expected: %v,\n got: %v", tt.wantErr, err)
				return
			}
			assert.Equalf(t, tt.want, got, "getKYCLevel(%v)", tt.args.actorId)
		})
	}
}

func Test_IsUserStuck(t *testing.T) {
	ctx := context.Background()

	type args struct {
		onb       *onbPb.OnboardingDetails
		stuckTime time.Duration
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "stuck more than max time",
			args: args{
				onb: &onbPb.OnboardingDetails{
					StageDetails: &onbPb.StageDetails{
						StageMapping: map[string]*onbPb.StageInfo{
							onbPb.OnboardingStage_ACCOUNT_CREATION.String(): {
								StartedAt: timestampPb.New(time.Now().Add(-(time.Minute * 40))),
							},
						},
					},
					CurrentOnboardingStage: onbPb.OnboardingStage_ACCOUNT_CREATION,
				},
				stuckTime: time.Minute * 10,
			},
			want: true,
		},
		{
			name: "stuck less than max time",
			args: args{
				onb: &onbPb.OnboardingDetails{
					StageDetails: &onbPb.StageDetails{
						StageMapping: map[string]*onbPb.StageInfo{
							onbPb.OnboardingStage_ACCOUNT_CREATION.String(): {
								StartedAt: timestampPb.New(time.Now().Add(-(time.Minute * 4))),
							},
						},
					},
					CurrentOnboardingStage: onbPb.OnboardingStage_ACCOUNT_CREATION,
				},
				stuckTime: time.Minute * 10,
			},
			want: false,
		},
		{
			name: "mapping not found",
			args: args{
				onb: &onbPb.OnboardingDetails{
					StageDetails: &onbPb.StageDetails{
						StageMapping: map[string]*onbPb.StageInfo{},
					},
					CurrentOnboardingStage: onbPb.OnboardingStage_ACCOUNT_CREATION,
				},
				stuckTime: time.Minute * 10,
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			res := IsUserStuck(ctx, tt.args.onb, tt.args.stuckTime)
			assert.Equal(t, res, tt.want)
		})
	}
}

func Test_isUserPastCurrentStage(t *testing.T) {
	var (
		actorId = "actor-id"
	)
	type args struct {
		stagesOrder []onbPb.OnboardingStage
		details     *onbPb.OnboardingDetails
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "user past stage",
			args: args{
				stagesOrder: SAOnboardingStagesOrder,
				details: &onbPb.OnboardingDetails{
					ActorId: actorId,
					StageDetails: &onbPb.StageDetails{
						StageMapping: map[string]*onbPb.StageInfo{
							onbPb.OnboardingStage_INTENT_SELECTION.String(): {
								State: onbPb.OnboardingState_INITIATED,
							},
							onbPb.OnboardingStage_APP_SCREENING.String(): {
								State: onbPb.OnboardingState_SUCCESS,
							},
						},
					},
					StageMetadata: &onbPb.StageMetadata{
						IntentSelectionMetadata: &onbPb.IntentSelectionMetadata{
							Selection: onbPb.OnboardingIntent_ONBOARDING_INTENT_FI_LITE,
							Choices: []onbPb.OnboardingIntent{
								onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT,
								onbPb.OnboardingIntent_ONBOARDING_INTENT_FI_LITE,
							},
						},
					},
					CurrentOnboardingStage: onbPb.OnboardingStage_INTENT_SELECTION,
				},
			},
			want: true,
		},
		{
			name: "user not past stage",
			args: args{
				stagesOrder: SAOnboardingStagesOrder,
				details: &onbPb.OnboardingDetails{
					ActorId: actorId,
					StageDetails: &onbPb.StageDetails{
						StageMapping: map[string]*onbPb.StageInfo{
							onbPb.OnboardingStage_INTENT_SELECTION.String(): {
								State: onbPb.OnboardingState_INITIATED,
							},
						},
					},
					StageMetadata: &onbPb.StageMetadata{
						IntentSelectionMetadata: &onbPb.IntentSelectionMetadata{
							Selection: onbPb.OnboardingIntent_ONBOARDING_INTENT_FI_LITE,
							Choices: []onbPb.OnboardingIntent{
								onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT,
								onbPb.OnboardingIntent_ONBOARDING_INTENT_FI_LITE,
							},
						},
					},
					CurrentOnboardingStage: onbPb.OnboardingStage_INTENT_SELECTION,
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, isUserPastCurrentStage(tt.args.stagesOrder, tt.args.details), "isUserPastCurrentStage(%v, %v)", tt.args.stagesOrder, tt.args.details)
		})
	}
}

func Test_GetStagesTillAppScreening(t *testing.T) {
	tests := []struct {
		name         string
		currentStage onbPb.OnboardingStage
		want         []onbPb.OnboardingStage
		wantErr      error
	}{
		{
			name:         "work email",
			currentStage: onbPb.OnboardingStage_WORK_EMAIL_VERIFICATION,
			want: []onbPb.OnboardingStage{
				onbPb.OnboardingStage_WORK_EMAIL_VERIFICATION,
				onbPb.OnboardingStage_UAN_PRESENCE_CHECK,
				onbPb.OnboardingStage_ITR_INTIMATION_VERIFICATION,
			},
			wantErr: nil,
		},
		{
			name:         "in app screening stage",
			currentStage: onbPb.OnboardingStage_APP_SCREENING,
			want:         nil,
			wantErr:      stageAfterAppScreeningErr,
		},
		{
			name:         "not a screening stage",
			currentStage: onbPb.OnboardingStage_CREDIT_CARD_ONBOARDING_STATUS_CHECK,
			want:         nil,
			wantErr:      notSaOnbStageErr,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetOnbStagesTillAppScreening(tt.currentStage)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("getStagesTillAppScreening Error expected: %v, got: %v", tt.wantErr, err)
			}
			assert.Equalf(t, tt.want, got, "getStagesTillAppScreening(%v)", tt.currentStage)
		})
	}
}
