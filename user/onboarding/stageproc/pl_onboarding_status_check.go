package stageproc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/onboarding/dao"
)

type PLOnboardingStatusCheck struct {
	palClient  palPb.PreApprovedLoanClient
	onbDao     dao.OnboardingDao
	timeClient datetime.Time
}

func NewPLOnboardingStatusCheckStage(palClient palPb.PreApprovedLoanClient, onbDao dao.OnboardingDao, timeClient datetime.Time) *PLOnboardingStatusCheck {
	return &PLOnboardingStatusCheck{
		palClient:  palClient,
		onbDao:     onbDao,
		timeClient: timeClient,
	}
}

var _ StageProcessorType = (*PLOnboardingStatusCheck)(nil)

func (s *PLOnboardingStatusCheck) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		onb     = req.GetOnb()
		actorId = onb.GetActorId()
	)

	getDashboardResp, errDashboard := s.palClient.GetDashboardV2(ctx, &palPb.GetDashboardRequest{
		ActorId:                 actorId,
		FetchEligibilityRequest: true,
	})
	if rpcErr := epifigrpc.RPCError(getDashboardResp, errDashboard); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
		logger.Error(ctx, "failure while calling GetDashboardV2 rpc", zap.Error(rpcErr))
		return nil, rpcErr
	}

	if len(getDashboardResp.GetLoanInfoList()) > 0 {
		if updateErr := updateFeatureOnbStatus(ctx, s.onbDao, onb, onbPb.Feature_FEATURE_PL, onbPb.FeatureStatus_FEATURE_STATUS_ACTIVE, s.timeClient); updateErr != nil {
			logger.Error(ctx, "error in updateFeatureOnbStatus", zap.Error(updateErr))
			return nil, updateErr
		}

		if onbErr := s.updateFiLiteAccessibility(ctx, onb); onbErr != nil {
			logger.Error(ctx, "error in updateFiLiteAccessibility", zap.Error(onbErr))
			return nil, onbErr
		}

		_, _ = updateStatus(ctx, s.onbDao, onb.GetOnboardingId(), onbPb.OnboardingStage_PL_ONBOARDING_STATUS_CHECK, onbPb.OnboardingState_SUCCESS)
		return &StageProcessorResponse{
			NextAction: redirectionActionAfterOnbComplete(onb.GetFeatureDetails().GetFeatureInfo()[onb.GetFeature().String()].GetFeatureOnboardingEntryPoint()),
		}, nil
	}

	// TODO: get the next action from perform and use that instead of fetching from client service and setting logic
	if getDashboardResp.GetRecentLoanRequest() != nil && getDashboardResp.GetRecentLoanRequest().GetNextAction() != nil &&
		getDashboardResp.GetRecentLoanRequest().GetVendor() == palPb.Vendor_FEDERAL &&
		getDashboardResp.GetRecentLoanRequest().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB {
		return &StageProcessorResponse{
			NextAction: getDashboardResp.GetRecentLoanRequest().GetNextAction(),
		}, nil
	}

	// in Fed NTB flow, pan dob verification happens in eligibility flow. To move back from onboarding orchestrated screens to PL
	// orchestrated screen, we need specific handling in eligibility flow only for pan and dob stage
	if getDashboardResp.GetRecentLoanRequest() != nil && getDashboardResp.GetRecentLoanRequest().GetNextAction() != nil &&
		getDashboardResp.GetRecentLoanRequest().GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY &&
		onb.GetStageProcLastResponse().GetStage() == onbPb.OnboardingStage_DOB_AND_PAN {
		return &StageProcessorResponse{
			NextAction: getDashboardResp.GetRecentLoanRequest().GetNextAction(),
		}, nil
	}

	if updateErr := updateFeatureOnbStatus(ctx, s.onbDao, onb, onbPb.Feature_FEATURE_PL, onbPb.FeatureStatus_FEATURE_STATUS_ONBOARDING_IN_PROGRESS, s.timeClient); updateErr != nil {
		logger.Error(ctx, "error in updateFeatureOnbStatus", zap.Error(updateErr))
		return nil, updateErr
	}

	return &StageProcessorResponse{
		NextAction: &dlPb.Deeplink{
			Screen: dlPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			ScreenOptions: &dlPb.Deeplink_PreApprovedLoanLandingScreenOptions{
				PreApprovedLoanLandingScreenOptions: &dlPb.PreApprovedLoanLandingScreenOptions{},
			},
		},
	}, nil
}

func (s *PLOnboardingStatusCheck) updateFiLiteAccessibility(ctx context.Context, onb *onbPb.OnboardingDetails) error {
	if onb.FiLiteDetails == nil {
		onb.FiLiteDetails = &onbPb.FiLiteDetails{}
	}

	onb.FiLiteDetails.IsEnabled = commontypes.BooleanEnum_TRUE
	if onb.FiLiteDetails.AccessibilityEnabledAt == nil {
		onb.FiLiteDetails.AccessibilityEnabledAt = timestampPb.New(s.timeClient.Now())
	}

	return s.onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_FI_LITE_DETAILS}, onb)
}
