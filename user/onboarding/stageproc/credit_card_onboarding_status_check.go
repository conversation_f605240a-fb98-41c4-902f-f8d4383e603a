//nolint:funlen,dupl
package stageproc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	userPb "github.com/epifi/gamma/api/user"
	onbpb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"

	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
)

type CreditCardOnboardingStatusCheck struct {
	fireflyV2Client ffBeV2Pb.FireflyV2Client
	onbDao          dao.OnboardingDao
	timeClient      datetime.Time
	dlHelper        helper.DeeplinkHelper
	userClient      userPb.UsersClient
}

func NewCreditCardOnboardingStatusCheck(fireflyV2Client ffBeV2Pb.FireflyV2Client, onbDao dao.OnboardingDao, timeClient datetime.Time, dlHelper helper.DeeplinkHelper, userClient userPb.UsersClient) *CreditCardOnboardingStatusCheck {
	return &CreditCardOnboardingStatusCheck{
		fireflyV2Client: fireflyV2Client,
		onbDao:          onbDao,
		timeClient:      timeClient,
		dlHelper:        dlHelper,
		userClient:      userClient,
	}
}

func (s *CreditCardOnboardingStatusCheck) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	actorId := req.GetOnb().GetActorId()

	// Get card request status using the V2 client
	cardStatusResp, err := s.fireflyV2Client.GetCardRequest(ctx, &ffBeV2Pb.GetCardRequestRequest{
		Identifier: &ffBeV2Pb.GetCardRequestRequest_ActorId{
			ActorId: actorId,
		},
		RequestType: ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING,
	})

	if te := epifigrpc.RPCError(cardStatusResp, err); te != nil && !cardStatusResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "Error fetching card request status", zap.Error(te))
		return nil, te
	}

	// Check if there's a valid card request
	hasCardRequest := !cardStatusResp.GetStatus().IsRecordNotFound() && cardStatusResp.GetCardRequest() != nil

	// If there's a card request, check its status
	if hasCardRequest {
		cardRequest := cardStatusResp.GetCardRequest()

		// Handle terminal states
		switch cardRequest.GetStatus() {
		case ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS:
			// Onboarding is complete, update the status
			logger.Info(ctx, "CC onboarding completed")
			if updateErr := updateFeatureOnbStatus(ctx, s.onbDao, req.GetOnb(), onbpb.Feature_FEATURE_CC, onbpb.FeatureStatus_FEATURE_STATUS_ACTIVE, s.timeClient); updateErr != nil {
				logger.Error(ctx, "error in updateFeatureOnbStatus", zap.Error(updateErr))
				return nil, updateErr
			}
			if onbErr := s.updateFiliteEnabled(ctx, req.Onb); onbErr != nil {
				logger.Error(ctx, "error in updateFiliteEnabled", zap.Error(onbErr))
				return nil, onbErr
			}
			return nil, NoActionError

		case ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED:
			// Onboarding failed, show ineligible screen
			intentDl, intentErr := s.dlHelper.GetIntentSelectionDL(ctx, actorId, onbpb.IntentSelectionEntryPoint_INTENT_SELECTION_ENTRY_POINT_CC_INELIGIBLE.String())
			if intentErr != nil {
				logger.Error(ctx, "failure while calling GetIntentSelectionDL", zap.Error(intentErr))
				return nil, intentErr
			}
			return &StageProcessorResponse{
				NextAction: getCreditCardUserIneligibleToOnboardScreen(intentDl),
			}, nil
		}
	} else {
		if updateErr := updateFeatureOnbStatus(ctx, s.onbDao, req.Onb, onbpb.Feature_FEATURE_CC, onbpb.FeatureStatus_FEATURE_STATUS_ONBOARDING_IN_PROGRESS, s.timeClient); updateErr != nil {
			logger.Error(ctx, "error in updateFeatureOnbStatus", zap.Error(updateErr))
			return nil, updateErr
		}
	}

	// For all non-terminal states or no card request, get landing info
	landingInfo, err := s.fireflyV2Client.GetLandingInfo(ctx, &ffBeV2Pb.GetLandingInfoRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(landingInfo, err); te != nil {
		logger.Error(ctx, "error getting landing info", zap.Error(te))
		return nil, te
	}

	return &StageProcessorResponse{
		NextAction: landingInfo.GetNextAction(),
	}, nil
}

func (s *CreditCardOnboardingStatusCheck) updateFiliteEnabled(ctx context.Context, onb *onbpb.OnboardingDetails) error {
	if onb.FiLiteDetails == nil {
		onb.FiLiteDetails = &onbpb.FiLiteDetails{}
	}

	onb.FiLiteDetails.IsEnabled = commontypes.BooleanEnum_TRUE
	if onb.FiLiteDetails.AccessibilityEnabledAt == nil {
		onb.FiLiteDetails.AccessibilityEnabledAt = timestampPb.New(s.timeClient.Now())
	}

	return s.onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbpb.OnboardingDetailsFieldMask{onbpb.OnboardingDetailsFieldMask_FI_LITE_DETAILS}, onb)
}
