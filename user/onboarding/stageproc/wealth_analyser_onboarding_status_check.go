package stageproc

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"

	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/api/typesv2"
	mfScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/investment/mutualfund"
	onbScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	insightsPkg "github.com/epifi/gamma/insights/pkg"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding/dao"
	onbDl "github.com/epifi/gamma/user/onboarding/helper/deeplink"
)

type WealthAnalyserOnboardingStatusCheck struct {
	onbDao                 dao.OnboardingDao
	userClient             userPb.UsersClient
	timeClient             datetime.Time
	userConf               *genconf.Config
	eventLogger            events.EventLogger
	mfExternalOrdersClient mfPb.MFExternalOrdersClient
	releaseEvaluator       release.IEvaluator
	panProcessor           insightsPkg.IPanProcessor
	connectedAccountClient connectedAccountPb.ConnectedAccountClient
}

func NewWealthAnalyserOnboardingStatusCheckStage(
	onbDao dao.OnboardingDao,
	userClient userPb.UsersClient,
	timeClient datetime.Time,
	userConf *genconf.Config,
	eventLogger events.EventLogger,
	mfExternalOrdersClient mfPb.MFExternalOrdersClient,
	releaseEvaluator release.IEvaluator,
	panProcessor insightsPkg.IPanProcessor,
	connectedAccountClient connectedAccountPb.ConnectedAccountClient,
) *WealthAnalyserOnboardingStatusCheck {
	return &WealthAnalyserOnboardingStatusCheck{
		onbDao:                 onbDao,
		userClient:             userClient,
		timeClient:             timeClient,
		userConf:               userConf,
		eventLogger:            eventLogger,
		mfExternalOrdersClient: mfExternalOrdersClient,
		releaseEvaluator:       releaseEvaluator,
		panProcessor:           panProcessor,
		connectedAccountClient: connectedAccountClient,
	}
}

var _ StageProcessorType = (*WealthAnalyserOnboardingStatusCheck)(nil)

func (s *WealthAnalyserOnboardingStatusCheck) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	onb := req.GetOnb()
	acqChannel, sErr := s.wealthAnalyserAcqChannel(ctx, onb)
	if sErr != nil {
		return nil, sErr
	}
	if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.userConf.Onboarding().Flags().WealthAnalyserFeature()) {
		logger.Info(ctx, "user in wealth analyser with old app version")
		return &StageProcessorResponse{
			NextAction: onbDl.UpdateAppDeeplink,
		}, nil
	}

	logger.Info(ctx, "eligible user in wealth analyser", zap.String("acquisition_channel", acqChannel.String()))

	// Setting feature status to ONBOARDING_IN_PROGRESS to indicate that the user is in the wealth analyser onboarding flow
	if updateErr := updateFeatureOnbStatus(ctx, s.onbDao, onb, onbPb.Feature_FEATURE_WEALTH_ANALYSER, onbPb.FeatureStatus_FEATURE_STATUS_ONBOARDING_IN_PROGRESS, s.timeClient); updateErr != nil {
		logger.Error(ctx, "error in updateFeatureOnbStatus", zap.Error(updateErr))
		return nil, updateErr
	}
	s.eventLogger.LogWealthAnalyserUserJourneyStarted(ctx, onb.GetActorId(), acqChannel)

	// TODO(Ayush): Add support for experimentation construct, redirect to different flows based on the same
	// Possible flows
	//	1. Redirect to Home Screen and mark FI lite as enabled
	//	1. Redirect to Unified Data Collection flow (WIP) and based on the status of that redirect to home screen and mark FI lite as enabled

	isMfImportWealthOnboardingEnabled, err := s.releaseEvaluator.Evaluate(ctx,
		release.NewCommonConstraintData(typesv2.Feature_FEATURE_MF_IMPORT_WEALTH_BUILDER_ONBOARDING).WithActorId(onb.GetActorId()))
	if err != nil {
		logger.Error(ctx, "error in feature release evaluation", zap.Error(err))
		isMfImportWealthOnboardingEnabled = false
	}

	// Removing Fi Lite flag to force users to go through all the wealth analyser onboarding stages
	// This prevents users from landing directly on home screen and ensures they complete the onboarding flow
	// Ref PR: https://github.com/epiFi/gamma/pull/122783,
	// Ref Monorail: https://monorail.pointz.in/p/fi-app/issues/detail?id=93943
	// We have added a new stage OnboardingStage_WEALTH_ANALYSER_ONBOARDING_COMPLETE where we will enable the fi lite
	// flag and update Feature_FEATURE_WEALTH_ANALYSER to ACTIVE
	if fiLiteUpdateErr := removeFiLiteFlag(ctx, s.onbDao, onb); fiLiteUpdateErr != nil {
		logger.Error(ctx, "error in removeFiLiteFlag", zap.Error(fiLiteUpdateErr))
		return nil, fiLiteUpdateErr
	}
	if isMfImportWealthOnboardingEnabled {
		maxRetries := s.userConf.CreditReportConfig().DownloadWaitConfigForWealthBuilder().MaxAttemptsForCheckingDownloadStatus()
		sleepDuration := s.userConf.CreditReportConfig().DownloadWaitConfigForWealthBuilder().SleepDurationBetweenEachAttempt()
		var panDetails *insightsPkg.GetVerifiedOrUnverifiedPanResponse
		var panErr error
		var i int32

		// This loop adds latency to fetch PAN for the cases where it is not able to get in the first attempt
		// And waits for the background process(like credit report fetch, sms, etc.) to get completed(if any) to fetch PAN
		for i = 0; i < maxRetries; i++ {
			panDetails, panErr = s.panProcessor.GetVerifiedOrUnverifiedPan(ctx, onb.GetActorId())
			if panErr == nil || panErr != epifierrors.ErrRecordNotFound {
				break
			}
			if i < maxRetries-1 {
				time.Sleep(sleepDuration)
			}
		}
		if panErr != nil && !errors.Is(panErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error in fetching pan for mf import", zap.Error(panErr))
			return nil, errors.Wrap(panErr, "error in fetching pan for mf import")
		}
		if panDetails != nil && panDetails.Pan != "" {
			logger.Info(ctx, fmt.Sprintf("pan fetched for mf import after %v tries with %s sleep duration for actor: ", i+1, sleepDuration.String()), zap.String(logger.ACTOR_ID, onb.GetActorId()))
			redirectDeeplink, mfHoldingsErr := s.checkMfHoldingsImportStatus(ctx, onb.GetActorId())
			if mfHoldingsErr != nil {
				logger.Error(ctx, "error in checkMfHoldingsImportStatus", zap.Error(mfHoldingsErr))
				return nil, mfHoldingsErr
			}
			if redirectDeeplink != nil {
				return &StageProcessorResponse{
					NextAction: redirectDeeplink,
				}, nil
			}
		} else {
			logger.Info(ctx, fmt.Sprintf("could not fetch pan after %v tries with %s sleep duration for actor: ", i+1, sleepDuration.String()), zap.String(logger.ACTOR_ID, onb.GetActorId()))
		}
	}

	return nil, NoActionError
}

/*
Current ways for user to be marked Wealth Analyser eligible
  - User dropped off during onboarding and we manually moved them to wealth analyser using 'update_onboarding_feature' job
  - User dropped off from PAN DOB stage or intent selection stage and selected Wealth Analyser there
  - User selected Wealth Analyser as their intent, in that case the onboarding intent would be 'Wealth Analyser'
  - We have forced the user to Wealth analyser journey via random experimentation, we have a deterministic way of checking this
  - User was acquired for Wealth Analyser, in that case the acquisition intent for that user would be 'Wealth Analyser'
*/
func (s *WealthAnalyserOnboardingStatusCheck) wealthAnalyserAcqChannel(ctx context.Context, onb *onbPb.OnboardingDetails) (events.WealthAnalyserAcquisitionChannel, error) {
	if onb.GetStageMetadata().GetIntentSelectionMetadata().GetSelection() == onbPb.OnboardingIntent_ONBOARDING_INTENT_WEALTH_ANALYSER {
		return events.OrganicUserSelectedHardIntent, nil
	}
	if onb.GetFeature() == onbPb.Feature_FEATURE_WEALTH_ANALYSER && onb.GetFiLiteDetails().GetFiLiteSource() == onbPb.FiLiteSource_FI_LITE_SOURCE_DROPPED_OFF_USER_TO_WEALTH_ANALYSER {
		return events.ManuallyMovedToWealthAnalyser, nil
	}
	if onb.GetFeature() == onbPb.Feature_FEATURE_WEALTH_ANALYSER && onb.GetFiLiteDetails().GetFiLiteSource() == onbPb.FiLiteSource_FI_LITE_SOURCE_INTENT_SELECTION {
		return events.IntentSelectionDropOff, nil
	}
	if onb.GetFeature() == onbPb.Feature_FEATURE_WEALTH_ANALYSER && onb.GetFiLiteDetails().GetFiLiteSource() == onbPb.FiLiteSource_FI_LITE_SOURCE_PAN_DOB {
		return events.PanDOBDropOff, nil
	}
	acIntent, _, err := getIntentAndChannelFromAcquisitionInfo(ctx, s.userConf.Onboarding(), s.userClient, onb.GetActorId())
	if err != nil {
		return events.Unknown, errors.Wrap(err, "error in getIntentAndChannelFromAcquisitionInfo in WealthAnalyserOnboardingStatusCheck")
	}
	if acIntent == userPb.AcquisitionIntent_ACQUISITION_INTENT_WEALTH_ANALYSER {
		return events.AcquiredViaPerfMarketing, nil
	}
	return events.Unknown, nil
}

func (s *WealthAnalyserOnboardingStatusCheck) updateFeature(ctx context.Context, onb *onbPb.OnboardingDetails, feature onbPb.Feature) error {
	onb.Feature = feature
	if err := s.onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_FEATURE,
	}, onb); err != nil {
		logger.Error(ctx, "error in update onb details by columns", zap.Error(err))
		return err
	}
	return nil
}

func (s *WealthAnalyserOnboardingStatusCheck) checkMfHoldingsImportStatus(ctx context.Context, actorId string) (*deeplinkPb.Deeplink, error) {
	resp, err := s.mfExternalOrdersClient.GetHoldingsImportStatus(ctx, &mfPb.GetHoldingsImportStatusRequest{
		Identifier: &mfPb.GetHoldingsImportStatusRequest_ActorId{ActorId: actorId},
	})
	mfHoldingsImportDl := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_MF_HOLDINGS_IMPORT_INITIATE_SCREEN_V2,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&mfScreenOptions.MFHoldingsImportInitiateScreenOptions{
			ExitDeeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&onbScreenOptions.GetNextOnbActionScreenOptions{
					Feature: onbPb.Feature_FEATURE_WEALTH_ANALYSER.String(),
				}),
			},
			Provenance: mfPb.Provenance_PROVENANCE_WEALTH_BUILDER_ONBOARDING.String(),
		}),
	}
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return mfHoldingsImportDl, nil
		}
		logger.Error(ctx, "error when fetching mf import request status", zap.Error(err))
		return nil, err
	}
	if !isMFImportHolidingImportAttempted(resp.GetHoldingsImportState()) {
		return mfHoldingsImportDl, nil
	}
	return nil, nil
}

func isMFImportHolidingImportAttempted(importState mfPb.State) bool {
	return importState == mfPb.State_FAILED || importState == mfPb.State_OTP_VERIFICATION_SUCCESSFUL || importState == mfPb.State_EXTERNAL_ORDERS_REFRESH_SUCCESSFUL
}
