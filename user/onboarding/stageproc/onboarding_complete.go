package stageproc

import (
	"context"
	"time"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	bcPb "github.com/epifi/gamma/api/bankcust"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/onboarding/dao"
	wireTypes "github.com/epifi/gamma/user/wire/types"
)

type OnboardingCompleteStage struct {
	onbConf                  *genconf.OnboardingConfig
	dao                      dao.OnboardingDao
	onboardingStagePublisher wireTypes.OnboardingStageEventPublisher
	timeClient               datetime.Time
	bcClient                 bcPb.BankCustomerServiceClient
}

func NewOnboardingCompleteStage(onbConf *genconf.OnboardingConfig, onboardingDao dao.OnboardingDao,
	onboardingStagePublisher wireTypes.OnboardingStageEventPublisher, timeClient datetime.Time, bcClient bcPb.BankCustomerServiceClient) *OnboardingCompleteStage {
	return &OnboardingCompleteStage{
		onbConf:                  onbConf,
		dao:                      onboardingDao,
		onboardingStagePublisher: onboardingStagePublisher,
		timeClient:               timeClient,
		bcClient:                 bcClient,
	}
}

func (m *OnboardingCompleteStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	onb := req.Onb
	onb.CompletedAt = timestamppb.New(m.timeClient.Now())

	if err := m.dao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_COMPLETED_AT,
	}, onb); err != nil {
		logger.Error(ctx, "failed to update onboarding completion timestamp ", zap.Error(err), logOnb(onb.OnboardingId))
		return nil, err
	}

	goroutine.Run(ctx, 1*time.Minute, func(ctx context.Context) {
		_ = m.publishOnboardingStagePacket(ctx, onb)
	})

	_, _ = updateStatus(ctx, m.dao, onb.GetOnboardingId(), onbPb.OnboardingStage_ONBOARDING_COMPLETE, onbPb.OnboardingState_SUCCESS)

	if isWalkthroughScreenEnabledForUser(ctx, m.onbConf.WalkthroughScreenConfig(), onb.GetFeature()) {
		return &StageProcessorResponse{
			NextAction: deeplink.GetWalkthroughScreenDeeplink(),
		}, nil
	}

	return &StageProcessorResponse{
		NextAction: redirectionActionAfterOnbComplete(onb.GetFeatureDetails().GetFeatureInfo()[onb.GetFeature().String()].GetFeatureOnboardingEntryPoint()),
	}, nil
}

func (m *OnboardingCompleteStage) publishOnboardingStagePacket(ctx context.Context, onb *onbPb.OnboardingDetails) error {
	getBCResp, errBC := m.bcClient.GetBankCustomer(ctx, &bcPb.GetBankCustomerRequest{
		Identifier: &bcPb.GetBankCustomerRequest_ActorId{
			ActorId: onb.GetActorId(),
		},
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
	})
	if grpcErr := epifigrpc.RPCError(getBCResp, errBC); grpcErr != nil {
		logger.Error(ctx, "failed to get bank customer", zap.Error(grpcErr))
		return grpcErr
	}

	if _, err := m.onboardingStagePublisher.Publish(ctx, &onbPb.OnboardingStageUpdate{
		ActorId:          onb.GetActorId(),
		Stage:            onbPb.OnboardingStage_ONBOARDING_COMPLETE,
		State:            onbPb.OnboardingStageUpdate_SUCCESS,
		StageCompletedAt: onb.CompletedAt,
		KycLevel:         getBCResp.GetBankCustomer().GetKycInfo().GetKycLevel(),
	}); err != nil {
		logger.Error(ctx, "error in publish onboarding stage packet", zap.Error(err))
		return err
	}
	return nil
}

// isWalkthroughScreenEnabledForUser checks if the walkthrough screen is enabled for the user.
// It checks if the feature is FEATURE_SA and evaluates the feature config.
func isWalkthroughScreenEnabledForUser(ctx context.Context, conf *genconf.WalkthroughScreenConfig, feature onbPb.Feature) bool {
	return feature == onbPb.Feature_FEATURE_SA &&
		apputils.IsFeatureEnabledFromCtxDynamic(ctx, conf.FeatureConfig())
}
