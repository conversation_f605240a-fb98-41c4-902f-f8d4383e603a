package stageproc

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

type PanUniquenessCheckStage struct {
	userConf      *genconf.Config
	UserClient    userPb.UsersClient
	onboardingDao dao.OnboardingDao
	userProc      helper.UserProcessor
	eventLogger   userEvents.EventLogger
}

func NewPanUniquenessCheckStage(userConf *genconf.Config, userClient userPb.UsersClient, onboardingDao dao.OnboardingDao,
	userProc helper.UserProcessor, eventLogger userEvents.EventLogger) *PanUniquenessCheckStage {
	return &PanUniquenessCheckStage{
		userConf:      userConf,
		UserClient:    userClient,
		onboardingDao: onboardingDao,
		userProc:      userProc,
		eventLogger:   eventLogger,
	}
}

// This checks if the pan entered by the user(u1) is associated with any other user(u2)
// In case any u2 is found and u2's device registration stage is processed, u1 is blocked
// In case any u2 is not found OR u2 is found but device registration isn't processed, u1 will be allowed to go ahead
func (s *PanUniquenessCheckStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		response = &StageProcessorResponse{}
		stage    = onbPb.OnboardingStage_PAN_UNIQUENESS_CHECK
	)
	onb := req.Onb
	if getStageStatus(onb, stage) == onbPb.OnboardingState_MANUAL_INTERVENTION {
		response.NextAction = deeplink.NewErrorFullScreen(ctx, error2.PANUniquenessError)
		return response, nil
	}
	// get current user's id
	aUser, err := s.userProc.GetUserByUserId(ctx, onb.GetUserId())
	if err != nil {
		return response, err
	}
	// allowing current user(aUser) and marking the stage success in simulated env
	if isSimulatedEnv(s.userConf) &&
		!strings.HasPrefix(strings.ToUpper(aUser.GetProfile().GetPAN()), "UNQPP") {
		logger.Info(ctx, fmt.Sprintf("bypassing unique pan check for %v", aUser.GetProfile().GetPAN()))
		return response, NoActionError
	}

	// get all users with pan provided by aUser
	userIdentifiers := []*userPb.GetUsersRequest_GetUsersIdentifier{
		{
			Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Pan{
				Pan: aUser.GetProfile().GetPAN(),
			},
		},
	}
	getUsersResp, err := s.UserClient.GetUsers(ctx, &userPb.GetUsersRequest{
		Identifier: userIdentifiers,
	})
	if err = epifigrpc.RPCError(getUsersResp, err); err != nil {
		logger.Error(ctx, "error in getting users by pan", zap.String(logger.ACTOR_ID_V2, onb.GetActorId()), zap.Error(err))
		return nil, fmt.Errorf("error in get user: %w", err)
	}
	usersByPan := getUsersResp.GetUsers()
	logger.Debug(ctx, fmt.Sprintf("len of user by pan %v", len(usersByPan)))
	// fetch onboarding details for all usersByPan
	for _, user := range usersByPan {
		// skip for current user(aUser)
		if user.GetId() == aUser.GetId() {
			continue
		}
		onbResp, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, user.GetActorId())
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				continue
			}
			logger.Error(ctx, "failed to get on-boarding details", zap.String(logger.USER_ID, user.GetId()), zap.Error(err))
			return nil, err
		}
		// aUser cannot continue if device registration is already processed for some other user with provided pan
		if getStageStatus(onbResp, onbPb.OnboardingStage_DEVICE_REGISTRATION).IsSuccessOrSkipped() {
			if _, err = updateStatus(ctx, s.onboardingDao, onb.GetOnboardingId(), stage, onbPb.OnboardingState_MANUAL_INTERVENTION); err != nil {
				return nil, err
			}
			logger.Info(ctx, "user is blocked, pan already used", zap.String("panUsedByUserId", user.GetId()),
				zap.String("blockedUserId", aUser.GetId()))
			response.NextAction = deeplink.NewErrorFullScreen(ctx, error2.PANUniquenessError)
			s.eventLogger.LogOnboardingRiskBlocking(ctx, onb.GetActorId(), true, userEvents.BlockReasonTerminal)
			return response, nil
		}
	}
	// allowing current user(aUser) and mark the stage success
	s.eventLogger.LogOnboardingRiskBlocking(ctx, onb.GetActorId(), false, "")
	return response, NoActionError
}

func isSimulatedEnv(userCfg *genconf.Config) bool {
	if userCfg == nil || userCfg.Application() == nil {
		return false
	}
	return cfg.IsSimulatedEnv(userCfg.Application().Environment)
}
