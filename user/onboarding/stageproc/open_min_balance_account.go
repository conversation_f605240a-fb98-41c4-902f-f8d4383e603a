package stageproc

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/samber/lo"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/typesv2"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	usersPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/pkg/feature/release"

	consentPb "github.com/epifi/gamma/api/consent"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/consent"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/deeplink"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

type OpenMinBalanceAccountStage struct {
	consentClient    consentPb.ConsentClient
	tieringClient    tiering.TieringClient
	usersClient      usersPb.UsersClient
	userGrpClient    userGroupPb.GroupClient
	releaseEvaluator release.IEvaluator
}

func NewOpenMinBalanceAccountStage(consentClient consentPb.ConsentClient, tieringClient tiering.TieringClient, usersClient usersPb.UsersClient, userGrpClient userGroupPb.GroupClient, releaseEvaluator release.IEvaluator) *OpenMinBalanceAccountStage {
	return &OpenMinBalanceAccountStage{
		consentClient:    consentClient,
		tieringClient:    tieringClient,
		usersClient:      usersClient,
		userGrpClient:    userGrpClient,
		releaseEvaluator: releaseEvaluator,
	}
}

func (m *OpenMinBalanceAccountStage) shouldSkipStageForActor(ctx context.Context, actorId, userId string, configParamsResp *tiering.GetConfigParamsResponse) (shouldSkip bool, reasonToSkip string, err error) {
	// disable regular tier for B2B users
	resp, errResp := m.usersClient.GetB2BSalaryProgramVerificationStatus(ctx, &usersPb.GetB2BSalaryProgramVerificationStatusRequest{
		Identifier: &usersPb.GetB2BSalaryProgramVerificationStatusRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err := epifigrpc.RPCError(resp, errResp); err != nil {
		return false, "", fmt.Errorf("error in fetching B2B verification status: %w", err)
	}
	if resp.GetIsVerified() {
		return true, "skipping for b2b users", nil
	}

	// skip for INTERNAL users
	userGroups, err := getUserGroupsByIdentifier(ctx, userId, m.userGrpClient, m.usersClient)
	if err != nil {
		return false, "", fmt.Errorf("error in fetching user groups: %w", err)
	}
	if lo.Contains(userGroups, commontypes.UserGroup_INTERNAL) {
		return true, "skipping for internal users", nil
	}

	if !configParamsResp.GetIsRegularTierEnabledForActor() {
		return true, "regular tier not enabled via release evaluator", nil
	}

	return false, "", nil
}

func (m *OpenMinBalanceAccountStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		onb     = req.GetOnb()
		userId  = onb.GetUserId()
		actorId = onb.GetActorId()
		dl      *deeplinkPb.Deeplink
	)

	tieringConfigParamsResp, configParamsErr := m.tieringClient.GetConfigParams(ctx, &tiering.GetConfigParamsRequest{
		ActorId: actorId,
	})
	if err := epifigrpc.RPCError(tieringConfigParamsResp, configParamsErr); err != nil {
		return nil, fmt.Errorf("error while fetching config params: %w", err)
	}

	shouldSkip, reasonToSkip, err := m.shouldSkipStageForActor(ctx, actorId, userId, tieringConfigParamsResp)
	if err != nil {
		logger.Error(ctx, "error in shouldSkipStageForActor", zap.Error(err))
		return nil, fmt.Errorf("error in determining if stage should be skipped: %w", err)
	}
	if shouldSkip {
		logger.Info(ctx, "skipping OpenMinBalanceAccountStage", zap.String(logger.REASON, reasonToSkip))
		return nil, SkipStageError
	}

	fetchConsentResp, fetchConsentErr := m.consentClient.FetchConsent(ctx, &consentPb.FetchConsentRequest{
		ConsentType: consentPb.ConsentType_CONSENT_OPEN_MIN_BALANCE_SAVINGS_ACCOUNT,
		ActorId:     actorId,
		Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if rpcErr := epifigrpc.RPCError(fetchConsentResp, fetchConsentErr); rpcErr != nil && !fetchConsentResp.GetStatus().IsRecordNotFound() {
		return nil, fmt.Errorf("failed to fetch consent: %w", rpcErr)
	}

	// we create consent and set the flag in ProcessUserAck RPC - after user has performed the swipe action.
	// 	user/onboarding/process_user_ack.go
	//
	// marking the stage as success if both Consent is created and userAllowedToOpenAmbAccount flag is set.
	if fetchConsentResp.GetStatus().IsSuccess() &&
		onb.GetStageMetadata().GetUserAllowedToOpenAmbAccount() {
		return nil, NoActionError
	}

	logger.Info(ctx, "actor eligible for opening min balance account", zap.String(logger.ACTOR_ID_V2, actorId))

	// Check if the SDUI version should be shown
	enableVersion := m.isNewSduiSectioFeatureEnabledForMinBalanceAccountScreen(ctx, actorId)
	if enableVersion {
		logger.Info(ctx, "using SDUI version for min balance account screen", zap.String(logger.ACTOR_ID_V2, actorId))
		dl = constructOpenMinBalanceAccountScreenV1(tieringConfigParamsResp, actorId)
	} else {
		logger.Info(ctx, "using Old version for min balance account screen", zap.String(logger.ACTOR_ID_V2, actorId))
		dl = constructOpenMinBalanceAccountScreen(tieringConfigParamsResp, actorId)
	}
	return &StageProcessorResponse{
		NextAction: dl,
	}, nil
}

func constructOpenMinBalanceAccountScreen(tieringConfigParamsResp *tiering.GetConfigParamsResponse, actorId string) *deeplinkPb.Deeplink {
	minBalance := money.ToDisplayStringWithPrecision(tieringConfigParamsResp.GetRegularTierConfigParams().GetMinBalanceForRegularTier(), 0)

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_DETAILED_ERROR_VIEW_SCREEN,
		ScreenOptions: &dlPb.Deeplink_DetailedErrorViewScreenOptions{
			DetailedErrorViewScreenOptions: &dlPb.DetailedErrorViewScreenOptions{
				Image: commontypes.GetVisualElementFromUrlHeightAndWidth(
					"https://epifi-icons.pointz.in/bank-icon.png", 288, 288,
				),
				TitleText: commontypes.GetTextFromStringFontColourFontStyle(
					fmt.Sprintf("Maintain minimum balance for \na seamless experience"),
					"#333333",
					commontypes.FontStyle_HEADLINE_L,
				),
				SubtitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle(
					fmt.Sprintf("Keep an average monthly balance of <b>%s</b> to avoid penalty.", minBalance),
					"#333333",
					commontypes.FontStyle_BODY_S,
				),
				SwipeCta: &deeplinkPb.Cta{
					Type: dlPb.Cta_CUSTOM,
					Text: "I understand",
					Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(dlPb.Screen_PROCESS_USER_ACK_API, &consent.ProcessUserAckApiScreenOptions{
						AckId:   actorId,
						AckType: consent.AckType_ACK_TYPE_ONB_OPEN_MIN_BALANCE_ACCOUNT.String(),
					}),
					DisplayTheme: dlPb.Cta_PRIMARY,
				},
				ScreenIdentifier: onboarding.OnboardingStage_OPEN_MIN_BALANCE_ACCOUNT.String(),
				HeaderBar: &deeplinkPb.HeaderBar{
					CenterLogo: deeplink.FederalLogo,
				},
				EventProperties: map[string]string{
					onboarding.OnboardingStage_OPEN_MIN_BALANCE_ACCOUNT.String(): "opted to open min balance account",
				},
			},
		},
	}
}

func constructOpenMinBalanceAccountScreenV1(resp *tiering.GetConfigParamsResponse, id string) *dlPb.Deeplink {
	minBalance := money.ToDisplayStringWithPrecision(resp.GetRegularTierConfigParams().GetMinBalanceForRegularTier(), 0)

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_DETAILED_ERROR_VIEW_SCREEN,
		ScreenOptions: &dlPb.Deeplink_DetailedErrorViewScreenOptions{
			DetailedErrorViewScreenOptions: &dlPb.DetailedErrorViewScreenOptions{
				Image: commontypes.GetVisualElementFromUrlHeightAndWidth(
					"https://epifi-icons.pointz.in/bank-icon.png", 288, 288,
				),
				TitleText: commontypes.GetTextFromStringFontColourFontStyle(
					fmt.Sprintf("Maintain minimum balance for \na seamless experience"),
					"#333333",
					commontypes.FontStyle_HEADLINE_L,
				),
				SubtitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle(
					fmt.Sprintf("Keep an average monthly balance of <b>%s</b> to avoid penalty.", minBalance),
					"#333333",
					commontypes.FontStyle_BODY_S,
				),
				SwipeCta: &dlPb.Cta{
					Type: dlPb.Cta_CUSTOM,
					Text: "I understand",
					Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(dlPb.Screen_PROCESS_USER_ACK_API, &consent.ProcessUserAckApiScreenOptions{
						AckId:   id,
						AckType: consent.AckType_ACK_TYPE_ONB_OPEN_MIN_BALANCE_ACCOUNT.String(),
					}),
					DisplayTheme: dlPb.Cta_PRIMARY,
				},
				ScreenIdentifier: onboarding.OnboardingStage_OPEN_MIN_BALANCE_ACCOUNT.String(),
				HeaderBar: &dlPb.HeaderBar{
					CenterLogo: deeplink.FederalLogo,
				},
				EventProperties: map[string]string{
					onboarding.OnboardingStage_OPEN_MIN_BALANCE_ACCOUNT.String(): "opted to open min balance account",
				},
				Sections: &sections.Section{
					Content: &sections.Section_VerticalListSection{
						VerticalListSection: &sections.VerticalListSection{
							IsScrollable: true,
							Components: []*components.Component{
								{
									Content: GetAnyWithoutError(&commontypes.VisualElement{
										Asset: &commontypes.VisualElement_Image_{
											Image: &commontypes.VisualElement_Image{
												Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/amb-new-large-bank.png"},
												Properties: &commontypes.VisualElementProperties{
													Width:  82,
													Height: 56,
												},
												ImageType: commontypes.ImageType_PNG,
											},
										},
									}),
								},
								{
									Content: GetAnyWithoutError(&typesUiPb.IconTextComponent{
										Texts: []*commontypes.Text{
											commontypes.GetTextFromStringFontColourFontStyle("Know what your account offers & \nrequires",
												"#313234", commontypes.FontStyle_HEADLINE_L).WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
										},
										ContainerProperties: &typesUiPb.IconTextComponent_ContainerProperties{
											TopPadding: 24,
										},
									}),
								},
								{
									Content: GetAnyWithoutError(&typesUiPb.IconTextComponent{
										Texts: []*commontypes.Text{
											commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("Maintain an average monthly balance of <b>%s</b> \nto avoid penalty.", minBalance),
												"#6A6D70", commontypes.FontStyle_SUBTITLE_S).WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
										},
										ContainerProperties: &typesUiPb.IconTextComponent_ContainerProperties{
											LeftPadding:   16,
											RightPadding:  16,
											TopPadding:    24,
											BottomPadding: 32,
										},
									}),
								},
								{
									Content: GetAnyWithoutError(&sections.VerticalListSection{
										IsScrollable: false,
										Components: []*components.Component{
											{
												Content: GetAnyWithoutError(&commontypes.VisualElement{
													Asset: &commontypes.VisualElement_Image_{
														Image: &commontypes.VisualElement_Image{
															Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/amb-13march.png"},
															Properties: &commontypes.VisualElementProperties{
																Width:  366,
																Height: 248,
															},
															ImageType: commontypes.ImageType_PNG,
														},
													},
												}),
											},
										},
										HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
										VisualProperties: []*properties.VisualProperty{
											{
												Properties: &properties.VisualProperty_ContainerProperty{
													ContainerProperty: &properties.ContainerProperty{
														Size: &properties.Size{
															Width: &properties.Size_Dimension{
																Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
															},
															Height: &properties.Size_Dimension{
																Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
															},
														},
														Padding: &properties.PaddingProperty{
															Top:   10,
															Left:  20,
															Right: 16,
														},
													},
												},
											},
										},
									}),
								},
							},
							VisualProperties: []*properties.VisualProperty{
								{
									Properties: &properties.VisualProperty_ContainerProperty{
										ContainerProperty: &properties.ContainerProperty{
											Size: &properties.Size{
												Width: &properties.Size_Dimension{
													Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
												},
												Height: &properties.Size_Dimension{
													Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
												},
											},
											Padding: &properties.PaddingProperty{
												Top:   24,
												Left:  22,
												Right: 24,
											},
										},
									},
								},
							},
							VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
							HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
						},
					},
				},
			},
		},
	}
}

func (m *OpenMinBalanceAccountStage) isNewSduiSectioFeatureEnabledForMinBalanceAccountScreen(ctx context.Context, actorId string) bool {
	enableVersion, releaseErr := m.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesv2.Feature_FEATURE_MIN_BALANCE_ACCOUNT_SCREEN_SDUI).WithActorId(actorId))
	if releaseErr != nil {
		logger.Error(ctx, "error in evaluating FEATURE_MIN_BALANCE_ACCOUNT_SCREEN_SDUI", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(releaseErr))
		return false
	}
	return enableVersion
}
