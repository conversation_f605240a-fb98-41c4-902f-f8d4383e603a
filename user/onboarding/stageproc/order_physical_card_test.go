package stageproc

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	inAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	inAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"
	mockInAppRef "github.com/epifi/gamma/api/inappreferral/mocks"
	mockVkyc "github.com/epifi/gamma/api/kyc/vkyc/mocks"
	mockUserGroup "github.com/epifi/gamma/api/user/group/mocks"
	mocks2 "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"

	rpcPb "github.com/epifi/be-common/api/rpc"

	cpPb "github.com/epifi/gamma/api/card/provisioning"
	cardMocks "github.com/epifi/gamma/api/card/provisioning/mocks"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	mockRelease "github.com/epifi/gamma/pkg/feature/release/mocks"
)

func TestOrderPhysicalCardStage_StageProcessor(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	actorId1 := "actor-id-1"
	userId := "user-id-1"
	// email := "<EMAIL>"
	cardId1 := "card-id-1"
	onbId1 := "onb-id-1"
	physicalCardOrderScreenDl := &dlPb.Deeplink{Screen: dlPb.Screen_DEBIT_CARD_ORDER_PHYSICAL_CARD_SCREEN}
	newError := errors.New("some error")

	stage := onbPb.OnboardingStage_ORDER_PHYSICAL_CARD

	type args struct {
		ctx context.Context
		req *StageProcessorRequest
	}
	type mockClients struct {
		cardProvisioningClient  *cardMocks.MockCardProvisioningClient
		mockEvaluator           *mockRelease.MockIEvaluator
		vkycClient              *mockVkyc.MockVKYCClient
		mockUsersClient         *mocks2.MockUsersClient
		mockUserGroupClient     *mockUserGroup.MockGroupClient
		mockInAppReferralClient *mockInAppRef.MockInAppReferralClient
	}

	tests := []struct {
		name           string
		args           args
		want           *StageProcessorResponse
		wantErr        error
		setupMockCalls func(md *mockClients)
	}{
		{
			name: "stage is already skipped",
			args: args{
				ctx: ctx,
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:                actorId1,
						CardInfo:               &onbPb.CardInformationInternal{CardDetails: []*onbPb.SingleCardInfo{{CardId: cardId1}}},
						OnboardingId:           onbId1,
						CurrentOnboardingStage: onbPb.OnboardingStage_ORDER_PHYSICAL_CARD,
						StageDetails: &onbPb.StageDetails{
							StageMapping: map[string]*onbPb.StageInfo{
								"ORDER_PHYSICAL_CARD": {
									State: onbPb.OnboardingState_SKIPPED,
								},
							},
						},
					},
				},
			},
			want:    nil,
			wantErr: SkipStageError,
		},
		{
			name: "error in GetPhysicalCardDispatchStatus",
			args: args{
				ctx: ctx,
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:                actorId1,
						CardInfo:               &onbPb.CardInformationInternal{CardDetails: []*onbPb.SingleCardInfo{{CardId: cardId1}}},
						OnboardingId:           onbId1,
						CurrentOnboardingStage: onbPb.OnboardingStage_ORDER_PHYSICAL_CARD,
					},
				},
			},
			want:    nil,
			wantErr: newError,
			setupMockCalls: func(md *mockClients) {
				md.cardProvisioningClient.EXPECT().GetPhysicalCardDispatchStatus(gomock.Any(), &cpPb.GetPhysicalCardDispatchStatusRequest{
					CardId: cardId1,
				}).Return(nil, newError)
				md.mockEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
			},
		},
		{
			name: "internal error in GetPhysicalCardDispatchStatus",
			args: args{
				ctx: ctx,
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:                actorId1,
						CardInfo:               &onbPb.CardInformationInternal{CardDetails: []*onbPb.SingleCardInfo{{CardId: cardId1}}},
						OnboardingId:           onbId1,
						CurrentOnboardingStage: onbPb.OnboardingStage_ORDER_PHYSICAL_CARD,
					},
				},
			},
			want:    nil,
			wantErr: rpcPb.StatusAsError(rpcPb.StatusInternal()),
			setupMockCalls: func(md *mockClients) {
				md.cardProvisioningClient.EXPECT().GetPhysicalCardDispatchStatus(gomock.Any(), &cpPb.GetPhysicalCardDispatchStatusRequest{
					CardId: cardId1,
				}).Return(&cpPb.GetPhysicalCardDispatchStatusResponse{Status: rpcPb.StatusInternal()}, nil)
				md.mockEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
			},
		},
		{
			name: "physical dispatch already moved to success",
			args: args{
				ctx: ctx,
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:                actorId1,
						CardInfo:               &onbPb.CardInformationInternal{CardDetails: []*onbPb.SingleCardInfo{{CardId: cardId1}}},
						OnboardingId:           onbId1,
						CurrentOnboardingStage: onbPb.OnboardingStage_ORDER_PHYSICAL_CARD,
					},
				},
			},
			want:    nil,
			wantErr: NoActionError,
			setupMockCalls: func(md *mockClients) {
				md.cardProvisioningClient.EXPECT().GetPhysicalCardDispatchStatus(gomock.Any(), &cpPb.GetPhysicalCardDispatchStatusRequest{
					CardId: cardId1,
				}).Return(&cpPb.GetPhysicalCardDispatchStatusResponse{Status: rpcPb.StatusOk()}, nil)
				md.mockEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
			},
		},
		{
			name: "physical dispatch in progress",
			args: args{
				ctx: ctx,
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:                actorId1,
						CardInfo:               &onbPb.CardInformationInternal{CardDetails: []*onbPb.SingleCardInfo{{CardId: cardId1}}},
						OnboardingId:           onbId1,
						CurrentOnboardingStage: onbPb.OnboardingStage_ORDER_PHYSICAL_CARD,
					},
				},
			},
			want:    nil,
			wantErr: NoActionError,
			setupMockCalls: func(md *mockClients) {
				md.cardProvisioningClient.EXPECT().GetPhysicalCardDispatchStatus(gomock.Any(), &cpPb.GetPhysicalCardDispatchStatusRequest{
					CardId: cardId1,
				}).Return(&cpPb.GetPhysicalCardDispatchStatusResponse{
					Status: rpcPb.NewStatus(uint32(cpPb.GetPhysicalCardDispatchStatusResponse_IN_PROGRESS), "", ""),
				}, nil)
				md.mockEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
			},
		},
		{
			name: "vkyc rejected",
			args: args{
				ctx: ctx,
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:                actorId1,
						CardInfo:               &onbPb.CardInformationInternal{CardDetails: []*onbPb.SingleCardInfo{{CardId: cardId1}}},
						OnboardingId:           onbId1,
						CurrentOnboardingStage: onbPb.OnboardingStage_ORDER_PHYSICAL_CARD,
					},
				},
			},
			want:    nil,
			wantErr: SkipStageError,
			setupMockCalls: func(md *mockClients) {
				md.cardProvisioningClient.EXPECT().GetPhysicalCardDispatchStatus(gomock.Any(), &cpPb.GetPhysicalCardDispatchStatusRequest{
					CardId: cardId1,
				}).Return(&cpPb.GetPhysicalCardDispatchStatusResponse{Status: rpcPb.StatusRecordNotFound()}, nil)
				md.mockEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)

				md.mockInAppReferralClient.EXPECT().GetClaimedFiniteCodeForActor(gomock.Any(), &inAppReferralPb.GetClaimedFiniteCodeForActorRequest{
					ActorId: actorId1,
				}).Return(&inAppReferralPb.GetClaimedFiniteCodeForActorResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil)

				md.cardProvisioningClient.EXPECT().FetchPhysicalCardChargesForUser(gomock.Any(), &cpPb.FetchPhysicalCardChargesForUserRequest{
					ActorId:               actorId1,
					PostSuccessNextAction: deeplink.NewActionToGetNextAction(),
					OnSkipNextAction:      deeplink.NextActionToSkipOnbStageNextAction(stage),
				}).Return(&cpPb.FetchPhysicalCardChargesForUserResponse{
					Status: rpcPb.NewStatus(uint32(cpPb.FetchPhysicalCardChargesForUserResponse_VKYC_REJECTED), "", ""),
				}, nil)
			},
		},
		{
			name: "error in FetchPhysicalCardChargesForUser",
			args: args{
				ctx: ctx,
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:                actorId1,
						CardInfo:               &onbPb.CardInformationInternal{CardDetails: []*onbPb.SingleCardInfo{{CardId: cardId1}}},
						OnboardingId:           onbId1,
						CurrentOnboardingStage: onbPb.OnboardingStage_ORDER_PHYSICAL_CARD,
					},
				},
			},
			want:    nil,
			wantErr: newError,
			setupMockCalls: func(md *mockClients) {
				md.cardProvisioningClient.EXPECT().GetPhysicalCardDispatchStatus(gomock.Any(), &cpPb.GetPhysicalCardDispatchStatusRequest{
					CardId: cardId1,
				}).Return(&cpPb.GetPhysicalCardDispatchStatusResponse{Status: rpcPb.StatusRecordNotFound()}, nil)

				md.mockInAppReferralClient.EXPECT().GetClaimedFiniteCodeForActor(gomock.Any(), &inAppReferralPb.GetClaimedFiniteCodeForActorRequest{
					ActorId: actorId1,
				}).Return(&inAppReferralPb.GetClaimedFiniteCodeForActorResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil)

				md.cardProvisioningClient.EXPECT().FetchPhysicalCardChargesForUser(gomock.Any(), &cpPb.FetchPhysicalCardChargesForUserRequest{
					ActorId:               actorId1,
					PostSuccessNextAction: deeplink.NewActionToGetNextAction(),
					OnSkipNextAction:      deeplink.NextActionToSkipOnbStageNextAction(stage),
				}).Return(nil, newError)
				md.mockEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
			},
		},
		{
			name: "should return error when GetClaimedFiniteCodeForActor rpc returns error",
			args: args{
				ctx: ctx,
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:                actorId1,
						UserId:                 userId,
						CardInfo:               &onbPb.CardInformationInternal{CardDetails: []*onbPb.SingleCardInfo{{CardId: cardId1}}},
						OnboardingId:           onbId1,
						CurrentOnboardingStage: onbPb.OnboardingStage_ORDER_PHYSICAL_CARD,
					},
				},
			},
			want:    nil,
			wantErr: rpcPb.StatusAsError(rpcPb.StatusInternal()),
			setupMockCalls: func(md *mockClients) {
				md.mockEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				md.cardProvisioningClient.EXPECT().GetPhysicalCardDispatchStatus(gomock.Any(), &cpPb.GetPhysicalCardDispatchStatusRequest{
					CardId: cardId1,
				}).Return(&cpPb.GetPhysicalCardDispatchStatusResponse{Status: rpcPb.StatusRecordNotFound()}, nil)

				md.mockInAppReferralClient.EXPECT().GetClaimedFiniteCodeForActor(gomock.Any(), &inAppReferralPb.GetClaimedFiniteCodeForActorRequest{
					ActorId: actorId1,
				}).Return(&inAppReferralPb.GetClaimedFiniteCodeForActorResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
		},
		{
			name: "successful fetch of physical card charges",
			args: args{
				ctx: ctx,
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:                actorId1,
						UserId:                 userId,
						CardInfo:               &onbPb.CardInformationInternal{CardDetails: []*onbPb.SingleCardInfo{{CardId: cardId1}}},
						OnboardingId:           onbId1,
						CurrentOnboardingStage: onbPb.OnboardingStage_ORDER_PHYSICAL_CARD,
					},
				},
			},
			want: &StageProcessorResponse{
				NextAction: physicalCardOrderScreenDl,
			},
			wantErr: nil,
			setupMockCalls: func(md *mockClients) {
				md.cardProvisioningClient.EXPECT().GetPhysicalCardDispatchStatus(gomock.Any(), &cpPb.GetPhysicalCardDispatchStatusRequest{
					CardId: cardId1,
				}).Return(&cpPb.GetPhysicalCardDispatchStatusResponse{Status: rpcPb.StatusRecordNotFound()}, nil)

				md.mockInAppReferralClient.EXPECT().GetClaimedFiniteCodeForActor(gomock.Any(), &inAppReferralPb.GetClaimedFiniteCodeForActorRequest{
					ActorId: actorId1,
				}).Return(&inAppReferralPb.GetClaimedFiniteCodeForActorResponse{
					Status: rpcPb.StatusOk(),
					FiniteCode: &inAppReferralPb.FiniteCode{
						Type:    inAppReferralEnumPb.FiniteCodeType_D2H_REGULAR,
						Channel: inAppReferralEnumPb.FiniteCodeChannel_IN_APP_REFERRAL,
					},
				}, nil)

				// md.mockUsersClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
				// 	Identifier: &user.GetUserRequest_Id{
				// 		Id: userId,
				// 	},
				// }).Return(&user.GetUserResponse{
				// 	User: &user.User{
				// 		Profile: &user.Profile{
				// 			Email: email,
				// 		},
				// 	},
				// 	Status: rpcPb.StatusOk(),
				// }, nil)
				//
				// md.mockUserGroupClient.EXPECT().AddEmailGroupMapping(gomock.Any(), &usergroup.AddEmailGroupMappingRequest{
				// 	UserGroup: commontypes.UserGroup_FREE_PHYSICAL_DC_REWARD,
				// 	Emails:    []string{email},
				// }).Return(&usergroup.AddEmailGroupMappingResponse{
				// 	Status: rpcPb.StatusOk(),
				// }, nil)

				md.cardProvisioningClient.EXPECT().FetchPhysicalCardChargesForUser(gomock.Any(), &cpPb.FetchPhysicalCardChargesForUserRequest{
					ActorId:               actorId1,
					PostSuccessNextAction: deeplink.NewActionToGetNextAction(),
					OnSkipNextAction:      deeplink.NextActionToSkipOnbStageNextAction(stage),
				}).Return(&cpPb.FetchPhysicalCardChargesForUserResponse{
					Status:     rpcPb.StatusOk(),
					NextAction: physicalCardOrderScreenDl,
				}, nil)
				md.mockEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)

			md := &mockClients{
				cardProvisioningClient:  cardMocks.NewMockCardProvisioningClient(ctrl),
				mockEvaluator:           mockRelease.NewMockIEvaluator(ctrl),
				vkycClient:              mockVkyc.NewMockVKYCClient(ctrl),
				mockUsersClient:         mocks2.NewMockUsersClient(ctrl),
				mockUserGroupClient:     mockUserGroup.NewMockGroupClient(ctrl),
				mockInAppReferralClient: mockInAppRef.NewMockInAppReferralClient(ctrl),
			}
			if tt.setupMockCalls != nil {
				tt.setupMockCalls(md)
			}
			got, err := NewOrderPhysicalCardStage(md.cardProvisioningClient, ts.GenConf, md.mockEvaluator, md.vkycClient, md.mockInAppReferralClient, md.mockUsersClient, md.mockUserGroupClient, nil).StageProcessor(tt.args.ctx, tt.args.req)
			if err != nil || tt.wantErr != nil {
				if !errors.Is(err, tt.wantErr) {
					t.Errorf("StageProcessor(got=%v,\n want= %v)", err, tt.wantErr)
				}
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("StageProcessor(got=%v, \n want=%v)", got, tt.want)
			}
		})
	}
}
