package stageproc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/user/onboarding/pkg/constant"

	"context"
	"strconv"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/gamma/api/comms"
	userPrefPb "github.com/epifi/gamma/api/comms/user_preference"
	userPrefMocks "github.com/epifi/gamma/api/comms/user_preference/mocks"
	"github.com/epifi/gamma/api/consent"
	consentMocks "github.com/epifi/gamma/api/consent/mocks"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	mocks3 "github.com/epifi/gamma/api/user/group/mocks"
	mocks2 "github.com/epifi/gamma/api/user/mocks"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
)

func TestTncConsentStage_StageProcessor(t *testing.T) {
	ctx := epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID)
	ctxIOS := context.WithValue(context.WithValue(context.Background(), epificontext.CtxAppVersionCodeKey, "300"), epificontext.CtxAppPlatformKey, header.Platform_IOS.String())
	minimalTncCtx := epificontext.CtxWithAppVersionCode(ctx, strconv.Itoa(100001))

	tncOnbDetails := &onbPb.OnboardingDetails{
		ActorId: "actor-id",
	}
	nriOnbDetails := &onbPb.OnboardingDetails{
		ActorId: "actor-id",
		Feature: onbPb.Feature_FEATURE_NON_RESIDENT_SA,
	}

	dl, analyticsProc := NewPhonePermissionDl(context.Background(), false, true, true, true, nil)
	stageProcResp1 := &StageProcessorResponse{
		NextAction:     dl,
		AnalyticsProps: analyticsProc,
	}

	dl, analyticsProc = NewPhonePermissionDl(ctxIOS, true, false, true, true, nil)
	stageProcResp2 := &StageProcessorResponse{
		NextAction:     dl,
		AnalyticsProps: analyticsProc,
	}

	type mockStruct struct {
		consentClient  *consentMocks.MockConsentClient
		userPrefClient *userPrefMocks.MockUserPreferenceClient
	}
	type args struct {
		req *StageProcessorRequest
		ctx context.Context
	}
	tests := []struct {
		name     string
		args     args
		want     *StageProcessorResponse
		wantErr  error
		mockFunc func(*mockStruct)
	}{
		{
			name: "tnc consent taken, WA positive consent given",
			args: args{
				req: &StageProcessorRequest{
					Onb: tncOnbDetails,
				},
				ctx: context.Background(),
			},
			wantErr: NoActionError,
			mockFunc: func(mocks *mockStruct) {
				mocks.consentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: tncOnbDetails.ActorId,
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FED_TNC,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
						consent.ConsentType_FI_WEALTH_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: false,
				}, nil)
				mocks.userPrefClient.EXPECT().GetPreference(gomock.Any(), &userPrefPb.GetPreferenceRequest{
					ActorId:  tncOnbDetails.ActorId,
					Medium:   comms.Medium_WHATSAPP,
					Category: comms.Category_CATEGORY_PROMOTIONAL,
				}).Return(&userPrefPb.GetPreferenceResponse{
					Preference: userPrefPb.Preference_ON, Status: rpc.StatusOk(),
				}, nil)

				mocks.userPrefClient.EXPECT().GetPreference(gomock.Any(), &userPrefPb.GetPreferenceRequest{
					ActorId:  tncOnbDetails.ActorId,
					Medium:   comms.Medium_WHATSAPP,
					Category: comms.Category_CATEGORY_TRANSACTIONAL,
				}).Return(&userPrefPb.GetPreferenceResponse{
					Preference: userPrefPb.Preference_ON, Status: rpc.StatusOk(),
				}, nil)
			},
		},
		{
			name: "tnc consent taken, WA negative consent given",
			args: args{
				req: &StageProcessorRequest{
					Onb: tncOnbDetails,
				},
				ctx: context.Background(),
			},
			wantErr: NoActionError,
			mockFunc: func(mocks *mockStruct) {
				mocks.consentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: tncOnbDetails.ActorId,
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FED_TNC,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
						consent.ConsentType_FI_WEALTH_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: false,
				}, nil)
				mocks.userPrefClient.EXPECT().GetPreference(gomock.Any(), &userPrefPb.GetPreferenceRequest{
					ActorId:  tncOnbDetails.ActorId,
					Medium:   comms.Medium_WHATSAPP,
					Category: comms.Category_CATEGORY_PROMOTIONAL,
				}).Return(&userPrefPb.GetPreferenceResponse{Preference: userPrefPb.Preference_OFF, Status: rpc.StatusOk()}, nil)

				mocks.userPrefClient.EXPECT().GetPreference(gomock.Any(), &userPrefPb.GetPreferenceRequest{
					ActorId:  tncOnbDetails.ActorId,
					Medium:   comms.Medium_WHATSAPP,
					Category: comms.Category_CATEGORY_TRANSACTIONAL,
				}).Return(&userPrefPb.GetPreferenceResponse{Preference: userPrefPb.Preference_OFF, Status: rpc.StatusOk()}, nil)
			},
		},
		{
			name: "tnc consent taken, WA consent not given",
			args: args{
				req: &StageProcessorRequest{
					Onb: tncOnbDetails,
				},
				ctx: context.Background(),
			},
			want: stageProcResp1,
			mockFunc: func(mocks *mockStruct) {
				mocks.consentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: tncOnbDetails.ActorId,
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FED_TNC,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
						consent.ConsentType_FI_WEALTH_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: false,
				}, nil)
				mocks.userPrefClient.EXPECT().GetPreference(gomock.Any(), &userPrefPb.GetPreferenceRequest{
					ActorId:  tncOnbDetails.ActorId,
					Medium:   comms.Medium_WHATSAPP,
					Category: comms.Category_CATEGORY_PROMOTIONAL,
				}).Return(&userPrefPb.GetPreferenceResponse{Status: rpc.StatusRecordNotFound()}, nil)
			},
		},
		{
			name: "tnc consent taken, WA consent not given (with iOS ATT prompt flag)",
			args: args{
				req: &StageProcessorRequest{
					Onb: tncOnbDetails,
				},
				ctx: ctxIOS,
			},
			want: stageProcResp2,
			mockFunc: func(mocks *mockStruct) {
				mocks.consentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: tncOnbDetails.ActorId,
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: false,
				}, nil)
				mocks.userPrefClient.EXPECT().GetPreference(gomock.Any(), &userPrefPb.GetPreferenceRequest{
					ActorId:  tncOnbDetails.ActorId,
					Medium:   comms.Medium_WHATSAPP,
					Category: comms.Category_CATEGORY_PROMOTIONAL,
				}).Return(&userPrefPb.GetPreferenceResponse{Status: rpc.StatusRecordNotFound()}, nil)
			},
		},
		{
			name: "tnc consent not taken",
			args: args{
				req: &StageProcessorRequest{
					Onb: tncOnbDetails,
				},
				ctx: context.Background(),
			},
			want: &StageProcessorResponse{
				NextAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_CONSENT,
					ScreenOptions: &deeplinkPb.Deeplink_ConsentScreenOptions{
						ConsentScreenOptions: &deeplinkPb.ConsentScreenOptions{
							Title:    constant.TnCTitle,
							Subtitle: constant.TnCSubtitle,
							CheckboxConsent1: &deeplinkPb.ConsentScreenOptions_CheckboxConsent{
								ConsentType:  consent.ConsentType_FI_TNC.String(),
								ConsentTypes: []string{consent.ConsentType_FI_TNC.String()},
								Text:         constant.TnCConsentIndianResidentText,
							},
							CheckboxConsent2: &deeplinkPb.ConsentScreenOptions_CheckboxConsent{
								ConsentType:  consent.ConsentType_FI_PRIVACY_POLICY.String(),
								ConsentTypes: []string{consent.ConsentType_FI_PRIVACY_POLICY.String()},
								Text:         constant.TnCConsentPersonalDataText,
							},
							CheckboxConsent3: &deeplinkPb.ConsentScreenOptions_CheckboxConsent{
								ConsentType:  consent.ConsentType_CREDIT_REPORT_TNC.String(),
								ConsentTypes: []string{consent.ConsentType_CREDIT_REPORT_TNC.String()},
								Text:         constant.TnCConsentExperianText,
							},
							ConsentTypeUrl: []*deeplinkPb.ConsentTypeUrl{
								{
									ConsentUrl:  "https://web.demo.pointz.in/T&C",
									ConsentType: deeplinkPb.ConsentTypeUrl_FI_TNC,
									Name:        "Fi Terms",
								},
							},
						},
					},
				},
			},
			mockFunc: func(mocks *mockStruct) {
				mocks.consentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: tncOnbDetails.ActorId,
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FED_TNC,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
						consent.ConsentType_FI_WEALTH_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: true,
					ConsentTypeUrls: []*consent.ConsentTypeUrl{
						{
							ConsentUrl:  "https://web.demo.pointz.in/T&C",
							ConsentType: consent.ConsentType_FI_TNC,
						},
					},
				}, nil)
			},
		},
		{
			name: "nri tnc consent not taken",
			args: args{
				req: &StageProcessorRequest{
					Onb: nriOnbDetails,
				},
				ctx: context.Background(),
			},
			want: &StageProcessorResponse{
				NextAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_CONSENT,
					ScreenOptions: &deeplinkPb.Deeplink_ConsentScreenOptions{
						ConsentScreenOptions: &deeplinkPb.ConsentScreenOptions{
							Title:    constant.TnCTitle,
							Subtitle: constant.TnCSubtitle,
							CheckboxConsent1: &deeplinkPb.ConsentScreenOptions_CheckboxConsent{
								ConsentTypes: []string{
									consent.ConsentType_CONSENT_NON_RESIDENT_ACCOUNT_TESTING.String(),
								},
								Text: constant.ConsentText1,
							},
							CheckboxConsent2: &deeplinkPb.ConsentScreenOptions_CheckboxConsent{
								ConsentTypes: []string{
									consent.ConsentType_CONSENT_NON_INDIAN_RESIDENCY.String(),
									consent.ConsentType_CONSENT_NOT_POLITICALLY_EXPOSED.String(),
									consent.ConsentType_CONSENT_FATCA_CRS.String(),
								},
								Text: constant.ConsentText2,
							},
							CheckboxConsent3: &deeplinkPb.ConsentScreenOptions_CheckboxConsent{
								ConsentTypes: []string{
									consent.ConsentType_CONSENT_FI_NRE_NRO_ACCOUNTS.String(),
									consent.ConsentType_FI_PRIVACY_POLICY.String(),
								},
								Text: constant.ConsentText3,
							},
							ShowMinimalScreen: true,
							ConsentTypeUrl: []*deeplinkPb.ConsentTypeUrl{
								{
									ConsentUrl:  "https://web.demo.pointz.in/T&C",
									ConsentType: deeplinkPb.ConsentTypeUrl_FI_TNC,
									Name:        "Fi Terms",
								},
							},
							EnableNfcCheck: true,
						},
					},
				},
			},
			mockFunc: func(mocks *mockStruct) {
				mocks.consentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: nriOnbDetails.GetActorId(),
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_CONSENT_FI_NRE_NRO_ACCOUNTS,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_CONSENT_NOT_POLITICALLY_EXPOSED,
						consent.ConsentType_CONSENT_NON_INDIAN_RESIDENCY,
						consent.ConsentType_CONSENT_NON_RESIDENT_ACCOUNT_TESTING,
						consent.ConsentType_CONSENT_FATCA_CRS,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: true,
					ConsentTypeUrls: []*consent.ConsentTypeUrl{
						{
							ConsentUrl:  "https://web.demo.pointz.in/T&C",
							ConsentType: consent.ConsentType_FI_TNC,
						},
					},
				}, nil)
			},
		},
		{
			name: "collect minimal consent",
			args: args{
				req: &StageProcessorRequest{
					Onb: tncOnbDetails,
				},
				ctx: minimalTncCtx,
			},
			want: &StageProcessorResponse{
				NextAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_CONSENT,
					ScreenOptions: &deeplinkPb.Deeplink_ConsentScreenOptions{
						ConsentScreenOptions: &deeplinkPb.ConsentScreenOptions{
							Title:    constant.TnCTitle,
							Subtitle: constant.TnCSubtitle,
							CheckboxConsent1: &deeplinkPb.ConsentScreenOptions_CheckboxConsent{
								ConsentType:  consent.ConsentType_FI_TNC.String(),
								ConsentTypes: []string{consent.ConsentType_FI_TNC.String()},
								Text:         constant.TnCConsentIndianResidentText,
							},
							CheckboxConsent2: &deeplinkPb.ConsentScreenOptions_CheckboxConsent{
								ConsentType:  consent.ConsentType_FI_PRIVACY_POLICY.String(),
								ConsentTypes: []string{consent.ConsentType_FI_PRIVACY_POLICY.String()},
								Text:         constant.TnCConsentPersonalDataText,
							},
							CheckboxConsent3: &deeplinkPb.ConsentScreenOptions_CheckboxConsent{
								ConsentType:  consent.ConsentType_CREDIT_REPORT_TNC.String(),
								ConsentTypes: []string{consent.ConsentType_CREDIT_REPORT_TNC.String()},
								Text:         constant.TnCConsentExperianText,
							},
							ConsentTypeUrl: []*deeplinkPb.ConsentTypeUrl{
								{
									ConsentUrl:  "https://web.demo.pointz.in/T&C",
									ConsentType: deeplinkPb.ConsentTypeUrl_FI_TNC,
									Name:        "Fi Terms",
								},
								{
									ConsentUrl:  "https://web.demo.pointz.in/T&C",
									ConsentType: deeplinkPb.ConsentTypeUrl_FI_PRIVACY_POLICY,
									Name:        "Fi Secure Privacy Policy",
								},
							},
							ShowMinimalScreen: true,
						},
					},
				},
			},
			mockFunc: func(mocks *mockStruct) {
				mocks.consentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: tncOnbDetails.ActorId,
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: true,
					ConsentTypeUrls: []*consent.ConsentTypeUrl{
						{
							ConsentUrl:  "https://web.demo.pointz.in/T&C",
							ConsentType: consent.ConsentType_FI_TNC,
						},
						{
							ConsentUrl:  "https://web.demo.pointz.in/T&C",
							ConsentType: consent.ConsentType_FI_PRIVACY_POLICY,
						},
					},
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			consentClient := consentMocks.NewMockConsentClient(ctrl)
			userPrefClient := userPrefMocks.NewMockUserPreferenceClient(ctrl)
			actorClient := mocks.NewMockActorClient(ctrl)
			userClient := mocks2.NewMockUsersClient(ctrl)
			userGroupClient := mocks3.NewMockGroupClient(ctrl)

			if tt.mockFunc != nil {
				tt.mockFunc(&mockStruct{
					consentClient:  consentClient,
					userPrefClient: userPrefClient,
				})
			}
			s := &TncConsentStage{
				consentClient:      consentClient,
				userPrefClient:     userPrefClient,
				conf:               ts.GenConf.Onboarding(),
				abEvaluatorGeneric: getABEvaluatorOfFeature[string](actorClient, userClient, userGroupClient, ts.GenConf.Onboarding().ABFeatureReleaseConfig(), func(str string) string { return str }),
			}
			got, err := s.StageProcessor(tt.args.ctx, tt.args.req)
			if err != nil {
				if err.Error() != tt.wantErr.Error() {
					t.Errorf("TnCConsent() error = %v, wantErr = %v", err, tt.wantErr)
				}
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("StageProcessor value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}
