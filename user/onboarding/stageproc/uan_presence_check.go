package stageproc

import (
	"context"
	"fmt"

	"github.com/samber/lo"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"

	"github.com/epifi/gamma/api/creditreportv2"
	empPb "github.com/epifi/gamma/api/employment"
	scrnrPb "github.com/epifi/gamma/api/screener"
	userIntelPb "github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/onboarding/pkg/screener"
)

type UANPresenceCheckStage struct {
	conf                 *genconf.AppScreeningConfig
	screenerClient       scrnrPb.ScreenerClient
	empClient            empPb.EmploymentClient
	creditReportV2Client creditreportv2.CreditReportManagerClient
	flags                *genconf.OnboardingFlags
	userIntelClient      userIntelPb.UserIntelServiceClient
}

func NewUANPresenceCheckStage(onbConf *genconf.OnboardingConfig, screenerClient scrnrPb.ScreenerClient,
	empClient empPb.EmploymentClient, creditReportV2Client creditreportv2.CreditReportManagerClient, userIntelClient userIntelPb.UserIntelServiceClient) *UANPresenceCheckStage {
	return &UANPresenceCheckStage{
		conf:                 onbConf.AppScreeningConfig(),
		screenerClient:       screenerClient,
		empClient:            empClient,
		creditReportV2Client: creditReportV2Client,
		flags:                onbConf.Flags(),
		userIntelClient:      userIntelClient,
	}
}

func (s *UANPresenceCheckStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		onb          = req.GetOnb()
		actorId      = req.GetOnb().GetActorId()
		currentCheck = scrnrPb.CheckType_CHECK_TYPE_UAN_PRESENCE
		forceCheck   = screener.CanForceScreenerCheck(onb, nil)
	)

	if screener.IsScreenerCompletedOrSkipped(onb) && !forceCheck {
		return nil, SkipStageError
	}

	if canForceSkipUanCheck(ctx, req.GetOnb(), s.userIntelClient) {
		logger.Info(ctx, "force skipping uan stage")
		return nil, SkipStageError
	}

	// skip uan presence check for non-salaried users
	getEmpInfoResp, errGetEmpInfo := s.empClient.GetEmploymentInfo(ctx, &empPb.GetEmploymentInfoRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(getEmpInfoResp, errGetEmpInfo); grpcErr != nil {
		logger.Error(ctx, "failed to get employment info for actor", zap.Error(grpcErr))
		return nil, grpcErr
	}

	if getEmpInfoResp.GetEmploymentData().GetEmploymentType() != empPb.EmploymentType_SALARIED {
		return nil, SkipStageError
	}

	// skip uan presence check for users with credit score less than a particular threshold
	// if credit report is not present for them, also skip this stage
	getCreditReportResp, getCreditReportErr := s.creditReportV2Client.GetCreditReport(ctx, &creditreportv2.GetCreditReportRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(getCreditReportResp, getCreditReportErr); grpcErr != nil && !getCreditReportResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "failed to get credit report for actor", zap.Error(grpcErr))
		return nil, grpcErr
	}

	if getCreditReportResp.GetStatus().IsRecordNotFound() ||
		getCreditReportResp.GetCreditReport().GetCreditReportData().GetCreditScore() < s.conf.UANCheckConfig().CreditScoreThreshold() {
		logger.Info(ctx, fmt.Sprintf("users credit score is below %d, skipping check", s.conf.UANCheckConfig().CreditScoreThreshold()))
		return nil, SkipStageError
	}

	getAttemptRes, errGet := s.screenerClient.GetScreenerAttemptsByActorId(ctx, &scrnrPb.GetScreenerAttemptsByActorIdRequest{
		ActorId: onb.GetActorId(),
	})
	if grpcErr := epifigrpc.RPCError(getAttemptRes, errGet); grpcErr != nil && !rpc.StatusFromError(grpcErr).IsRecordNotFound() {
		logger.Error(ctx, "failed to get screener attempts by actor id", zap.Error(grpcErr))
		return nil, grpcErr
	}

	// If no screener attempt is present, start the check to create an attempt
	if getAttemptRes.GetStatus().IsRecordNotFound() {
		return runScreenerV2Check(ctx, s.screenerClient, actorId, currentCheck)
	}

	scrAtt := getAttemptRes.GetScreenerAttempt()
	uanCheckDetails, ok := lo.Find[*scrnrPb.CheckDetails](getAttemptRes.GetChecksMap(), func(check *scrnrPb.CheckDetails) bool {
		return check.GetCheckType() == currentCheck
	})
	if !ok {
		logger.Error(ctx, fmt.Sprintf("did not find current check in screener attempt : %v", currentCheck))
		return nil, fmt.Errorf("did not find current check in screener attempt")
	}

	if !forceCheck {
		if scrAtt.GetResultInfo().GetResult() == scrnrPb.ScreenerAttemptResult_SCREENER_ATTEMPT_RESULT_PASSED ||
			scrAtt.GetResultInfo().GetResult() == scrnrPb.ScreenerAttemptResult_SCREENER_ATTEMPT_RESULT_MANUALLY_PASSED {
			// 1. If it got passed due to some other check -> SKIPPED
			// 2. If got passed due to current check -> COMPLETED
			if uanCheckDetails.GetCheckResult() == scrnrPb.CheckResult_CHECK_RESULT_PASSED {
				return nil, NoActionError
			}
			return nil, SkipStageError
		}
	}

	switch uanCheckDetails.GetCheckResult() {
	case scrnrPb.CheckResult_CHECK_RESULT_PASSED:
		return nil, NoActionError
	case scrnrPb.CheckResult_CHECK_RESULT_SKIPPED,
		scrnrPb.CheckResult_CHECK_RESULT_FAILED,
		scrnrPb.CheckResult_CHECK_RESULT_DISABLED:
		return nil, SkipStageError
	case scrnrPb.CheckResult_CHECK_RESULT_UNSCPECIFIED, scrnrPb.CheckResult_CHECK_RESULT_IN_PROGRESS:
		// Need to start a new check or check current check status
		return runScreenerV2Check(ctx, s.screenerClient, actorId, currentCheck)
	}

	logger.Error(ctx, "Flow shouldn't have gone here as all cases are handled above")
	return nil, fmt.Errorf("unexpected code flow")
}
