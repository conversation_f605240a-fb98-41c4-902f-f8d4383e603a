package onboarding

import (
	"context"
	"encoding/base64"
	"errors"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	awsS3 "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	types "github.com/epifi/gamma/api/typesv2"
	vgDocPb "github.com/epifi/gamma/api/vendorgateway/docs"
	"github.com/epifi/gamma/user/onboarding/helper/passport"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	img "github.com/epifi/gamma/pkg/image"
)

func (s *Service) UploadPassport(ctx context.Context, req *onbPb.UploadPassportRequest) (*onbPb.UploadPassportResponse, error) {
	if strings.TrimSpace(req.GetImageBase64()) == "" {
		return &onbPb.UploadPassportResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("empty passport data"),
		}, nil
	}
	image, err := base64.StdEncoding.DecodeString(req.GetImageBase64())
	if err != nil {
		logger.Error(ctx, "failed to decode image base64 string", zap.Error(err))
		return &onbPb.UploadPassportResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	timeStr := strconv.FormatInt(time.Now().Unix(), 10)
	filePath := filepath.Join("passport", strings.ToLower(req.GetPassportSide().String()), timeStr, req.GetFileName())
	if err = s.nrS3Client.Write(ctx, filePath, image, string(awsS3.ObjectCannedACLBucketOwnerFullControl)); err != nil {
		logger.Error(ctx, "failed to store image in S3", zap.Error(err))
		return &onbPb.UploadPassportResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	// TODO(NR): remove nr bucket hardcoding
	httpUrl := img.GetHttpUrl(s.dynConf.NrBucketName(), s.dynConf.AWS().Region, filePath)

	onb, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error while getting onb details", zap.Error(err))
		return &onbPb.UploadPassportResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	sm := onb.GetStageMetadata().GetPassportVerificationMetadata()
	if sm == nil {
		sm = &onbPb.PassportVerificationMetadata{}
	}
	switch req.GetPassportSide() {
	case onbPb.PassportSide_PASSPORT_SIDE_FRONT:
		sm.PassportFrontUrl = httpUrl
		sm.Status = onbPb.PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_FRONT_DOWNLOADED
	case onbPb.PassportSide_PASSPORT_SIDE_BACK:
		sm.PassportBackUrl = httpUrl
		sm.Status = onbPb.PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_BACK_DOWNLOADED
	}
	onb.GetStageMetadata().PassportVerificationMetadata = sm

	if err = s.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_STAGE_METADATA}, onb); err != nil {
		logger.Error(ctx, "error while updating stage details by columns", zap.Error(err))
		return &onbPb.UploadPassportResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	s.eventLogger.LogPassportVerificationServer(ctx, onb.GetOnboardingId(), onbPb.PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_BACK_DOWNLOADED, "", onb.GetFeature())

	return &onbPb.UploadPassportResponse{
		Status: rpc.StatusOk(),
		NextAction: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
		},
	}, nil
}

// nolint:funlen
func (s *Service) FetchPassport(ctx context.Context, req *onbPb.FetchPassportRequest) (*onbPb.FetchPassportResponse, error) {
	passport, err := s.passportHelper.FetchPassport(ctx, req.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &onbPb.FetchPassportResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}

		logger.Error(ctx, "error in fetching passport details", zap.Error(err))
		return &onbPb.FetchPassportResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &onbPb.FetchPassportResponse{
		Status:   rpc.StatusOk(),
		Passport: passport,
	}, nil
}

func (s *Service) VerifyGlobalIssuedPassport(ctx context.Context, req *onbPb.VerifyGlobalIssuedPassportRequest) (*onbPb.VerifyGlobalIssuedPassportResponse, error) {

	if req.GetArn() != "" && req.GetFileNumber() != "" {
		logger.Error(ctx, "both arn and file number cannot be present")
		return &onbPb.VerifyGlobalIssuedPassportResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("both arn and file number cannot be present"),
		}, nil
	}

	if req.GetArn() != "" {
		onb, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, req.GetActorId())
		if err != nil {
			logger.Error(ctx, "error while getting onb details", zap.Error(err))
			return &onbPb.VerifyGlobalIssuedPassportResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
		sm := onb.GetStageMetadata().GetPassportVerificationMetadata()
		sm.PassportArn = req.GetArn()
		sm.Status = onbPb.PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_MANUAL_REVIEW
		onb.GetStageMetadata().PassportVerificationMetadata = sm

		if err = s.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_STAGE_METADATA}, onb); err != nil {
			logger.Error(ctx, "error while updating stage details by columns", zap.Error(err))
			return &onbPb.VerifyGlobalIssuedPassportResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
		logger.Info(ctx, "passport verification status set to manual review", zap.String(logger.REQUEST_ID, req.GetArn()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		s.eventLogger.LogPassportVerificationServer(ctx, req.GetActorId(), onbPb.PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_MANUAL_REVIEW, "", onb.GetFeature())
		return &onbPb.VerifyGlobalIssuedPassportResponse{
			Status: rpc.StatusOk(),
		}, nil
	}

	logger.Info(ctx, "passport verification using old passport file number", zap.String(logger.REQUEST_ID, req.GetFileNumber()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))

	passportData, err := s.passportHelper.FetchPassport(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in fetching passport details from actor id", zap.Error(err))
		if errors.Is(err, passport.FrontPageNotPresentError) || errors.Is(err, passport.BackPageNotPresentError) {
			return &onbPb.VerifyGlobalIssuedPassportResponse{
				Status: rpc.StatusRecordNotFoundWithDebugMsg(err.Error()),
			}, nil
		}

		return &onbPb.VerifyGlobalIssuedPassportResponse{
			Status: rpc.StatusFromError(err),
		}, nil
	}

	resp, err := s.vgDocClient.PassportVerification(ctx, &vgDocPb.PassportVerificationRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_KARZA,
		},
		CaseId:           idgen.FederalRandomSequence("PPV", 5),
		FileNumber:       req.GetFileNumber(),
		Dob:              passportData.GetDateOfBirth(),
		PassportNumber:   passportData.GetOldPassportNumber(),
		DateOfIssue:      passportData.GetDateOfIssue(),
		UserPassportName: passportData.GetName(),
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error in passport verification with karza", zap.Error(err))
		return &onbPb.VerifyGlobalIssuedPassportResponse{
			Status: rpc.StatusFromErrorWithDefaultInternal(err),
		}, nil
	}

	ppNumMatch := resp.GetVerificationResult().GetPassportNumberMatchResult().GetPassportNumberMatch()
	nameMatch := resp.GetVerificationResult().GetNameMatchResult().GetNameMatch()
	doiMatch := resp.GetVerificationResult().GetDateOfIssueMatchResult().GetDateOfIssueMatch()
	// validating name and old passport number match status
	if ppNumMatch == types.Verdict_VERDICT_FAIL {
		logger.Info(ctx, "old passport number match failed")
		return &onbPb.VerifyGlobalIssuedPassportResponse{
			Status: rpc.NewStatus(uint32(onbPb.VerifyGlobalIssuedPassportResponse_PASSPORT_NUMBER_MISMATCH), "old passport number match failed", ""),
		}, nil
	}
	if nameMatch == types.Verdict_VERDICT_FAIL {
		logger.Info(ctx, "name match failed")
		return &onbPb.VerifyGlobalIssuedPassportResponse{
			Status: rpc.NewStatus(uint32(onbPb.VerifyGlobalIssuedPassportResponse_PASSPORT_NAME_MISMATCH), "passport name match failed", ""),
		}, nil
	}

	onb, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error while getting onb details", zap.Error(err))
		return &onbPb.VerifyGlobalIssuedPassportResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	sm := onb.GetStageMetadata().GetPassportVerificationMetadata()

	sm.Status = onbPb.PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_SUCCESS
	sm.VerificationVendorApiResults = &onbPb.PassportVerificationMetadata_VerificationVendorAPIResults{
		PassportNumberMatch: ppNumMatch,
		UserNameMatch:       nameMatch,
		DateOfIssueMatch:    doiMatch,
	}
	sm.OldPassportFileNumber = req.GetFileNumber()
	sm.FailureReason = onbPb.PassportVerificationFailureReason_PASSPORT_VERIFICATION_FAILURE_REASON_UNSPECIFIED
	onb.GetStageMetadata().PassportVerificationMetadata = sm

	if err = s.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_STAGE_METADATA}, onb); err != nil {
		logger.Error(ctx, "error while updating stage details by columns", zap.Error(err))
		return &onbPb.VerifyGlobalIssuedPassportResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	s.eventLogger.LogPassportVerificationServer(ctx, req.GetActorId(), onbPb.PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_SUCCESS, "", onb.GetFeature())
	return &onbPb.VerifyGlobalIssuedPassportResponse{
		Status: rpc.StatusOk(),
	}, nil

}
