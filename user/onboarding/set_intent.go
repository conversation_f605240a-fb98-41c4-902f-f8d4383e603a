package onboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"strings"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/onboarding/metrics"
)

func (s *Service) SetOnboardingIntent(ctx context.Context, req *onbPb.SetOnboardingIntentRequest) (*onbPb.SetOnboardingIntentResponse, error) {

	s.recordMetricsAndLogEvents(ctx, req)

	switch req.GetIntent() {
	case onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT,
		onbPb.OnboardingIntent_ONBOARDING_INTENT_DEBIT_CARD:
		return s.handleSAOnbIntent(ctx, req)
	case onbPb.OnboardingIntent_ONBOARDING_INTENT_FI_LITE, onbPb.OnboardingIntent_ONBOARDING_INTENT_NET_WORTH:
		return s.handleFiLiteOnbIntent(ctx, req)
	case onbPb.OnboardingIntent_ONBOARDING_INTENT_WEALTH_ANALYSER:
		return s.handleWealthAnalyserOnbIntent(ctx, req)
	case onbPb.OnboardingIntent_ONBOARDING_INTENT_CREDIT_CARD:
		return s.handleCCOnbIntent(ctx, req)
	case onbPb.OnboardingIntent_ONBOARDING_INTENT_PERSONAL_LOANS:
		return s.handlePLOnbIntent(ctx, req)
	default:
		return &onbPb.SetOnboardingIntentResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("unhandled onboarding intent"),
		}, nil
	}
}

func (s *Service) recordMetricsAndLogEvents(ctx context.Context, req *onbPb.SetOnboardingIntentRequest) {
	// log
	logger.Info(ctx, "set onboarding intent called for actor", zap.Any(logger.UI_ENTRY_POINT, req.GetEntryPoint().String()), zap.Any("onboardingIntent", req.GetIntent().String()))

	// record metrics
	metrics.RecordOnboardingManualIntent(req.GetIntent())

	// log events
	s.eventLogger.LogSetOnboardingIntentServer(ctx, req.GetActorId(), req.GetIntent())
}

func (s *Service) handleCCOnbIntent(ctx context.Context, req *onbPb.SetOnboardingIntentRequest) (*onbPb.SetOnboardingIntentResponse, error) {
	onbDetails, err := s.getOnboardingDetailsWithIntentMetadata(ctx, req.GetActorId(), req.GetIntent())
	if err != nil {
		return &onbPb.SetOnboardingIntentResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	onbDetails.Feature = onbPb.Feature_FEATURE_CC

	if err = s.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
		onbPb.OnboardingDetailsFieldMask_FEATURE,
	}, onbDetails); err != nil {
		logger.Error(ctx, "error while updating intent metadata", zap.Error(err))
		return &onbPb.SetOnboardingIntentResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	s.eventLogger.LogOnboardingCCJourneyStarted(ctx, onbDetails)

	return &onbPb.SetOnboardingIntentResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) handleSAOnbIntent(ctx context.Context, req *onbPb.SetOnboardingIntentRequest) (*onbPb.SetOnboardingIntentResponse, error) {
	onbDetails, err := s.getOnboardingDetailsWithIntentMetadata(ctx, req.GetActorId(), req.GetIntent())
	if err != nil {
		return &onbPb.SetOnboardingIntentResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	onbDetails.Feature = onbPb.Feature_FEATURE_SA

	return s.updateOnbDetailsAndReturnIntentResponse(ctx, onbDetails, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_FEATURE,
		onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
	})
}

func (s *Service) handleFiLiteOnbIntent(ctx context.Context, req *onbPb.SetOnboardingIntentRequest) (*onbPb.SetOnboardingIntentResponse, error) {
	onbDetails, err := s.getOnboardingDetailsWithIntentMetadata(ctx, req.GetActorId(), req.GetIntent())
	if err != nil {
		return &onbPb.SetOnboardingIntentResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	onbDetails = s.populateFiLiteOnboardingDetails(ctx, onbDetails, commontypes.BooleanEnum_TRUE, onbPb.FiLiteSource_FI_LITE_SOURCE_INTENT_SELECTION, timestampPb.New(s.Time.Now()))

	updResp, updErr := s.updateOnbDetailsAndReturnIntentResponse(ctx, onbDetails, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_FEATURE,
		onbPb.OnboardingDetailsFieldMask_FI_LITE_DETAILS,
		onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
	})
	if updResp.GetStatus().IsSuccess() {
		s.eventLogger.LogFiLiteUserConverted(ctx, onbDetails.GetActorId(), onbDetails.GetFiLiteDetails().GetFiLiteSource())
	}
	return updResp, updErr
}

func (s *Service) handleWealthAnalyserOnbIntent(ctx context.Context, req *onbPb.SetOnboardingIntentRequest) (*onbPb.SetOnboardingIntentResponse, error) {
	onbDetails, err := s.getOnboardingDetailsWithIntentMetadata(ctx, req.GetActorId(), req.GetIntent())
	if err != nil {
		return &onbPb.SetOnboardingIntentResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	onbDetails.Feature = onbPb.Feature_FEATURE_WEALTH_ANALYSER

	updResp, updErr := s.updateOnbDetailsAndReturnIntentResponse(ctx, onbDetails, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_FEATURE,
		onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
	})
	return updResp, updErr
}

func (s *Service) handlePLOnbIntent(ctx context.Context, req *onbPb.SetOnboardingIntentRequest) (*onbPb.SetOnboardingIntentResponse, error) {
	onbDetails, err := s.getOnboardingDetailsWithIntentMetadata(ctx, req.GetActorId(), req.GetIntent())
	if err != nil {
		return &onbPb.SetOnboardingIntentResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	onbDetails.Feature = onbPb.Feature_FEATURE_PL

	return s.updateOnbDetailsAndReturnIntentResponse(ctx, onbDetails, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_FEATURE,
		onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
	})
}

func (s *Service) getOnboardingDetailsWithIntentMetadata(ctx context.Context, actorId string, intent onbPb.OnboardingIntent) (*onbPb.OnboardingDetails, error) {
	onbDetails, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while getting onb details", zap.Error(err))
		return nil, err
	}
	onbDetails.StageMetadata = populateOnboardingIntentMetadata(onbDetails.GetStageMetadata(), intent, s.dynConf)
	return onbDetails, nil
}

func (s *Service) updateOnbDetailsAndReturnIntentResponse(ctx context.Context, details *onbPb.OnboardingDetails, fm []onbPb.OnboardingDetailsFieldMask) (*onbPb.SetOnboardingIntentResponse, error) {
	if err := s.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, fm, details); err != nil {
		logger.Error(ctx, "error while updating intent metadata", zap.Error(err))
		return &onbPb.SetOnboardingIntentResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &onbPb.SetOnboardingIntentResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func populateOnboardingIntentMetadata(metadata *onbPb.StageMetadata, intent onbPb.OnboardingIntent, onbConf *genconf.OnboardingConfig) *onbPb.StageMetadata {
	if metadata == nil {
		metadata = &onbPb.StageMetadata{}
	}
	metadata.IntentSelectionMetadata = &onbPb.IntentSelectionMetadata{
		Selection: intent,
		Choices:   getValidIntentListFromConfig(onbConf),
	}
	return metadata
}

func getValidIntentListFromConfig(onbConf *genconf.OnboardingConfig) []onbPb.OnboardingIntent {
	var resp []onbPb.OnboardingIntent
	onbConf.IntentSelectionConfigV2().IntentConfigMap().Range(func(key string, _ *genconf.IntentConfig) (continueRange bool) {
		intentVal, ok := onbPb.OnboardingIntent_value[strings.ToUpper(key)]
		if ok {
			resp = append(resp, onbPb.OnboardingIntent(intentVal))
		}
		return true
	})

	return resp
}
