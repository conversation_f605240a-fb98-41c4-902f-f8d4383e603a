package workflow

import (
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	ffActPb "github.com/epifi/gamma/api/firefly/activity"
	ccEnumPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/firefly/workflow/internal"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	fireflyNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/firefly"
	"github.com/epifi/be-common/pkg/logger"
)

// ProcessViewCardDetails workflow is a flow which will guide the user through the auth process
// as per their complexity of app unlock and then will reveal the clear card details
func ProcessViewCardDetails(ctx workflow.Context, req *workflowPb.Request) error {
	var (
		wfProcessingParams = &workflowPb.ProcessingParams{}
		stageStatus        stagePb.Status
		err                error
	)
	lg := workflow.GetLogger(ctx)
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID

	// get processing params including client request ID
	if err = activityPkg.ExecuteRaw(ctx, epifitemporal.GetWorkflowProcessingParams, &wfProcessingParams, wfReqID); err != nil {
		lg.Error("activity failed", zap.String(logger.ACTIVITY, string(epifitemporal.GetWorkflowProcessingParams)), zap.Error(err))
		return err
	}

	// update card request state to in_progress state
	_, err = internal.UpdateCardRequest(ctx, wfProcessingParams, &ffActPb.CardRequestUpdateRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
		},
		Status: ccEnumPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
		FieldMasks: []ccEnumPb.CardRequestFieldMask{
			ccEnumPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
		},
	})
	if err != nil {
		lg.Error("error while updating card request", zap.Error(err), zap.String(logger.ORCHESTRATION_ID, wfProcessingParams.GetClientReqId().GetId()))
		return err
	}

	// --------------------------- AUTH STAGE ------------------------------------
	stageStatus, err = PerformAuth(ctx, wfReqID, wfProcessingParams)
	if err != nil {
		lg.Error("error in performAuth", zap.Error(err))
		return err
	}
	if err != nil {
		return errors.Wrap(err, "executeActivity failed")
	}
	if stageStatus != stagePb.Status_SUCCESSFUL {
		return nil
	}

	// --------------------------- VIEW CARD DETAILS STAGE ------------------------
	if err = activityPkg.ExecuteRaw(ctx, epifitemporal.InitiateWorkflowStage, nil, wfReqID, workflowPb.Stage_VIEW_CARD_DETAILS, stagePb.Status_INITIATED); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStage, err)
	}
	if err = performViewCardDetailsStage(ctx, wfReqID, wfProcessingParams); err != nil {
		lg.Error("view card details stage failed in ViewCardDetails flow", zap.Error(err))
		return err
	}

	if err = activityPkg.ExecuteRaw(ctx, epifitemporal.PublishWorkflowUpdateEvent, nil, wfReqID); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.PublishWorkflowUpdateEvent, err)
	}
	return nil
}

func performViewCardDetailsStage(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams) error {
	var (
		workflowStageStatus stagePb.Status
		cardRequestStatus   ccEnumPb.CardRequestStatus
	)
	actErr := activityPkg.Execute(ctx, fireflyNs.ViewCardDetails, &ffActPb.ViewCardDetailsResponse{}, &ffActPb.ViewCardDetailsRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
		},
	})

	switch {
	case actErr != nil && !epifitemporal.IsRetryableError(actErr):
		workflowStageStatus = stagePb.Status_FAILED
		cardRequestStatus = ccEnumPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED
	case actErr != nil:
		// activity erred out
		workflowStageStatus = stagePb.Status_MANUAL_INTERVENTION
		cardRequestStatus = ccEnumPb.CardRequestStatus_CARD_REQUEST_STATUS_MANUAL_INTERVENTION
	default:
		// activity processed
		workflowStageStatus = stagePb.Status_SUCCESSFUL
		cardRequestStatus = ccEnumPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS
	}
	_, err := internal.UpdateCardRequest(ctx, wfProcessingParams, &ffActPb.CardRequestUpdateRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
		},
		Status: cardRequestStatus,
		FieldMasks: []ccEnumPb.CardRequestFieldMask{
			ccEnumPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
		},
		WorkflowStage: workflowPb.Stage_VIEW_CARD_DETAILS,
	})
	if err != nil {
		return errors.Wrap(err, "error while updating card request")
	}

	// activity erred out
	if err := activityPkg.ExecuteRaw(ctx, epifitemporal.UpdateWorkflowStageStatus, nil, wfReqID, workflowPb.Stage_VIEW_CARD_DETAILS, workflowStageStatus); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStageStatus, err)
	}

	if stagePb.IsTerminalStatus(workflowStageStatus) {
		if publishErr := activityPkg.ExecuteRaw(ctx, epifitemporal.PublishWorkflowUpdateEvent, nil, wfReqID); publishErr != nil {
			return fmt.Errorf("%s activity failed: %w", epifitemporal.PublishWorkflowUpdateEvent, publishErr)
		}
	}

	return nil
}
