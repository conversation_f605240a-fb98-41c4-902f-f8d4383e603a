package v2

import (
	"context"
	"errors"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/firefly/config/genconf"

	"github.com/samber/lo"

	"github.com/google/uuid"
	errorsPkg "github.com/pkg/errors"
	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	authPb "github.com/epifi/gamma/api/auth"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	ffBePb "github.com/epifi/gamma/api/firefly"
	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ffConsumerV2Pb "github.com/epifi/gamma/api/firefly/v2/consumer"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	typesV2 "github.com/epifi/gamma/api/typesv2"
	accountsTypes "github.com/epifi/gamma/api/typesv2/account"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	ccVgPb "github.com/epifi/gamma/api/vendorgateway/creditcard"
	"github.com/epifi/gamma/firefly/v2/dao"
	wireTypes "github.com/epifi/gamma/firefly/v2/wire/types"
	"github.com/epifi/gamma/pkg/feature/release"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

var (
	cardProgramSourceToApplicantMap = map[typesV2.CardProgramSource]ccEnumsV2Pb.CreditCardApplicantType{
		typesV2.CardProgramSource_CARD_PROGRAM_SOURCE_PREAPPROVED: ccEnumsV2Pb.CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_PRE_APPROVED_ONBOARDING,
	}
	cardProgramOriginToApplicationTypeMap = map[typesV2.CardProgramOrigin]ccEnumsV2Pb.CreditCardApplicantType{
		typesV2.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI:      ccEnumsV2Pb.CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_ETB_ONBOARDING,
		typesV2.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE: ccEnumsV2Pb.CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_NTB_ONBOARDING,
	}

	ccSdkModuleToTokenTypeMap = map[ccVgPb.CreditCardSdkModuleName]typesV2.CreditCardSdkTokenType{
		ccVgPb.CreditCardSdkModuleName_CREDIT_CARD_SDK_MODULE_NAME_CMS:        typesV2.CreditCardSdkTokenType_TOKEN_TYPE_CMS,
		ccVgPb.CreditCardSdkModuleName_CREDIT_CARD_SDK_MODULE_NAME_ONBOARDING: typesV2.CreditCardSdkTokenType_TOKEN_TYPE_ONBOARDING,
	}
)

type Service struct {
	ffBeV2Pb.UnimplementedFireflyV2Server
	conf                                  *genconf.Config
	ccVgClient                            ccVgPb.CreditCardClient
	authClient                            authPb.AuthClient
	cardRequestDao                        dao.CardRequestDao
	creditCardDao                         dao.CreditCardDao
	ccOfferDao                            dao.CreditCardOffersDao
	userClient                            userPb.UsersClient
	bankCustClient                        bankCustPb.BankCustomerServiceClient
	onbClient                             onbPb.OnboardingClient
	savingsClient                         savingsPb.SavingsClient
	ccOnboardingStateUpdateEventPublisher wireTypes.CcOnboardingStateUpdateEventPublisher
	segmentationClient                    segmentPb.SegmentationServiceClient
	fireflyClient                         ffBePb.FireflyClient
	releaseEvaluator                      release.IEvaluator
}

func NewService(conf *genconf.Config, ccVgClient ccVgPb.CreditCardClient, authClient authPb.AuthClient,
	cardRequestDao dao.CardRequestDao, creditCardDao dao.CreditCardDao, ccOfferDao dao.CreditCardOffersDao,
	userClient userPb.UsersClient, bankCustClient bankCustPb.BankCustomerServiceClient,
	onbClient onbPb.OnboardingClient, savingsClient savingsPb.SavingsClient,
	ccOnboardingStateUpdateEventPublisher wireTypes.CcOnboardingStateUpdateEventPublisher,
	segmentationClient segmentPb.SegmentationServiceClient, fireflyClient ffBePb.FireflyClient, releaseEvaluator release.IEvaluator) *Service {
	return &Service{
		conf:                                  conf,
		ccVgClient:                            ccVgClient,
		authClient:                            authClient,
		cardRequestDao:                        cardRequestDao,
		creditCardDao:                         creditCardDao,
		ccOfferDao:                            ccOfferDao,
		userClient:                            userClient,
		bankCustClient:                        bankCustClient,
		onbClient:                             onbClient,
		savingsClient:                         savingsClient,
		ccOnboardingStateUpdateEventPublisher: ccOnboardingStateUpdateEventPublisher,
		segmentationClient:                    segmentationClient,
		fireflyClient:                         fireflyClient,
		releaseEvaluator:                      releaseEvaluator,
	}
}

func (s *Service) FetchCreditCardEligibility(ctx context.Context, req *ffBeV2Pb.FetchCreditCardEligibilityRequest) (*ffBeV2Pb.FetchCreditCardEligibilityResponse, error) {
	res := &ffBeV2Pb.FetchCreditCardEligibilityResponse{}

	// First check if user is already a credit card user
	isCcUserResp, err := s.fireflyClient.IsCreditCardUser(ctx, &ffBePb.IsCreditCardUserRequest{
		ActorId: req.GetActorId(),
	})
	if te := epifigrpc.RPCError(isCcUserResp, err); te != nil {
		logger.Error(ctx, "error while checking if user is already a credit card user", zap.Error(te))
		res.Status = rpc.StatusInternalWithDebugMsg(te.Error())
		return res, nil
	}

	// If user is already a credit card user, return ALREADY_EXISTS status
	if isCcUserResp.GetIsCreditCardUser() {
		logger.Debug(ctx, "user is already a credit card user", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusAlreadyExists()
		res.IsUserCcEligible = false // Set to false when user already exists
		return res, nil
	}

	// Check eligibility for new users
	isUserCcEligible, err := s.isUserCcEligible(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error checking CC v2 eligibility for user", zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return res, nil
	}

	res.IsUserCcEligible = isUserCcEligible
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) GetCreditCards(ctx context.Context, req *ffBeV2Pb.GetCreditCardsRequest) (*ffBeV2Pb.GetCreditCardsResponse, error) {
	var (
		res          = &ffBeV2Pb.GetCreditCardsResponse{}
		creditCards  []*ffBeV2Pb.CreditCard
		err          error
		stateFilters = []ccEnumsV2Pb.CardState{ccEnumsV2Pb.CardState_CARD_STATE_CREATED}
	)
	if len(req.GetStateFilters()) != 0 {
		stateFilters = req.GetStateFilters()
	}

	switch {
	case req.GetActorId() != "":
		creditCards, err = s.creditCardDao.GetByActorId(ctx, req.GetActorId())

	case req.GetExternalUserId() != "":
		creditCards, err = s.creditCardDao.GetByExternalUserId(ctx, req.GetExternalUserId())

	default:
		logger.Error(ctx, "invalid request for get credit cards")
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}
	switch {
	case err != nil && storageV2.IsRecordNotFoundError(err) || len(creditCards) == 0:
		logger.Error(ctx, "no credit cards found for user", zap.Error(err))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error fetching credit cards for user", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		for _, cc := range creditCards {
			if lo.Contains(stateFilters, cc.GetState()) {
				res.CreditCards = append(res.GetCreditCards(), cc)
			}
		}
		if len(res.GetCreditCards()) == 0 {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusOk()
		return res, nil
	}
}

func (s *Service) GenerateCreditCardSdkAuthToken(ctx context.Context, req *ffBeV2Pb.GenerateCreditCardSdkAuthTokenRequest) (*ffBeV2Pb.GenerateCreditCardSdkAuthTokenResponse, error) {
	savedCards, err := s.creditCardDao.GetByActorId(ctx, req.GetActorId())
	switch {
	case err != nil && !storageV2.IsRecordNotFoundError(err):
		logger.Error(ctx, "error fetching credit card for actor", zap.Error(err))
		return &ffBeV2Pb.GenerateCreditCardSdkAuthTokenResponse{
			Status: rpc.StatusInternal(),
		}, nil

	case len(savedCards) > 0 && savedCards[0].GetState() == ccEnumsV2Pb.CardState_CARD_STATE_CLOSED:
		logger.Info(ctx, "credit card is already closed", zap.String(logger.CARD_ID, savedCards[0].GetId()))
		return &ffBeV2Pb.GenerateCreditCardSdkAuthTokenResponse{
			Status: rpc.StatusFailedPrecondition(),
		}, nil
	}

	// get or create the latest onboarding request for the actor,
	// new onboarding request is created if no valid request is found or latest request is in failed state
	ccOnbReq, err := s.getOrCreateOnboardingRequest(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error getting or creating onboarding request for actor", zap.Error(err))
		return &ffBeV2Pb.GenerateCreditCardSdkAuthTokenResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	goroutine.RunWithDefaultTimeout(ctx, func(gctx context.Context) {
		// After successfully creating the card request, update Fi Lite accessibility for D2CC users
		// This ensures they can access home when they return to the app
		s.updateFiLiteAccessibilityForCCOnboarding(ctx, req.GetActorId())
	})

	authToke, moduleName, err := s.generateCreditCardSdkAuthToken(ctx, ccOnbReq, req.GetDeviceInfo())
	if err != nil {
		logger.Error(ctx, "error generating credit card sdk auth token", zap.Error(err))
		return &ffBeV2Pb.GenerateCreditCardSdkAuthTokenResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &ffBeV2Pb.GenerateCreditCardSdkAuthTokenResponse{
		Status:    rpc.StatusOk(),
		AuthToken: authToke,
		TokenType: ccSdkModuleToTokenTypeMap[moduleName],
	}, nil
}

func (s *Service) generateCreditCardSdkAuthToken(ctx context.Context, ccOnbReq *ffBeV2Pb.CardRequest, deviceInfoFromFeReq *commontypes.Device) (string, ccVgPb.CreditCardSdkModuleName, error) {
	userDetailsResp, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: ccOnbReq.GetActorId(),
		},
	})
	if te := epifigrpc.RPCError(userDetailsResp, err); te != nil {
		return "", 0, errorsPkg.Wrap(te, "error fetching user details for actor id")
	}

	ccOffer, err := s.getCcOfferWithIdNilCheck(ctx, ccOnbReq.GetRequestDetails().GetOnboardingRequestDetails().GetOfferId())
	if err != nil {
		logger.Error(ctx, "error fetching cc offer by id", zap.Error(err))
		return "", 0, errorsPkg.Wrap(err, "error fetching cc offer by id for initial onboarding auth")
	}

	vgAuthRequest := &ccVgPb.GenerateCreditCardSdkAuthTokenRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_SAVEN,
		},
		ApplicantType: ccOnbReq.GetRequestDetails().GetOnboardingRequestDetails().GetApplicantType(),
		UserInfo: &ccVgPb.UserInfo{
			EmailAddress:   userDetailsResp.GetUser().GetProfile().GetEmail(),
			PhoneNumber:    userDetailsResp.GetUser().GetProfile().GetPhoneNumber(),
			InternalUserId: ccOnbReq.GetExternalUserId(),
		},
		DeviceInfo: &ccVgPb.DeviceInfo{
			Device: deviceInfoFromFeReq,
		},
	}

	if ccOffer != nil {
		vgAuthRequest.PreApprovedInfo = &ccVgPb.PreApprovedInfo{
			PreApprovedLimit: ccOffer.GetOfferConstraints().GetLimit(),
			PreApprovedExp:   dateTimePkg.TimeToDateInLoc(ccOffer.GetValidTill().AsTime(), dateTimePkg.IST),
		}
	}

	if (ccOnbReq.GetRequestDetails().GetOnboardingRequestDetails().GetApplicantType() == ccEnumsV2Pb.CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_ETB_ONBOARDING ||
		ccOnbReq.GetRequestDetails().GetOnboardingRequestDetails().GetApplicantType() == ccEnumsV2Pb.CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_PRE_APPROVED_ONBOARDING) &&
		userDetailsResp.GetUser().GetProfile().GetPAN() != "" && userDetailsResp.GetUser().GetProfile().GetPanName() != nil {
		vgAuthRequest.PanInfo = &ccVgPb.PanInfo{
			PanNumber: userDetailsResp.GetUser().GetProfile().GetPAN(),
			UserName:  userDetailsResp.GetUser().GetProfile().GetPanName(),
		}
	}

	vgGenCCAuthTokenResp, err := s.ccVgClient.GenerateCreditCardSdkAuthToken(ctx, vgAuthRequest)
	if te := epifigrpc.RPCError(vgGenCCAuthTokenResp, err); te != nil {
		return "", 0, errorsPkg.Wrap(te, "failed to generate cc auth token for cc sdk at vg")
	}

	if !s.isInitialOnboardingAuthCompleted(ccOnbReq) {
		logger.Info(ctx, "credit card onboarding initiated for actor", zap.String(logger.CARD_REQUEST_ID, ccOnbReq.GetId()))
		if err = s.handleCcOnboardingInitSuccess(ctx, ccOnbReq, vgGenCCAuthTokenResp); err != nil {
			logger.Error(ctx, "error updating onboarding request after generating cc auth token", zap.Error(err))
			return "", 0, errorsPkg.Wrap(err, "error updating onboarding request after generating cc auth token")
		}
	}

	return vgGenCCAuthTokenResp.GetAuthToken(), vgGenCCAuthTokenResp.GetModuleName(), nil
}

func (s *Service) handleCcOnboardingInitSuccess(ctx context.Context, cardRequest *ffBeV2Pb.CardRequest, vgGenCCAuthTokenResp *ccVgPb.GenerateCreditCardSdkAuthTokenResponse) error {
	_, err := s.ccOnboardingStateUpdateEventPublisher.Publish(ctx, &ffConsumerV2Pb.ProcessCreditCardOnboardingStateUpdateEventRequest{
		ExternalUserId:   cardRequest.GetExternalUserId(),
		VendorWorkflowId: vgGenCCAuthTokenResp.GetAdditionalInfo().GetWorkflowId(),
		WorkflowStatus:   vgGenCCAuthTokenResp.GetAdditionalInfo().GetWorkflowStatus(),
		StageTransitionInfo: &ffConsumerV2Pb.CreditCardOnboardingStageTransitionInfo{
			CurrentStage: ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_ONBOARDING_INIT_AT_VENDOR,
			NextStage:    vgGenCCAuthTokenResp.GetAdditionalInfo().GetWorkflowState(),
			Description:  vgGenCCAuthTokenResp.GetAdditionalInfo().GetWorkflowMessage(),
		},
	})
	if err != nil {
		logger.Error(ctx, "error publishing credit card onboarding stage update event", zap.Error(err))
		return err
	}
	return nil
}

// getOrCreateOnboardingRequest retrieves or creates a card onboarding request for the given actor Id.
// It first attempts to fetch existing onboarding requests for the actor. If a valid request is found, it is returned.
// If no valid request is found, a new onboarding request is created and returned.
func (s *Service) getOrCreateOnboardingRequest(ctx context.Context, actorId string) (*ffBeV2Pb.CardRequest, error) {
	cardRequests, err := s.cardRequestDao.GetByActorIdAndRequestType(ctx, actorId, ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING)
	if err != nil && !storageV2.IsRecordNotFoundError(err) {
		return nil, errorsPkg.Wrap(err, "error fetching cc onboarding request for actor id")
	}
	if len(cardRequests) > 0 && cardRequests[0].GetStatus() != ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED {
		return cardRequests[0], nil
	}
	extUserId := uuid.NewString()
	if len(cardRequests) > 0 {
		extUserId = cardRequests[0].GetExternalUserId()
	}
	// Create a new onboarding request
	return s.createOnboardingCardRequest(ctx, actorId, extUserId)
}

func (s *Service) createOnboardingCardRequest(ctx context.Context, actorId, extUserId string) (*ffBeV2Pb.CardRequest, error) {
	cardProgramOrigin, err := s.getCardProgramOriginForCcOnb(ctx, actorId)
	if err != nil {
		return nil, errorsPkg.Wrap(err, "error fetching card program origin for cc onboarding")
	}

	onbRequest := &ffBeV2Pb.CardRequest{
		ActorId: actorId,
		Type:    ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING,
		Status:  ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
		RequestDetails: &ffBeV2Pb.CardRequestDetails{
			Data: &ffBeV2Pb.CardRequestDetails_OnboardingRequestDetails{
				OnboardingRequestDetails: &ffBeV2Pb.CardOnboardingRequestDetails{
					// TODO(cb): make changes to get card program from client during onboarding init and modify the logic accordingly. For now this works since we'll be enabling onb for mgnifi only.
					CardProgram: &typesV2.CardProgram{
						CardProgramVendor:     typesV2.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
						CardProgramSource:     typesV2.CardProgramSource_CARD_PROGRAM_SOURCE_REALTIME_BRE,
						CardProgramType:       typesV2.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED,
						CardProgramCollateral: typesV2.CardProgramCollateral_CARD_PROGRAM_COLLATERAL_UNSPECIFIED,
						CardProgramOrigin:     cardProgramOrigin,
					},
				},
			},
		},
		StageDetails: &ffBeV2Pb.CardRequestStageDetails{
			Stages: map[string]*ffBeV2Pb.StageInfo{
				ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_ONBOARDING_INIT_AT_VENDOR.String(): {
					State:         ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_INITIATED,
					LastUpdatedAt: timestamp.Now(),
					StartedAt:     timestamp.Now(),
				},
			},
		},
		Vendor:         commonvgpb.Vendor_SAVEN,
		ExternalUserId: extUserId,
	}

	// check for any pre-approved offer for the actor
	ccOffer, err := s.ccOfferDao.GetActiveOfferByActorIdAndVendorAndCardProgram(ctx, actorId, commonvgpb.Vendor_FEDERAL_BANK,
		ffPkg.GetCardProgramStringFromCardProgram(&typesV2.CardProgram{
			CardProgramVendor:     typesV2.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
			CardProgramSource:     typesV2.CardProgramSource_CARD_PROGRAM_SOURCE_PREAPPROVED,
			CardProgramType:       typesV2.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED,
			CardProgramCollateral: typesV2.CardProgramCollateral_CARD_PROGRAM_COLLATERAL_UNSPECIFIED,
			CardProgramOrigin:     typesV2.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI,
		}))
	if err != nil && !storageV2.IsRecordNotFoundError(err) {
		return nil, errorsPkg.Wrap(err, "error fetching cc offers for actor id")
	}
	if ccOffer != nil {
		onbRequest.RequestDetails = &ffBeV2Pb.CardRequestDetails{
			Data: &ffBeV2Pb.CardRequestDetails_OnboardingRequestDetails{
				OnboardingRequestDetails: &ffBeV2Pb.CardOnboardingRequestDetails{
					OfferId:     ccOffer.GetId(),
					CardProgram: ccOffer.GetCardProgram(),
				},
			},
		}
	}

	applicantType := getOnboardingApplicationType(onbRequest)
	if applicantType == ccEnumsV2Pb.CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_UNSPECIFIED {
		return nil, errorsPkg.New("invalid applicant type for onboarding request")
	}
	onbRequest.RequestDetails.GetOnboardingRequestDetails().ApplicantType = applicantType

	newCardRequest, err := s.cardRequestDao.Create(ctx, onbRequest)
	if err != nil {
		return nil, errorsPkg.Wrap(err, "error creating cc onboarding request for actor id")
	}
	return newCardRequest, nil
}

// getCcOfferWithIdNilCheck retrieves a credit card offer by its ID if the ID is not empty.
// If the offer ID is empty, it returns nil without an error.
// If an error occurs during the retrieval, it returns the error.
func (s *Service) getCcOfferWithIdNilCheck(ctx context.Context, offerId string) (*ffBeV2Pb.CreditCardOffer, error) {
	if offerId == "" {
		return nil, nil
	}
	ccOffer, err := s.ccOfferDao.GetById(ctx, offerId)
	if err != nil {
		return nil, err
	}
	return ccOffer, nil
}

// isInitialOnboardingAuthCompleted checks if the initial onboarding auth has already been completed for a card request.
func (s *Service) isInitialOnboardingAuthCompleted(ccOnbReq *ffBeV2Pb.CardRequest) bool {
	if ccOnbReq.GetStatus() == ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS {
		return true
	}
	onbVendorInitStageState := ccOnbReq.GetStageDetails().GetStages()[ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_ONBOARDING_INIT_AT_VENDOR.String()].GetState()
	return onbVendorInitStageState == ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_SUCCESS
}

// getOnboardingApplicationType retrieves the onboarding application type for a card request based on its card program.
// It first checks the card program source and then the card program origin if the source is not found.
func getOnboardingApplicationType(ccOnbReq *ffBeV2Pb.CardRequest) ccEnumsV2Pb.CreditCardApplicantType {
	onbApplicationType, ok := cardProgramSourceToApplicantMap[ccOnbReq.GetRequestDetails().GetOnboardingRequestDetails().GetCardProgram().GetCardProgramSource()]
	if !ok {
		onbApplicationType = cardProgramOriginToApplicationTypeMap[ccOnbReq.GetRequestDetails().GetOnboardingRequestDetails().GetCardProgram().GetCardProgramOrigin()]
	}
	return onbApplicationType
}

// We might need to collect pan dob and per dedupe check for NTB onboarding.
func (s *Service) getCardProgramOriginForCcOnb(ctx context.Context, actorId string) (typesV2.CardProgramOrigin, error) {
	// Check if user is Fi Lite user
	_, gaErr := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_ActorUniqueAccountIdentifier{
			ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
				ActorId:                actorId,
				AccountProductOffering: accountsTypes.AccountProductOffering_APO_REGULAR,
				PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	if gaErr != nil {
		if s, _ := status.FromError(gaErr); s.Code() == codes.NotFound {
			return typesV2.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE, nil
		}
		return typesV2.CardProgramOrigin_CARD_PROGRAM_ORIGIN_UNSPECIFIED, errorsPkg.Wrap(gaErr, "GetAccount failed")
	}
	return typesV2.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI, nil
}

// isUserHomeAccessible checks if a user is home accessible.
// A user is home accessible if they are either a Fi SA holder or a Fi Lite user.
func (s *Service) isUserHomeAccessible(ctx context.Context, actorId string) (bool, error) {
	// Check if user has an active savings account (Fi SA holder)
	isFiSAHolder := true
	_, gaErr := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_ActorUniqueAccountIdentifier{
			ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
				ActorId:                actorId,
				PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
				AccountProductOffering: accountsTypes.AccountProductOffering_APO_REGULAR,
			},
		},
	})
	if gaErr != nil {
		if s, _ := status.FromError(gaErr); s.Code() == codes.NotFound {
			isFiSAHolder = false
		} else {
			return false, errorsPkg.Wrap(gaErr, "GetAccount failed to fetch account")
		}
	}

	// Check if user is Fi Lite user
	getSAFeatResp, err := s.onbClient.GetFeatureDetails(ctx, &onbPb.GetFeatureDetailsRequest{
		ActorId: actorId,
		Feature: onbPb.Feature_FEATURE_SA,
	})
	if grpcErr := epifigrpc.RPCError(getSAFeatResp, err); grpcErr != nil && !getSAFeatResp.GetStatus().IsRecordNotFound() {
		return false, errorsPkg.Wrap(grpcErr, "failed to get SA feature details from onboarding")
	}

	isFiLiteUser := getSAFeatResp.GetIsFiLiteUser()

	// User is home accessible if they are Fi SA holder or Fi Lite user
	return isFiSAHolder || isFiLiteUser, nil
}

// updateFiLiteAccessibilityForCCOnboarding updates Fi Lite accessibility for D2CC users.
// As soon as a D2CC user shows their intent to apply for a credit card, they should be converted to Fi Lite so that
// the next time they open the app, they should land on home so that they can explore other features along with credit cards.
// This is being done primarily to provide a way for the user to explore other options when they are stuck or rejected from credit card flow.
func (s *Service) updateFiLiteAccessibilityForCCOnboarding(ctx context.Context, actorId string) {
	isHomeAccessible, err := s.isUserHomeAccessible(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error checking if user is home accessible", zap.Error(err))
		// Don't block the credit card onboarding flow if this check fails
		return
	}

	// Only update Fi Lite accessibility if user is not already home accessible (i.e., D2CC user)
	if !isHomeAccessible {
		fiLiteRes, fiLiteErr := s.onbClient.UpdateFiLiteAccessibility(ctx, &onbPb.UpdateFiLiteAccessibilityRequest{
			ActorId:   actorId,
			Source:    onbPb.FiLiteSource_FI_LITE_SOURCE_CREDIT_CARD_SDK_ONBOARDING,
			IsEnabled: commontypes.BooleanEnum_TRUE,
		})
		if te := epifigrpc.RPCError(fiLiteRes, fiLiteErr); te != nil {
			logger.Error(ctx, "error in updating Fi Lite accessibility for D2CC user", zap.Error(te))
			// Don't block the credit card onboarding flow if this update fails
		}
	}
}

// nolint:dupl
// GetCardRequest retrieves the status of a card request
func (s *Service) GetCardRequest(ctx context.Context, req *ffBeV2Pb.GetCardRequestRequest) (*ffBeV2Pb.GetCardRequestResponse, error) {
	var (
		res          = &ffBeV2Pb.GetCardRequestResponse{}
		cardRequest  *ffBeV2Pb.CardRequest
		cardRequests []*ffBeV2Pb.CardRequest
		err          error
		requestType  = req.GetRequestType()
	)

	// Handle different identifier cases
	switch {
	case req.GetRequestId() != "":
		// If request ID is provided, fetch the specific card request
		cardRequest, err = s.cardRequestDao.GetById(ctx, req.GetRequestId())
		if err != nil {
			logger.Error(ctx, "error fetching card request by id", zap.Error(err), zap.String(logger.REQUEST_ID, req.GetRequestId()))
			return &ffBeV2Pb.GetCardRequestResponse{
				Status: getStatusFromDaoError(err),
			}, nil
		}
		// Check if the request type matches the filter
		if requestType != ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_UNSPECIFIED &&
			cardRequest.GetType() != requestType {
			logger.Error(ctx, "card request type doesn't match the filter",
				zap.String(logger.REQUEST_ID, req.GetRequestId()),
				zap.String(logger.REQUEST_TYPE, cardRequest.GetType().String()))
			res.Status = rpc.StatusInvalidArgument()
			return res, nil
		}

	case req.GetActorId() != "":
		// If actor ID is provided, fetch card requests by actor ID and type
		cardRequests, err = s.cardRequestDao.GetByActorIdAndRequestType(ctx, req.GetActorId(), requestType)
		if err != nil {
			logger.Error(ctx, "error fetching card request by actor id and request type", zap.Error(err), zap.String(logger.REQUEST_TYPE, req.GetRequestType().String()))
			return &ffBeV2Pb.GetCardRequestResponse{
				Status: getStatusFromDaoError(err),
			}, nil
		}
		// Get the most recent card request (should be first as they're sorted by created_at DESC)
		if len(cardRequests) > 0 {
			cardRequest = cardRequests[0]
		}

	case req.GetExternalUserId() != "":
		// If external user ID is provided, fetch card requests by external user ID and type
		cardRequests, err = s.cardRequestDao.GetByExternalUserIdAndRequestType(ctx, req.GetExternalUserId(), requestType)
		if err != nil {
			logger.Error(ctx, "error fetching card request by external id and request type", zap.Error(err), zap.String(logger.REQUEST_TYPE, req.GetRequestType().String()))
			return &ffBeV2Pb.GetCardRequestResponse{
				Status: getStatusFromDaoError(err),
			}, nil
		}
		// Get the most recent card request (should be first as they're sorted by created_at DESC)
		if len(cardRequests) > 0 {
			cardRequest = cardRequests[0]
		}

	default:
		logger.Error(ctx, "invalid request for get card request status, no identifier provided")
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	// Return the card request
	res.CardRequest = cardRequest
	res.Status = rpc.StatusOk()
	return res, nil
}

func getStatusFromDaoError(err error) *rpc.Status {
	switch {
	case storageV2.IsRecordNotFoundError(err):
		return rpc.StatusRecordNotFound()

	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return rpc.StatusInvalidArgument()

	default:
		return rpc.StatusInternal()
	}
}
