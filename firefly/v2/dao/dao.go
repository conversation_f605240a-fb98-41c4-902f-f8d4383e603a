//go:generate mockgen -source=dao.go -destination=mocks/mock_dao.go
//go:generate dao_metrics_gen .
package dao

import (
	"context"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/google/wire"

	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	"github.com/epifi/gamma/firefly/v2/dao/impl"
)

var CardRequestWireSet = wire.NewSet(impl.NewCardRequestDao, wire.Bind(new(CardRequestDao), new(*impl.CardRequestDao)))
var CreditCardWireSet = wire.NewSet(impl.NewCreditCardDao, wire.Bind(new(CreditCardDao), new(*impl.CreditCardDao)))
var CreditCardOffersWireSet = wire.NewSet(impl.NewCreditCardOfferDao, wire.Bind(new(CreditCardOffersDao), new(*impl.CreditCardOfferDao)))
var CreditCardTrackingInfoWireSet = wire.NewSet(impl.NewCardTrackingInfoDao, wire.Bind(new(CardTrackingInfoDao), new(*impl.CardTrackingInfoDao)))

type CardRequestDao interface {
	Create(ctx context.Context, request *ffBeV2Pb.CardRequest) (*ffBeV2Pb.CardRequest, error)
	Update(ctx context.Context, request *ffBeV2Pb.CardRequest, updateMasks []ccEnumsV2Pb.CardRequestFieldMask) error
	GetById(ctx context.Context, id string) (*ffBeV2Pb.CardRequest, error)
	GetByActorIdAndRequestType(ctx context.Context, actorId string, requestType ccEnumsV2Pb.CardRequestType) ([]*ffBeV2Pb.CardRequest, error)
	GetByExternalUserIdAndRequestType(ctx context.Context, extUserId string, requestType ccEnumsV2Pb.CardRequestType) ([]*ffBeV2Pb.CardRequest, error)
	GetByClientReqId(ctx context.Context, clientReqId string) (*ffBeV2Pb.CardRequest, error)
	UpdateIfUpdatedAtMatches(ctx context.Context, request *ffBeV2Pb.CardRequest, updateMasks []ccEnumsV2Pb.CardRequestFieldMask) error
}

type CreditCardDao interface {
	Create(ctx context.Context, request *ffBeV2Pb.CreditCard) (*ffBeV2Pb.CreditCard, error)
	Update(ctx context.Context, request *ffBeV2Pb.CreditCard, updateMasks []ccEnumsV2Pb.CreditCardFieldMask) error
	GetById(ctx context.Context, id string) (*ffBeV2Pb.CreditCard, error)
	GetByActorId(ctx context.Context, actorId string) ([]*ffBeV2Pb.CreditCard, error)
	GetByExternalUserId(ctx context.Context, extUserId string) ([]*ffBeV2Pb.CreditCard, error)
}

type CreditCardOffersDao interface {
	Create(ctx context.Context, ccOffer *ffBeV2Pb.CreditCardOffer) (*ffBeV2Pb.CreditCardOffer, error)
	Update(ctx context.Context, ccOffer *ffBeV2Pb.CreditCardOffer, updateMasks []ccEnumsV2Pb.CreditCardOfferFieldMask) error
	GetById(ctx context.Context, id string) (*ffBeV2Pb.CreditCardOffer, error)
	GetActiveOfferByActorIdAndVendorAndCardProgram(ctx context.Context, actorId string, vendor commonvgpb.Vendor,
		cardProgram string) (*ffBeV2Pb.CreditCardOffer, error)
	GetAllActiveOffersByActorId(ctx context.Context, actorId string) ([]*ffBeV2Pb.CreditCardOffer, error)
	CreateBatch(ctx context.Context, ccOffers []*ffBeV2Pb.CreditCardOffer, batchSize int) error
	GetOfferByActorIdAndVendor(ctx context.Context, actorId string, vendor commonvgpb.Vendor, orderByDesc bool) (*ffBeV2Pb.CreditCardOffer, error)
}

type CardTrackingInfoDao interface {
	Create(ctx context.Context, trackingRequest *ffBeV2Pb.CardTrackingInfo) (*ffBeV2Pb.CardTrackingInfo, error)
	Update(ctx context.Context, trackingRequest *ffBeV2Pb.CardTrackingInfo, updateMasks []ccEnumsV2Pb.CardTrackingInfoFieldMask) error
	GetById(ctx context.Context, id string) (*ffBeV2Pb.CardTrackingInfo, error)
	GetByActorAwbPrintingVendor(ctx context.Context, actorId string, awb string, vendor commonvgpb.Vendor) (*ffBeV2Pb.CardTrackingInfo, error)
}
