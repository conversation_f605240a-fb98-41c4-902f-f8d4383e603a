package recurringpayment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	mocks3 "github.com/epifi/be-common/pkg/lock/mocks"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/be-common/api/celestial"
	mockCelestial "github.com/epifi/be-common/api/celestial/mocks"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestial2 "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/recurringpayment/payload"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	"github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor"
	mocks2 "github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor/mocks"
	"github.com/epifi/gamma/recurringpayment/internal/validationrules"
	"github.com/epifi/gamma/recurringpayment/internal/validationrules/mocks"
)

func TestService_ExecuteRecurringPaymentV1(t *testing.T) {
	var (
		defaultRecurringPaymentId = "default-recurring-payment"
		defaultRecurringPayment   = &rpPb.RecurringPayment{
			Id:           defaultRecurringPaymentId,
			Type:         rpPb.RecurringPaymentType_ENACH_MANDATES,
			State:        rpPb.RecurringPaymentState_ACTIVATED,
			PaymentRoute: rpPb.RecurringPaymentRoute_RECURRING_PAYMENT_ROUTE_NATIVE,
		}
		defaultClientRequestId = "default-client-request-id"
		defaultAmount          = &moneyPb.Money{
			CurrencyCode: moneyPkg.RupeeCurrencyCode,
			Units:        100,
		}
		defaultDomainSpecificExecutionPayload   = &payload.RecurringPaymentTypeSpecificExecutionPayload{}
		defaultExecuteRecurringPaymentV1Request = &rpPb.ExecuteRecurringPaymentV1Request{
			RecurringPaymentId:                  defaultRecurringPaymentId,
			ClientRequestId:                     defaultClientRequestId,
			Amount:                              defaultAmount,
			RecurringPaymentTypeSpecificPayload: defaultDomainSpecificExecutionPayload,
		}
		defaultValidateReq = &validationrules.ValidateReq{
			Recurringpayment: defaultRecurringPayment,
			Amount:           defaultAmount,
		}
		defaultRecurringPaymentAction = &rpPb.RecurringPaymentsAction{
			RecurringPaymentId: defaultRecurringPaymentId,
			ClientRequestId:    defaultClientRequestId,
			Action:             rpPb.Action_EXECUTE,
			ActionMetadata: &rpPb.ActionMetadata{
				ExecuteActionMetadate: &rpPb.ExecuteActionMetaData{
					Amount: defaultAmount,
				},
			},
			State: rpPb.ActionState_ACTION_CREATED,
		}
		defaulRecurringPaymentActionId     = "execute-action-id"
		defaultRecurringPaymentActionWitId = &rpPb.RecurringPaymentsAction{
			Id:                 defaulRecurringPaymentActionId,
			RecurringPaymentId: defaultRecurringPaymentId,
			ClientRequestId:    defaultClientRequestId,
			Action:             rpPb.Action_EXECUTE,
			ActionMetadata: &rpPb.ActionMetadata{
				ExecuteActionMetadate: &rpPb.ExecuteActionMetaData{
					Amount: defaultAmount,
				},
			},
			State: rpPb.ActionState_ACTION_CREATED,
		}
		defaultWorkflowPayload, _ = protojson.Marshal(&payload.ExecuteRecurringPaymentWithoutAuthV1WorkflowPayload{
			RecurringPaymentId:                  defaultRecurringPaymentId,
			RecurringPaymentActionId:            defaulRecurringPaymentActionId,
			RecurringPaymentTypeSpecificPayload: defaultDomainSpecificExecutionPayload,
		})
	)
	type args struct {
		ctx context.Context
		req *rpPb.ExecuteRecurringPaymentV1Request
	}
	tests := []struct {
		name       string
		args       args
		want       *rpPb.ExecuteRecurringPaymentV1Response
		setupMocks func(actionDao *daoMocks.MockRecurringPaymentsActionDao, dao *daoMocks.MockRecurringPaymentDao, lockManager *mocks3.MockRwLockManager, lock *mocks3.MockILock, ivalidateRules *mocks.MockIValidationRule, executionFactory *mocks2.MockDomainExecutionProcessorFactory, executionProcessor *mocks2.MockDomainExecutionProcessor, celestialClient *mockCelestial.MockCelestialClient)
		wantErr    bool
	}{
		{
			name: "should pass (execution action with given client request id already exists)",
			args: args{
				ctx: context.Background(),
				req: defaultExecuteRecurringPaymentV1Request,
			},
			setupMocks: func(actionDao *daoMocks.MockRecurringPaymentsActionDao, dao *daoMocks.MockRecurringPaymentDao, lockManager *mocks3.MockRwLockManager, lock *mocks3.MockILock, ivalidateRules *mocks.MockIValidationRule, executionFactory *mocks2.MockDomainExecutionProcessorFactory, executionProcessor *mocks2.MockDomainExecutionProcessor, celestialClient *mockCelestial.MockCelestialClient) {
				actionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientRequestId, true).Return(defaultRecurringPaymentActionWitId, nil)
			},
			want: &rpPb.ExecuteRecurringPaymentV1Response{Status: rpc.StatusOk()},
		},
		{
			name: "should fail (error while fetching recurring payment action by client request id)",
			args: args{
				ctx: context.Background(),
				req: defaultExecuteRecurringPaymentV1Request,
			},
			setupMocks: func(actionDao *daoMocks.MockRecurringPaymentsActionDao, dao *daoMocks.MockRecurringPaymentDao, lockManager *mocks3.MockRwLockManager, lock *mocks3.MockILock, ivalidateRules *mocks.MockIValidationRule, executionFactory *mocks2.MockDomainExecutionProcessorFactory, executionProcessor *mocks2.MockDomainExecutionProcessor, celestialClient *mockCelestial.MockCelestialClient) {
				actionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientRequestId, true).Return(nil, errors.New("database is down"))
			},
			want: &rpPb.ExecuteRecurringPaymentV1Response{Status: rpc.StatusInternalWithDebugMsg("database is down")},
		},
		{
			name: "should fail (error while fetching recurring payment by id)",
			args: args{
				ctx: context.Background(),
				req: defaultExecuteRecurringPaymentV1Request,
			},
			setupMocks: func(actionDao *daoMocks.MockRecurringPaymentsActionDao, dao *daoMocks.MockRecurringPaymentDao, lockManager *mocks3.MockRwLockManager, lock *mocks3.MockILock, ivalidateRules *mocks.MockIValidationRule, executionFactory *mocks2.MockDomainExecutionProcessorFactory, executionProcessor *mocks2.MockDomainExecutionProcessor, celestialClient *mockCelestial.MockCelestialClient) {
				actionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientRequestId, true).Return(nil, epifierrors.ErrRecordNotFound)
				dao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(nil, errors.New("error while fetching recurring payment by id"))
			},
			want: &rpPb.ExecuteRecurringPaymentV1Response{Status: rpc.StatusInternalWithDebugMsg("error while fetching recurring payment by id")},
		},
		{
			name: "should fail (failed to take the lock)",
			args: args{
				ctx: context.Background(),
				req: defaultExecuteRecurringPaymentV1Request,
			},
			setupMocks: func(actionDao *daoMocks.MockRecurringPaymentsActionDao, dao *daoMocks.MockRecurringPaymentDao, lockManager *mocks3.MockRwLockManager, lock *mocks3.MockILock, ivalidateRules *mocks.MockIValidationRule, executionFactory *mocks2.MockDomainExecutionProcessorFactory, executionProcessor *mocks2.MockDomainExecutionProcessor, celestialClient *mockCelestial.MockCelestialClient) {
				actionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientRequestId, true).Return(nil, epifierrors.ErrRecordNotFound)
				dao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				lockManager.EXPECT().Lock(gomock.Any(), conf.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLock+":"+defaultRecurringPaymentId, conf.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLockLeaseDuration).Return(nil, errors.New("failed to aquire lock"))
			},
			want: &rpPb.ExecuteRecurringPaymentV1Response{Status: rpc.StatusInternalWithDebugMsg("failed to aquire the lock")},
		},
		{
			name: "should fail (some validation rule failed)",
			args: args{
				ctx: context.Background(),
				req: defaultExecuteRecurringPaymentV1Request,
			},
			setupMocks: func(actionDao *daoMocks.MockRecurringPaymentsActionDao, dao *daoMocks.MockRecurringPaymentDao, lockManager *mocks3.MockRwLockManager, lock *mocks3.MockILock, ivalidateRules *mocks.MockIValidationRule, executionFactory *mocks2.MockDomainExecutionProcessorFactory, executionProcessor *mocks2.MockDomainExecutionProcessor, celestialClient *mockCelestial.MockCelestialClient) {
				actionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientRequestId, true).Return(nil, epifierrors.ErrRecordNotFound)
				dao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				lockManager.EXPECT().Lock(gomock.Any(), conf.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLock+":"+defaultRecurringPaymentId, conf.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLockLeaseDuration).Return(lock, nil)
				lock.EXPECT().Release(gomock.Any()).Return(nil)
				ivalidateRules.EXPECT().Validate(gomock.Any(), defaultValidateReq).Return(errors.New("validation failed"))
			},
			want: &rpPb.ExecuteRecurringPaymentV1Response{Status: rpc.StatusFailedPreconditionWithDebugMsg("validation failed")},
		},
		{
			name: "should fail (error while creating recurring payment execution action)",
			args: args{
				ctx: context.Background(),
				req: defaultExecuteRecurringPaymentV1Request,
			},
			setupMocks: func(actionDao *daoMocks.MockRecurringPaymentsActionDao, dao *daoMocks.MockRecurringPaymentDao, lockManager *mocks3.MockRwLockManager, lock *mocks3.MockILock, ivalidateRules *mocks.MockIValidationRule, executionFactory *mocks2.MockDomainExecutionProcessorFactory, executionProcessor *mocks2.MockDomainExecutionProcessor, celestialClient *mockCelestial.MockCelestialClient) {
				actionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientRequestId, true).Return(nil, epifierrors.ErrRecordNotFound)
				dao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				lockManager.EXPECT().Lock(gomock.Any(), conf.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLock+":"+defaultRecurringPaymentId, conf.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLockLeaseDuration).Return(lock, nil)
				lock.EXPECT().Release(gomock.Any()).Return(nil)
				ivalidateRules.EXPECT().Validate(gomock.Any(), defaultValidateReq).Return(nil)
				actionDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentAction).Return(nil, errors.New("failed to create execution action"))
			},
			want: &rpPb.ExecuteRecurringPaymentV1Response{Status: rpc.StatusInternalWithDebugMsg("error while creating recurring payment execution action")},
		},
		{
			name: "should fail (error while fetching the processor from execution factory)",
			args: args{
				ctx: context.Background(),
				req: defaultExecuteRecurringPaymentV1Request,
			},
			setupMocks: func(actionDao *daoMocks.MockRecurringPaymentsActionDao, dao *daoMocks.MockRecurringPaymentDao, lockManager *mocks3.MockRwLockManager, lock *mocks3.MockILock, ivalidateRules *mocks.MockIValidationRule, executionFactory *mocks2.MockDomainExecutionProcessorFactory, executionProcessor *mocks2.MockDomainExecutionProcessor, celestialClient *mockCelestial.MockCelestialClient) {
				actionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientRequestId, true).Return(nil, epifierrors.ErrRecordNotFound)
				dao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				lockManager.EXPECT().Lock(gomock.Any(), conf.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLock+":"+defaultRecurringPaymentId, conf.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLockLeaseDuration).Return(lock, nil)
				lock.EXPECT().Release(gomock.Any()).Return(nil)
				ivalidateRules.EXPECT().Validate(gomock.Any(), defaultValidateReq).Return(nil)
				actionDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentAction).Return(defaultRecurringPaymentActionWitId, nil)
				executionFactory.EXPECT().GetProcessor(defaultRecurringPayment.GetType(), defaultRecurringPayment.GetPaymentRoute()).Return(nil, epifierrors.ErrInvalidArgument)
			},
			want: &rpPb.ExecuteRecurringPaymentV1Response{Status: rpc.StatusInternalWithDebugMsg("error while checking if auth is required")},
		},
		{
			name: "should fail (error while checking if auth is required)",
			args: args{
				ctx: context.Background(),
				req: defaultExecuteRecurringPaymentV1Request,
			},
			setupMocks: func(actionDao *daoMocks.MockRecurringPaymentsActionDao, dao *daoMocks.MockRecurringPaymentDao, lockManager *mocks3.MockRwLockManager, lock *mocks3.MockILock, ivalidateRules *mocks.MockIValidationRule, executionFactory *mocks2.MockDomainExecutionProcessorFactory, executionProcessor *mocks2.MockDomainExecutionProcessor, celestialClient *mockCelestial.MockCelestialClient) {
				actionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientRequestId, true).Return(nil, epifierrors.ErrRecordNotFound)
				dao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				lockManager.EXPECT().Lock(gomock.Any(), conf.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLock+":"+defaultRecurringPaymentId, conf.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLockLeaseDuration).Return(lock, nil)
				lock.EXPECT().Release(gomock.Any()).Return(nil)
				ivalidateRules.EXPECT().Validate(gomock.Any(), defaultValidateReq).Return(nil)
				actionDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentAction).Return(defaultRecurringPaymentActionWitId, nil)
				executionFactory.EXPECT().GetProcessor(defaultRecurringPayment.GetType(), defaultRecurringPayment.GetPaymentRoute()).Return(executionProcessor, nil)
				executionProcessor.EXPECT().IsAuthRequired(gomock.Any(), &domainexecutionprocessor.IsAuthRequiredReq{
					RecurringPaymentId: defaultRecurringPaymentId,
					Payload:            defaultDomainSpecificExecutionPayload,
				}).Return(false, errors.New("error while checking if auth is required"))
			},
			want: &rpPb.ExecuteRecurringPaymentV1Response{Status: rpc.StatusInternalWithDebugMsg("error while checking if auth is required")},
		},
		{
			name: "should fail (error while initiating worfklow)",
			args: args{
				ctx: context.Background(),
				req: defaultExecuteRecurringPaymentV1Request,
			},
			setupMocks: func(actionDao *daoMocks.MockRecurringPaymentsActionDao, dao *daoMocks.MockRecurringPaymentDao, lockManager *mocks3.MockRwLockManager, lock *mocks3.MockILock, ivalidateRules *mocks.MockIValidationRule, executionFactory *mocks2.MockDomainExecutionProcessorFactory, executionProcessor *mocks2.MockDomainExecutionProcessor, celestialClient *mockCelestial.MockCelestialClient) {
				actionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientRequestId, true).Return(nil, epifierrors.ErrRecordNotFound)
				dao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				lockManager.EXPECT().Lock(gomock.Any(), conf.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLock+":"+defaultRecurringPaymentId, conf.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLockLeaseDuration).Return(lock, nil)
				lock.EXPECT().Release(gomock.Any()).Return(nil)
				ivalidateRules.EXPECT().Validate(gomock.Any(), defaultValidateReq).Return(nil)
				actionDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentAction).Return(defaultRecurringPaymentActionWitId, nil)
				executionFactory.EXPECT().GetProcessor(defaultRecurringPayment.GetType(), defaultRecurringPayment.GetPaymentRoute()).Return(executionProcessor, nil)
				executionProcessor.EXPECT().IsAuthRequired(gomock.Any(), &domainexecutionprocessor.IsAuthRequiredReq{
					RecurringPaymentId: defaultRecurringPaymentId,
					Payload:            defaultDomainSpecificExecutionPayload,
				}).Return(false, nil)
				celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), &celestial.InitiateWorkflowRequest{
					Params: &celestial.WorkflowCreationRequestParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id:     defaulRecurringPaymentActionId,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
						Payload:          defaultWorkflowPayload,
						Ownership:        commontypes.Ownership_EPIFI_TECH,
						QualityOfService: celestial.QoS_GUARANTEED,
						Version:          workflowPb.Version_V0,
						Type:             celestial2.GetTypeEnumFromWorkflowType(rpNs.ExecuteRecurringPaymentWithoutAuthV1),
					},
				}).Return(&celestial.InitiateWorkflowResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: &rpPb.ExecuteRecurringPaymentV1Response{Status: rpc.StatusInternalWithDebugMsg("failed to initialise the workflow")},
		},
		{
			name: "should successfully execute the recurring payment",
			args: args{
				ctx: context.Background(),
				req: defaultExecuteRecurringPaymentV1Request,
			},
			setupMocks: func(actionDao *daoMocks.MockRecurringPaymentsActionDao, dao *daoMocks.MockRecurringPaymentDao, lockManager *mocks3.MockRwLockManager, lock *mocks3.MockILock, ivalidateRules *mocks.MockIValidationRule, executionFactory *mocks2.MockDomainExecutionProcessorFactory, executionProcessor *mocks2.MockDomainExecutionProcessor, celestialClient *mockCelestial.MockCelestialClient) {
				actionDao.EXPECT().GetByClientRequestId(gomock.Any(), defaultClientRequestId, true).Return(nil, epifierrors.ErrRecordNotFound)
				dao.EXPECT().GetById(gomock.Any(), defaultRecurringPaymentId).Return(defaultRecurringPayment, nil)
				lockManager.EXPECT().Lock(gomock.Any(), conf.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLock+":"+defaultRecurringPaymentId, conf.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLockLeaseDuration).Return(lock, nil)
				lock.EXPECT().Release(gomock.Any()).Return(nil)
				ivalidateRules.EXPECT().Validate(gomock.Any(), defaultValidateReq).Return(nil)
				actionDao.EXPECT().Create(gomock.Any(), defaultRecurringPaymentAction).Return(defaultRecurringPaymentActionWitId, nil)
				executionFactory.EXPECT().GetProcessor(defaultRecurringPayment.GetType(), defaultRecurringPayment.GetPaymentRoute()).Return(executionProcessor, nil)
				executionProcessor.EXPECT().IsAuthRequired(gomock.Any(), &domainexecutionprocessor.IsAuthRequiredReq{
					RecurringPaymentId: defaultRecurringPaymentId,
					Payload:            defaultDomainSpecificExecutionPayload,
				}).Return(false, nil)
				celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), &celestial.InitiateWorkflowRequest{
					Params: &celestial.WorkflowCreationRequestParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id:     defaulRecurringPaymentActionId,
							Client: workflowPb.Client_RECURRING_PAYMENT,
						},
						Payload:          defaultWorkflowPayload,
						Ownership:        commontypes.Ownership_EPIFI_TECH,
						QualityOfService: celestial.QoS_GUARANTEED,
						Version:          workflowPb.Version_V0,
						Type:             celestial2.GetTypeEnumFromWorkflowType(rpNs.ExecuteRecurringPaymentWithoutAuthV1),
					},
				}).Return(&celestial.InitiateWorkflowResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &rpPb.ExecuteRecurringPaymentV1Response{Status: rpc.StatusOk()},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			actionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
			dao := daoMocks.NewMockRecurringPaymentDao(ctr)
			ivalidateRules := mocks.NewMockIValidationRule(ctr)
			executionFactory := mocks2.NewMockDomainExecutionProcessorFactory(ctr)
			executionProcessor := mocks2.NewMockDomainExecutionProcessor(ctr)
			celestialClient := mockCelestial.NewMockCelestialClient(ctr)
			mockIlockManager := mocks3.NewMockRwLockManager(ctr)
			mockIlock := mocks3.NewMockILock(ctr)

			mockRecurringPaymentVendorDetailsDao := daoMocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)
			s := NewService(dao, nil, nil, actionDao, nil, nil, nil, nil, nil, conf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockIlockManager, nil, nil, nil, nil, nil, nil, celestialClient, nil, executionFactory, ivalidateRules, nil, nil, nil, nil, mockRecurringPaymentVendorDetailsDao, nil, nil, nil, nil, nil)
			tt.setupMocks(actionDao, dao, mockIlockManager, mockIlock, ivalidateRules, executionFactory, executionProcessor, celestialClient)
			got, err := s.ExecuteRecurringPaymentV1(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExecuteRecurringPaymentV1() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("ExecuteRecurringPaymentV1() got = %v, want %v", got, tt.want)
			}
		})
	}
}
