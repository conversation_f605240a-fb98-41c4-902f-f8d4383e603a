package recurringpayment

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"

	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	domainPb "github.com/epifi/gamma/api/order/domain"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	pb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/recurringpayment/standinginstruction/mocks"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	mocks3 "github.com/epifi/gamma/api/upi/mandate/mocks"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	mocks2 "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

func TestService_ProcessRecurringPaymentRevoke(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentsActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockSIClient := mocks.NewMockStandingInstructionServiceClient(ctr)
	mockRecurringPaymentProcessor := mocks2.NewMockRecurringPaymentProcessor(ctr)
	mockActor := actorMocks.NewMockActorClient(ctr)
	mockMandateClient := mocks3.NewMockMandateServiceClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)

	svc := NewService(mockRecurringPaymentDao, mockOrderClient, mockSIClient, mockRecurringPaymentsActionDao, nil, mockActor, nil, nil, nil, conf, mockRecurringPaymentProcessor, mockMandateClient, nil, mockPiClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	payload := &pb.RecurringPaymentRevokeInfo{
		RecurringPaymentId: "rp-1",
		RequestId:          "request-id-1",
		ClientRequestId:    "client-req-1",
		InitiatedBy:        pb.InitiatedBy_PAYER,
	}
	revokedRecurringPayment := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		State:       pb.RecurringPaymentState_REVOKED,
	}
	activatedRecurringPayment := &pb.RecurringPayment{

		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		State:       pb.RecurringPaymentState_ACTIVATED,
	}
	revokeQueuedRecurringPayment := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		State:       pb.RecurringPaymentState_REVOKE_QUEUED,
		InitiatedBy: pb.InitiatedBy_PAYER,
	}

	revokeInitiatedMandate := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		State:       pb.RecurringPaymentState_REVOKE_INITIATED,
		InitiatedBy: pb.InitiatedBy_PAYER,
	}
	action1 := &pb.RecurringPaymentsAction{
		State:     pb.ActionState_ACTION_CREATED,
		Action:    pb.Action_REVOKE,
		CreatedAt: timestampPb.New(time.Now().Add(-65 * time.Minute)),
	}
	action2 := &pb.RecurringPaymentsAction{
		State: pb.ActionState_ACTION_CREATED,
	}

	marshalledPayload, _ := protojson.Marshal(payload)
	tests := []struct {
		name           string
		req            *domainPb.ProcessFulfilmentRequest
		setupMockCalls func()
		want           *domainPb.ProcessFulfilmentResponse
		wantErr        bool
	}{
		{
			name: "already revoked",
			req: &domainPb.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(revokedRecurringPayment, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, nil)
			},
			want:    &domainPb.ProcessFulfilmentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_SUCCESS}},
			wantErr: false,
		},
		{
			name: "activated rp",
			req: &domainPb.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(activatedRecurringPayment, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(nil, nil)
			},
			want:    &domainPb.ProcessFulfilmentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE}},
			wantErr: false,
		},
		{
			name: "revoke still in queued and time less than threshold",
			req: &domainPb.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(revokeQueuedRecurringPayment, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					State:     pb.ActionState_ACTION_CREATED,
					Action:    pb.Action_REVOKE,
					CreatedAt: timestampPb.New(time.Now()),
				}, nil)
			},
			want:    &domainPb.ProcessFulfilmentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_NO_OP}},
			wantErr: false,
		},
		{
			name: "revoke still in queued and time greater than threshold",
			req: &domainPb.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(revokeQueuedRecurringPayment, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action1, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), revokeQueuedRecurringPayment, nil,
					pb.RecurringPaymentState_REVOKE_QUEUED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), action1, nil,
					pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_FAILURE).Return(nil)
			},
			want:    &domainPb.ProcessFulfilmentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE}},
			wantErr: false,
		},
		{
			name: "revoke initiated moving to SUCCESS after checking status from domain service for mandate",
			req: &domainPb.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(revokeInitiatedMandate, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(action2, nil)
				mockMandateClient.EXPECT().FetchAndUpdateRequestStatus(gomock.Any(), &upiMandatePb.FetchAndUpdateRequestStatusRequest{
					ReqId:           "request-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					EpifiCustomerPi: "pi-1",
				}).
					Return(&upiMandatePb.FetchAndUpdateRequestStatusResponse{
						Status: rpc.StatusOk(),
					}, nil)
				mockPiClient.EXPECT().GetPIsByIds(gomock.Any(), &piPb.GetPIsByIdsRequest{Ids: []string{
					"pi-1",
					"pi-2",
				}}).Return(&piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Id:                   "pi-1",
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
						},
					},
				}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), revokeInitiatedMandate, nil,
					pb.RecurringPaymentState_REVOKE_INITIATED, pb.RecurringPaymentState_REVOKED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), action2, nil,
					pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
			},
			want:    &domainPb.ProcessFulfilmentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_SUCCESS}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.ProcessRecurringPaymentRevoke(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessRecurringPaymentRevoke() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessRecurringPaymentRevoke() got = %v, want %v", got, tt.want)
			}
		})
	}
}
