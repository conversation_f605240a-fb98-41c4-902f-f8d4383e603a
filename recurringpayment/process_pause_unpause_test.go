package recurringpayment

import (
	"context"
	"reflect"
	"testing"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"

	domainPb "github.com/epifi/gamma/api/order/domain"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	pb "github.com/epifi/gamma/api/recurringpayment"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	"github.com/epifi/gamma/api/upi/mandate/mocks"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
)

func TestService_ProcessPauseUnpause(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentsActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockUpiMandateClient := mocks.NewMockMandateServiceClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)

	svc := NewService(mockRecurringPaymentDao, nil, nil, mockRecurringPaymentsActionDao, nil, nil, nil, nil, nil, conf, nil, mockUpiMandateClient, nil, mockPiClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	payload := &pb.RecurringPaymentPauseUnpauseInfo{
		RecurringPaymentId: "rp-1",
		ClientRequestId:    "client-req-1",
	}
	marshalledPayload, _ := protojson.Marshal(payload)

	tests := []struct {
		name           string
		req            *domainPb.ProcessFulfilmentRequest
		setupMockCalls func()
		want           *domainPb.ProcessFulfilmentResponse
		wantErr        bool
	}{
		{
			name: "recurring payment already paused",
			req: &domainPb.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:    "rp-1",
					State: pb.RecurringPaymentState_PAUSED,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_PAUSE,
					State:              pb.ActionState_ACTION_CREATED,
				}, nil)
			},
			want:    &domainPb.ProcessFulfilmentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_SUCCESS}},
			wantErr: false,
		},
		{
			name: "fetched status successfully",
			req: &domainPb.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:          "rp-1",
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					State:       pb.RecurringPaymentState_PAUSE_AUTHORISED,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Type:        pb.RecurringPaymentType_UPI_MANDATES,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_PAUSE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "req-1",
				}, nil)
				mockPiClient.EXPECT().GetPIsByIds(gomock.Any(), &piPb.GetPIsByIdsRequest{Ids: []string{"pi-1", "pi-2"}}).Return(&piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Id:                   "pi-1",
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
						},
					},
				}, nil)
				mockUpiMandateClient.EXPECT().FetchAndUpdateRequestStatus(gomock.Any(), &upiMandatePb.FetchAndUpdateRequestStatusRequest{
					ReqId:           "req-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					EpifiCustomerPi: "pi-1",
				}).Return(&upiMandatePb.FetchAndUpdateRequestStatusResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPayment{
					Id:          "rp-1",
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					State:       pb.RecurringPaymentState_PAUSE_AUTHORISED,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Type:        pb.RecurringPaymentType_UPI_MANDATES,
				}, nil, pb.RecurringPaymentState_PAUSE_AUTHORISED, pb.RecurringPaymentState_PAUSED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_PAUSE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "req-1",
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
			},
			want:    &domainPb.ProcessFulfilmentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_SUCCESS}},
			wantErr: false,
		},
		{
			name: "permanent error in fetching status successfully",
			req: &domainPb.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:          "rp-1",
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					State:       pb.RecurringPaymentState_PAUSE_AUTHORISED,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Type:        pb.RecurringPaymentType_UPI_MANDATES,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_PAUSE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "req-1",
				}, nil)
				mockPiClient.EXPECT().GetPIsByIds(gomock.Any(), &piPb.GetPIsByIdsRequest{Ids: []string{"pi-1", "pi-2"}}).Return(&piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Id:                   "pi-1",
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
						},
					},
				}, nil)
				mockUpiMandateClient.EXPECT().FetchAndUpdateRequestStatus(gomock.Any(), &upiMandatePb.FetchAndUpdateRequestStatusRequest{
					ReqId:           "req-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					EpifiCustomerPi: "pi-1",
				}).Return(&upiMandatePb.FetchAndUpdateRequestStatusResponse{Status: rpc.NewStatusWithoutDebug(uint32(upiMandatePb.FetchAndUpdateRequestStatusResponse_PERMANENT_FAILURE), "")}, nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPayment{
					Id:          "rp-1",
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					State:       pb.RecurringPaymentState_PAUSE_AUTHORISED,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Type:        pb.RecurringPaymentType_UPI_MANDATES,
				}, nil, pb.RecurringPaymentState_PAUSE_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockRecurringPaymentsActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_PAUSE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "req-1",
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_FAILURE).Return(nil)
			},
			want:    &domainPb.ProcessFulfilmentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE}},
			wantErr: false,
		},
		{
			name: "status in progress",
			req: &domainPb.ProcessFulfilmentRequest{
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(&pb.RecurringPayment{
					Id:          "rp-1",
					PiFrom:      "pi-1",
					PiTo:        "pi-2",
					State:       pb.RecurringPaymentState_PAUSE_AUTHORISED,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Type:        pb.RecurringPaymentType_UPI_MANDATES,
				}, nil)
				mockRecurringPaymentsActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_PAUSE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "req-1",
				}, nil)
				mockPiClient.EXPECT().GetPIsByIds(gomock.Any(), &piPb.GetPIsByIdsRequest{Ids: []string{"pi-1", "pi-2"}}).Return(&piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Id:                   "pi-1",
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
						},
					},
				}, nil)
				mockUpiMandateClient.EXPECT().FetchAndUpdateRequestStatus(gomock.Any(), &upiMandatePb.FetchAndUpdateRequestStatusRequest{
					ReqId:           "req-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					EpifiCustomerPi: "pi-1",
				}).Return(&upiMandatePb.FetchAndUpdateRequestStatusResponse{Status: rpc.NewStatusWithoutDebug(uint32(upiMandatePb.FetchAndUpdateRequestStatusResponse_IN_PROGRESS), "")}, nil)
			},
			want:    &domainPb.ProcessFulfilmentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_IN_PROGRESS}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.ProcessPauseUnpause(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessPauseUnpause() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessPauseUnpause() got = %v, want %v", got, tt.want)
			}
		})
	}
}
