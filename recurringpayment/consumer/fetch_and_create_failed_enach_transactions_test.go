package consumer

import (
	"context"
	"testing"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/golang/mock/gomock"
	"github.com/jonboulle/clockwork"
	"github.com/stretchr/testify/require"
	datePb "google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	idgenMocks "github.com/epifi/be-common/pkg/idgen/mocks"

	actorPb "github.com/epifi/gamma/api/actor"
	mockActorPb "github.com/epifi/gamma/api/actor/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	mockOrderPb "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	mockPi "github.com/epifi/gamma/api/paymentinstrument/mocks"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpConsumerPb "github.com/epifi/gamma/api/recurringpayment/consumer"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	mockEnachPb "github.com/epifi/gamma/api/recurringpayment/enach/mocks"
	mockRpPb "github.com/epifi/gamma/api/recurringpayment/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	mockSavingsPb "github.com/epifi/gamma/api/savings/mocks"
	timelinePb "github.com/epifi/gamma/api/timeline"
	timelineMocks "github.com/epifi/gamma/api/timeline/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	mockVgMerchantPb "github.com/epifi/gamma/api/vendorgateway/merchantresolution/mocks"
	vgEnachPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	vgEnachEnumsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/enums"
	mocks2 "github.com/epifi/gamma/api/vendorgateway/openbanking/enach/mocks"

	actorProcessor "github.com/epifi/gamma/recurringpayment/internal/actor"
	mock_internal "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

type testArgs struct {
	ctx       context.Context
	actorID   string
	accountNo string
	umrn      string
	userName  string
	fromDate  *datePb.Date
	toDate    *datePb.Date
}

func TestConsumerService_FetchAndCreateFailedEnachTransactions(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)
	defer ctr.Finish()

	curTime := time.Now()

	// Create mocks for all dependencies
	mockEnachVgClient := mocks2.NewMockEnachClient(ctr)
	mockEnachClient := mockEnachPb.NewMockEnachServiceClient(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockOrderClient := mockOrderPb.NewMockOrderServiceClient(ctr)
	mockActorProcessor := mock_internal.NewMockActorProcessor(ctr)
	mockSavingsClient := mockSavingsPb.NewMockSavingsClient(ctr)
	mockPiClient := mockPi.NewMockPiClient(ctr)
	mockMerchantResolutionClient := mockVgMerchantPb.NewMockMerchantResolutionServer(ctr)
	mockUuidGen := idgenMocks.NewMockIUuidGenerator(ctr)
	mockClock := clockwork.NewFakeClockAt(curTime)
	mockRecurringPaymentClient := mockRpPb.NewMockRecurringPaymentServiceClient(ctr)
	mockActorClient := mockActorPb.NewMockActorClient(ctr)

	// Create test service with mocks
	service := &ConsumerService{
		gconf:                    gconf,
		enachVgClient:            mockEnachVgClient,
		enachClient:              mockEnachClient,
		timelineClient:           mockTimelineClient,
		orderClient:              mockOrderClient,
		actorProcessor:           mockActorProcessor,
		savingsClient:            mockSavingsClient,
		piClient:                 mockPiClient,
		merchantResolutionClient: mockMerchantResolutionClient,
		uuidGen:                  mockUuidGen,
		clock:                    mockClock,
		recurringPaymentClient:   mockRecurringPaymentClient,
		actorClient:              mockActorClient,
	}

	// Setup common test arguments
	now := time.Now()
	commonArgs := testArgs{
		ctx:       context.Background(),
		actorID:   "test-actor-id",
		accountNo: "test-account-no",
		umrn:      "test-umrn",
		userName:  "test-merchant",
		fromDate: &datePb.Date{
			Year:  int32(now.Add(-24 * time.Hour).Year()),
			Month: int32(now.Add(-24 * time.Hour).Month()),
			Day:   int32(now.Add(-24 * time.Hour).Day()),
		},
		toDate: &datePb.Date{
			Year:  int32(now.Year()),
			Month: int32(now.Month()),
			Day:   int32(now.Day()),
		},
	}

	testCases := []struct {
		name          string
		args          testArgs
		setupMocks    func(args testArgs)
		expectedError bool
		expectedRes   *rpConsumerPb.ConsumerResponse
	}{
		{
			name: "Should successfully create txn for failed ENACH transactions",
			args: commonArgs,
			setupMocks: func(args testArgs) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     args.actorID,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpcPb.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						Id:          "sa-id-1",
						AccountNo:   args.accountNo,
						ActorId:     args.actorID,
						IfscCode:    "ifsc-1",
						State:       savingsPb.State_CREATED,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil)

				// Mock FetchEnachTransactions
				mockEnachVgClient.EXPECT().
					FetchEnachTransactions(gomock.Any(), &vgEnachPb.FetchEnachTransactionsRequest{
						Header:            &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
						TransactionFilter: &vgEnachPb.FetchEnachTransactionsRequest_AccountNumber{AccountNumber: args.accountNo},
						FromDate:          args.fromDate,
						ToDate:            args.toDate,
					}).
					Return(&vgEnachPb.FetchEnachTransactionsResponse{
						Status: rpcPb.StatusOk(),
						Transactions: []*vgEnachPb.EnachTransaction{
							{
								Umrn:                 args.umrn,
								UserName:             args.userName,
								TransactionReference: "txn-ref-1",
								Amount:               &moneyPb.Money{CurrencyCode: "INR", Units: 100, Nanos: 0},
								RequestTimestamp:     timestamppb.New(curTime.Add(-1 * time.Hour)),
								ResponseStatus: &vgEnachPb.TransactionStatus{
									Status:             vgEnachEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILED,
									DetailedStatusCode: "ENA9999",
								},
							},
						},
					}, nil)

				// Mock GetEnachMandate
				mockEnachClient.EXPECT().
					GetEnachMandate(gomock.Any(), &enachPb.GetEnachMandateRequest{
						Identifier: &enachPb.GetEnachMandateRequest_Umrn{
							Umrn: args.umrn,
						},
					}).
					Return(&enachPb.GetEnachMandateResponse{
						Status: rpcPb.StatusOk(),
						EnachMandate: &enachPb.EnachMandate{
							Id:                 "mandate-id-1",
							RecurringPaymentId: "rp-id-1",
							Umrn:               args.umrn,
						},
					}, nil)

				mockPiClient.EXPECT().GetPi(gomock.Any(), &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_AccountRequestParams_{
						AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: args.accountNo,
							IfscCode:            "ifsc-1",
						},
					},
				}).Return(&piPb.GetPiResponse{
					Status: rpcPb.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "pi-id-1",
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: args.accountNo,
								Name:                "user-name-1",
							},
						},
					},
				}, nil)

				// Mock fetchMerchantActorEntityForEnach calls
				mockRecurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), &rpPb.GetRecurringPaymentByIdRequest{
					Id: "rp-id-1",
				}).Return(&rpPb.GetRecurringPaymentByIdResponse{
					Status: rpcPb.StatusOk(),
					RecurringPayment: &rpPb.RecurringPayment{
						ToActorId: "merchant-actor-id",
					},
				}, nil)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "merchant-actor-id",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpcPb.StatusOk(),
					EntityId: "merchant-entity-id",
					Name:     &commontypes.Name{FirstName: "Netflix", LastName: "Inc"},
					Type:     types.ActorType_EXTERNAL_MERCHANT,
				}, nil)

				// Mock ResolveOtherActorAndPiForOffAppRecurringPayment
				mockActorProcessor.EXPECT().
					ResolveOtherActorAndPiForOffAppRecurringPayment(
						gomock.Any(),
						rpPb.RecurringPaymentType_ENACH_MANDATES,
						&actorProcessor.ResolveOtherActorAndPiForOffAppRecurringPaymentReq{
							InternalActorId: args.actorID,
							EnachPayload: &actorProcessor.EnachPayloadToResolveActorAndPi{
								OrgName: "Netflix Inc",
							},
						},
					).
					Return(&piPb.PaymentInstrument{
						Id:                   "pi-1",
						Type:                 piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier:           &piPb.PaymentInstrument_Account{},
						VerifiedName:         "netflix",
						State:                0,
						Capabilities:         nil,
						CreatedAt:            nil,
						UpdatedAt:            nil,
						DeletedAt:            nil,
						IssuerClassification: 0,
						Ownership:            0,
						LastVerifiedAt:       nil,
					}, "other-actor-id", nil)

				// Mock Create Timeline
				mockTimelineClient.EXPECT().
					Create(gomock.Any(), &timelinePb.CreateRequest{
						PrimaryActorId:     args.actorID,
						SecondaryActorId:   "other-actor-id",
						PrimaryActorName:   "user-name-1",
						SecondaryActorName: "netflix",
						Ownership:          timelinePb.Ownership_EPIFI_TECH,
					}).
					Return(&timelinePb.CreateResponse{
						Status: rpcPb.StatusOk(),
					}, nil)

				mockUuidGen.EXPECT().GenerateUuid().Return("client-req-id-1")

				// Mock CreateOrderWithTransaction
				mockOrderClient.EXPECT().
					CreateOrderWithTransaction(gomock.Any(), &orderPb.CreateOrderWithTransactionRequest{
						OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
							ActorFrom:   "test-actor-id",
							ActorTo:     "other-actor-id",
							Workflow:    orderPb.OrderWorkflow_NO_OP,
							Provenance:  orderPb.OrderProvenance_EXTERNAL,
							Status:      orderPb.OrderStatus_PAYMENT_FAILED,
							ClientReqId: "client-req-id-1",
						},
						TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
							PiFrom:          "pi-id-1",
							PiTo:            "pi-1",
							Remarks:         "UMRN: test-umrn",
							PaymentProtocol: paymentPb.PaymentProtocol_ENACH,
							Status:          paymentPb.TransactionStatus_FAILED,
							ProtocolStatus:  0,
							ReqInfo:         nil,
							ExecutedAt:      timestamppb.New(curTime.Add(-1 * time.Hour)),
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										StatusCodePayer:        "ENA9999",
										StatusDescriptionPayer: "Unknown Status",
										StatusCodePayee:        "",
										StatusDescriptionPayee: "",
										ErrorCategory:          0,
										CreatedAt:              timestamppb.New(curTime),
										State:                  paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE,
										Api:                    paymentPb.TransactionDetailedStatus_DetailedStatus_ENACH_FAILED_TXN_SYNC,
									},
								},
							},
							Utr:       "txn-ref-1",
							Ownership: commontypes.Ownership_EPIFI_TECH,
							OverrideDedupeId: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams_DedupeId{
								OverrideDedupeTime: timestamppb.New(curTime.Add(-1 * time.Hour)),
							},
						},
						Amount:                    &moneyPb.Money{CurrencyCode: "INR", Units: 100, Nanos: 0},
						DisableWorkflowProcessing: true,
					}).
					Return(&orderPb.CreateOrderWithTransactionResponse{
						Status:      rpcPb.StatusOk(),
						Order:       &orderPb.Order{},
						Transaction: &paymentPb.Transaction{},
					}, nil)
			},
			expectedError: false,
			expectedRes:   getSuccessRes(),
		},
		{
			name: "Should handle transaction already exists gracefully",
			args: commonArgs,
			setupMocks: func(args testArgs) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     args.actorID,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpcPb.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						Id:          "sa-id-1",
						AccountNo:   args.accountNo,
						ActorId:     args.actorID,
						IfscCode:    "ifsc-1",
						State:       savingsPb.State_CREATED,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil)

				// Mock FetchEnachTransactions
				mockEnachVgClient.EXPECT().
					FetchEnachTransactions(gomock.Any(), &vgEnachPb.FetchEnachTransactionsRequest{
						Header:            &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
						TransactionFilter: &vgEnachPb.FetchEnachTransactionsRequest_AccountNumber{AccountNumber: args.accountNo},
						FromDate:          args.fromDate,
						ToDate:            args.toDate,
					}).
					Return(&vgEnachPb.FetchEnachTransactionsResponse{
						Status: rpcPb.StatusOk(),
						Transactions: []*vgEnachPb.EnachTransaction{
							{
								Umrn:                 args.umrn,
								UserName:             args.userName,
								TransactionReference: "txn-ref-1",
								Amount:               &moneyPb.Money{CurrencyCode: "INR", Units: 100, Nanos: 0},
								RequestTimestamp:     timestamppb.New(curTime.Add(-1 * time.Hour)),
								ResponseStatus: &vgEnachPb.TransactionStatus{
									Status:             vgEnachEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILED,
									DetailedStatusCode: "ENA9999",
								},
							},
						},
					}, nil)

				// Mock GetEnachMandate
				mockEnachClient.EXPECT().
					GetEnachMandate(gomock.Any(), &enachPb.GetEnachMandateRequest{
						Identifier: &enachPb.GetEnachMandateRequest_Umrn{
							Umrn: args.umrn,
						},
					}).
					Return(&enachPb.GetEnachMandateResponse{
						Status: rpcPb.StatusOk(),
						EnachMandate: &enachPb.EnachMandate{
							Id:                 "mandate-id-1",
							RecurringPaymentId: "rp-id-1",
							Umrn:               args.umrn,
						},
					}, nil)

				mockPiClient.EXPECT().GetPi(gomock.Any(), &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_AccountRequestParams_{
						AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: args.accountNo,
							IfscCode:            "ifsc-1",
						},
					},
				}).Return(&piPb.GetPiResponse{
					Status: rpcPb.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "pi-id-1",
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: args.accountNo,
								Name:                "user-name-1",
							},
						},
					},
				}, nil)

				// Mock fetchMerchantActorEntityForEnach calls
				mockRecurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), &rpPb.GetRecurringPaymentByIdRequest{
					Id: "rp-id-1",
				}).Return(&rpPb.GetRecurringPaymentByIdResponse{
					Status: rpcPb.StatusOk(),
					RecurringPayment: &rpPb.RecurringPayment{
						ToActorId: "merchant-actor-id",
					},
				}, nil)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "merchant-actor-id",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpcPb.StatusOk(),
					EntityId: "merchant-entity-id",
					Name:     &commontypes.Name{FirstName: "Netflix", LastName: "Inc"},
					Type:     types.ActorType_EXTERNAL_MERCHANT,
				}, nil)

				// Mock ResolveOtherActorAndPiForOffAppRecurringPayment
				mockActorProcessor.EXPECT().
					ResolveOtherActorAndPiForOffAppRecurringPayment(
						gomock.Any(),
						rpPb.RecurringPaymentType_ENACH_MANDATES,
						&actorProcessor.ResolveOtherActorAndPiForOffAppRecurringPaymentReq{
							InternalActorId: args.actorID,
							EnachPayload: &actorProcessor.EnachPayloadToResolveActorAndPi{
								OrgName: "Netflix Inc",
							},
						},
					).
					Return(&piPb.PaymentInstrument{
						Id:                   "pi-1",
						Type:                 piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier:           &piPb.PaymentInstrument_Account{},
						VerifiedName:         "netflix",
						State:                0,
						Capabilities:         nil,
						CreatedAt:            nil,
						UpdatedAt:            nil,
						DeletedAt:            nil,
						IssuerClassification: 0,
						Ownership:            0,
						LastVerifiedAt:       nil,
					}, "other-actor-id", nil)

				// Mock Create Timeline
				mockTimelineClient.EXPECT().
					Create(gomock.Any(), &timelinePb.CreateRequest{
						PrimaryActorId:     args.actorID,
						SecondaryActorId:   "other-actor-id",
						PrimaryActorName:   "user-name-1",
						SecondaryActorName: "netflix",
						Ownership:          timelinePb.Ownership_EPIFI_TECH,
					}).
					Return(&timelinePb.CreateResponse{
						Status: rpcPb.StatusOk(),
					}, nil)

				mockUuidGen.EXPECT().GenerateUuid().Return("client-req-id-1")

				// Mock CreateOrderWithTransaction to return AlreadyExists
				mockOrderClient.EXPECT().
					CreateOrderWithTransaction(gomock.Any(), gomock.Any()).
					Return(&orderPb.CreateOrderWithTransactionResponse{
						Status: rpcPb.StatusAlreadyExists(),
					}, nil)
			},
			expectedError: false,
			expectedRes:   getSuccessRes(),
		},
		{
			name: "Should return error when merchant entity type is USER instead of EXTERNAL_MERCHANT",
			args: commonArgs,
			setupMocks: func(args testArgs) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     args.actorID,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpcPb.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						Id:          "sa-id-1",
						AccountNo:   args.accountNo,
						ActorId:     args.actorID,
						IfscCode:    "ifsc-1",
						State:       savingsPb.State_CREATED,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil)

				// Mock FetchEnachTransactions
				mockEnachVgClient.EXPECT().
					FetchEnachTransactions(gomock.Any(), &vgEnachPb.FetchEnachTransactionsRequest{
						Header:            &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
						TransactionFilter: &vgEnachPb.FetchEnachTransactionsRequest_AccountNumber{AccountNumber: args.accountNo},
						FromDate:          args.fromDate,
						ToDate:            args.toDate,
					}).
					Return(&vgEnachPb.FetchEnachTransactionsResponse{
						Status: rpcPb.StatusOk(),
						Transactions: []*vgEnachPb.EnachTransaction{
							{
								Umrn:                 args.umrn,
								UserName:             args.userName,
								TransactionReference: "txn-ref-1",
								Amount:               &moneyPb.Money{CurrencyCode: "INR", Units: 100, Nanos: 0},
								RequestTimestamp:     timestamppb.New(curTime.Add(-1 * time.Hour)),
								ResponseStatus: &vgEnachPb.TransactionStatus{
									Status:             vgEnachEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILED,
									DetailedStatusCode: "ENA9999",
								},
							},
						},
					}, nil)

				// Mock GetEnachMandate
				mockEnachClient.EXPECT().
					GetEnachMandate(gomock.Any(), &enachPb.GetEnachMandateRequest{
						Identifier: &enachPb.GetEnachMandateRequest_Umrn{
							Umrn: args.umrn,
						},
					}).
					Return(&enachPb.GetEnachMandateResponse{
						Status: rpcPb.StatusOk(),
						EnachMandate: &enachPb.EnachMandate{
							Id:                 "mandate-id-1",
							RecurringPaymentId: "rp-id-1",
							Umrn:               args.umrn,
						},
					}, nil)

				// Mock GetPi for internal actor
				mockPiClient.EXPECT().GetPi(gomock.Any(), &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_AccountRequestParams_{
						AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: args.accountNo,
							IfscCode:            "ifsc-1",
						},
					},
				}).Return(&piPb.GetPiResponse{
					Status: rpcPb.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "pi-id-1",
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: args.accountNo,
								Name:                "user-name-1",
							},
						},
					},
				}, nil)

				// Mock fetchMerchantActorEntityForEnach calls - returns USER entity type
				mockRecurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), &rpPb.GetRecurringPaymentByIdRequest{
					Id: "rp-id-1",
				}).Return(&rpPb.GetRecurringPaymentByIdResponse{
					Status: rpcPb.StatusOk(),
					RecurringPayment: &rpPb.RecurringPayment{
						ToActorId: "merchant-actor-id",
					},
				}, nil)

				mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "merchant-actor-id",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpcPb.StatusOk(),
					EntityId: "merchant-entity-id",
					Name:     &commontypes.Name{FirstName: "John", LastName: "Doe"},
					Type:     types.ActorType_USER, // This should cause merchant check failure
				}, nil)
			},
			expectedError: false,
			expectedRes:   getTransientFailureRes(),
		},
		{
			name: "Should return transient error if ENACH transactions cannot be fetched",
			args: commonArgs,
			setupMocks: func(args testArgs) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     args.actorID,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpcPb.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						Id:          "sa-id-1",
						AccountNo:   args.accountNo,
						ActorId:     args.actorID,
						IfscCode:    "ifsc-1",
						State:       savingsPb.State_CREATED,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil)

				// Mock FetchEnachTransactions with error
				mockEnachVgClient.EXPECT().
					FetchEnachTransactions(gomock.Any(), gomock.Any()).
					Return(&vgEnachPb.FetchEnachTransactionsResponse{
						Status: rpcPb.StatusInternal(),
					}, nil)
			},
			expectedError: false,
			expectedRes:   getTransientFailureRes(),
		},
		{
			name: "Should return permanent error if no failed transactions are found",
			args: commonArgs,
			setupMocks: func(args testArgs) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     args.actorID,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpcPb.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						Id:          "sa-id-1",
						AccountNo:   args.accountNo,
						ActorId:     args.actorID,
						IfscCode:    "ifsc-1",
						State:       savingsPb.State_CREATED,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil)

				// Mock FetchEnachTransactions for a UMRN which does not exist in our system.
				mockEnachVgClient.EXPECT().
					FetchEnachTransactions(gomock.Any(), gomock.Any()).
					Return(&vgEnachPb.FetchEnachTransactionsResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)
			},
			expectedError: false,
			expectedRes:   getPermanentFailureRes(),
		},
		{
			name: "Should return transient error if fetchMerchantActorEntityForEnach fails",
			args: commonArgs,
			setupMocks: func(args testArgs) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     args.actorID,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpcPb.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						Id:          "sa-id-1",
						AccountNo:   args.accountNo,
						ActorId:     args.actorID,
						IfscCode:    "ifsc-1",
						State:       savingsPb.State_CREATED,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil)

				// Mock FetchEnachTransactions
				mockEnachVgClient.EXPECT().
					FetchEnachTransactions(gomock.Any(), &vgEnachPb.FetchEnachTransactionsRequest{
						Header:            &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
						TransactionFilter: &vgEnachPb.FetchEnachTransactionsRequest_AccountNumber{AccountNumber: args.accountNo},
						FromDate:          args.fromDate,
						ToDate:            args.toDate,
					}).
					Return(&vgEnachPb.FetchEnachTransactionsResponse{
						Status: rpcPb.StatusOk(),
						Transactions: []*vgEnachPb.EnachTransaction{
							{
								Umrn:                 args.umrn,
								UserName:             args.userName,
								TransactionReference: "txn-ref-1",
								Amount:               &moneyPb.Money{CurrencyCode: "INR", Units: 100, Nanos: 0},
								RequestTimestamp:     timestamppb.New(curTime.Add(-1 * time.Hour)),
								ResponseStatus: &vgEnachPb.TransactionStatus{
									Status:             vgEnachEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILED,
									DetailedStatusCode: "ENA9999",
								},
							},
						},
					}, nil)

				// Mock GetEnachMandate
				mockEnachClient.EXPECT().
					GetEnachMandate(gomock.Any(), &enachPb.GetEnachMandateRequest{
						Identifier: &enachPb.GetEnachMandateRequest_Umrn{
							Umrn: args.umrn,
						},
					}).
					Return(&enachPb.GetEnachMandateResponse{
						Status: rpcPb.StatusOk(),
						EnachMandate: &enachPb.EnachMandate{
							Id:                 "mandate-id-1",
							RecurringPaymentId: "rp-id-1",
							Umrn:               args.umrn,
						},
					}, nil)

				mockPiClient.EXPECT().GetPi(gomock.Any(), &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_AccountRequestParams_{
						AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: args.accountNo,
							IfscCode:            "ifsc-1",
						},
					},
				}).Return(&piPb.GetPiResponse{
					Status: rpcPb.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "pi-id-1",
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: args.accountNo,
								Name:                "user-name-1",
							},
						},
					},
				}, nil)

				// Mock fetchMerchantActorEntityForEnach to fail
				mockRecurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), &rpPb.GetRecurringPaymentByIdRequest{
					Id: "rp-id-1",
				}).Return(&rpPb.GetRecurringPaymentByIdResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
			expectedError: false,
			expectedRes:   getTransientFailureRes(),
		},
		{
			name: "Should return transient error if mandate does not exist in our system",
			args: commonArgs,
			setupMocks: func(args testArgs) {
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     args.actorID,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpcPb.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						Id:          "sa-id-1",
						AccountNo:   args.accountNo,
						ActorId:     args.actorID,
						IfscCode:    "ifsc-1",
						State:       savingsPb.State_CREATED,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil)

				// Mock FetchEnachTransactions
				mockEnachVgClient.EXPECT().
					FetchEnachTransactions(gomock.Any(), &vgEnachPb.FetchEnachTransactionsRequest{
						Header:            &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
						TransactionFilter: &vgEnachPb.FetchEnachTransactionsRequest_AccountNumber{AccountNumber: args.accountNo},
						FromDate:          args.fromDate,
						ToDate:            args.toDate,
					}).
					Return(&vgEnachPb.FetchEnachTransactionsResponse{
						Status: rpcPb.StatusOk(),
						Transactions: []*vgEnachPb.EnachTransaction{
							{
								Umrn:                 args.umrn,
								UserName:             args.userName,
								TransactionReference: "txn-ref-1",
								Amount:               &moneyPb.Money{CurrencyCode: "INR", Units: 100, Nanos: 0},
								RequestTimestamp:     timestamppb.New(curTime.Add(-1 * time.Hour)),
								ResponseStatus: &vgEnachPb.TransactionStatus{
									Status:             vgEnachEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILED,
									DetailedStatusCode: "ENA9999",
								},
							},
						},
					}, nil)

				// Mock GetEnachMandate to return record not found
				mockEnachClient.EXPECT().
					GetEnachMandate(gomock.Any(), &enachPb.GetEnachMandateRequest{
						Identifier: &enachPb.GetEnachMandateRequest_Umrn{
							Umrn: args.umrn,
						},
					}).
					Return(&enachPb.GetEnachMandateResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)

				mockPiClient.EXPECT().GetPi(gomock.Any(), &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					Identifier: &piPb.GetPiRequest_AccountRequestParams_{
						AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: args.accountNo,
							IfscCode:            "ifsc-1",
						},
					},
				}).Return(&piPb.GetPiResponse{
					Status: rpcPb.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "pi-id-1",
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: args.accountNo,
								Name:                "user-name-1",
							},
						},
					},
				}, nil)
			},
			expectedError: false,
			expectedRes:   getSuccessRes(), // This should succeed as the transaction is skipped for non-existent mandates
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.setupMocks(tc.args)

			res, err := service.FetchAndCreateFailedEnachTransactions(tc.args.ctx, &rpConsumerPb.FetchAndCreateFailedEnachTransactionsRequest{
				ActorId:  tc.args.actorID,
				FromDate: tc.args.fromDate,
				ToDate:   tc.args.toDate,
			})

			if tc.expectedError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tc.expectedRes, res)
			}
		})
	}
}

func TestConsumerService_fetchMerchantActorEntityForEnach(t *testing.T) {
	t.Parallel()

	type args struct {
		recurringPaymentId string
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(
			recurringPaymentClient *mockRpPb.MockRecurringPaymentServiceClient,
			actorClient *mockActorPb.MockActorClient,
		)
		want    *actorPb.GetEntityDetailsByActorIdResponse
		wantErr bool
	}{
		{
			name: "Should successfully fetch merchant actor entity 1",
			args: args{
				recurringPaymentId: "rp-id-1",
			},
			setupMocks: func(
				recurringPaymentClient *mockRpPb.MockRecurringPaymentServiceClient,
				actorClient *mockActorPb.MockActorClient,
			) {
				recurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), &rpPb.GetRecurringPaymentByIdRequest{
					Id: "rp-id-1",
				}).Return(&rpPb.GetRecurringPaymentByIdResponse{
					Status: rpcPb.StatusOk(),
					RecurringPayment: &rpPb.RecurringPayment{
						ToActorId: "merchant-actor-id",
					},
				}, nil)

				actorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "merchant-actor-id",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpcPb.StatusOk(),
					EntityId: "merchant-entity-id",
					Name:     &commontypes.Name{FirstName: "Test", LastName: "Merchant"},
					Type:     types.ActorType_EXTERNAL_MERCHANT,
				}, nil)
			},
			want: &actorPb.GetEntityDetailsByActorIdResponse{
				Status:   rpcPb.StatusOk(),
				EntityId: "merchant-entity-id",
				Name:     &commontypes.Name{FirstName: "Test", LastName: "Merchant"},
				Type:     types.ActorType_EXTERNAL_MERCHANT,
			},
			wantErr: false,
		},
		{
			name: "Should successfully fetch merchant actor entity 2",
			args: args{
				recurringPaymentId: "rp-id-1",
			},
			setupMocks: func(
				recurringPaymentClient *mockRpPb.MockRecurringPaymentServiceClient,
				actorClient *mockActorPb.MockActorClient,
			) {
				recurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), &rpPb.GetRecurringPaymentByIdRequest{
					Id: "rp-id-1",
				}).Return(&rpPb.GetRecurringPaymentByIdResponse{
					Status: rpcPb.StatusOk(),
					RecurringPayment: &rpPb.RecurringPayment{
						ToActorId: "merchant-actor-id",
					},
				}, nil)

				actorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "merchant-actor-id",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpcPb.StatusOk(),
					EntityId: "merchant-entity-id",
					Name:     &commontypes.Name{FirstName: "Test", LastName: "Merchant"},
					Type:     types.ActorType_MERCHANT,
				}, nil)
			},
			want: &actorPb.GetEntityDetailsByActorIdResponse{
				Status:   rpcPb.StatusOk(),
				EntityId: "merchant-entity-id",
				Name:     &commontypes.Name{FirstName: "Test", LastName: "Merchant"},
				Type:     types.ActorType_MERCHANT,
			},
			wantErr: false,
		},
		{
			name: "Should return error when GetEntityDetailsByActorId returns non-merchant type",
			args: args{
				recurringPaymentId: "rp-id-1",
			},
			setupMocks: func(
				recurringPaymentClient *mockRpPb.MockRecurringPaymentServiceClient,
				actorClient *mockActorPb.MockActorClient,
			) {
				recurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), &rpPb.GetRecurringPaymentByIdRequest{
					Id: "rp-id-1",
				}).Return(&rpPb.GetRecurringPaymentByIdResponse{
					Status: rpcPb.StatusOk(),
					RecurringPayment: &rpPb.RecurringPayment{
						ToActorId: "merchant-actor-id",
					},
				}, nil)

				actorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "merchant-actor-id",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpcPb.StatusOk(),
					EntityId: "merchant-entity-id",
					Name:     &commontypes.Name{FirstName: "Test", LastName: "Merchant"},
					Type:     types.ActorType_USER,
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Should return error when GetRecurringPaymentById fails",
			args: args{
				recurringPaymentId: "rp-id-1",
			},
			setupMocks: func(
				recurringPaymentClient *mockRpPb.MockRecurringPaymentServiceClient,
				actorClient *mockActorPb.MockActorClient,
			) {
				recurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), &rpPb.GetRecurringPaymentByIdRequest{
					Id: "rp-id-1",
				}).Return(&rpPb.GetRecurringPaymentByIdResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Should return error when GetEntityDetailsByActorId fails",
			args: args{
				recurringPaymentId: "rp-id-1",
			},
			setupMocks: func(
				recurringPaymentClient *mockRpPb.MockRecurringPaymentServiceClient,
				actorClient *mockActorPb.MockActorClient,
			) {
				recurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), &rpPb.GetRecurringPaymentByIdRequest{
					Id: "rp-id-1",
				}).Return(&rpPb.GetRecurringPaymentByIdResponse{
					Status: rpcPb.StatusOk(),
					RecurringPayment: &rpPb.RecurringPayment{
						ToActorId: "merchant-actor-id",
					},
				}, nil)

				actorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "merchant-actor-id",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt // capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create controller and mocks for this test
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// Initialize mock clients
			recurringPaymentClient := mockRpPb.NewMockRecurringPaymentServiceClient(ctr)
			actorClient := mockActorPb.NewMockActorClient(ctr)

			// Setup mocks for this test case
			tt.setupMocks(recurringPaymentClient, actorClient)

			// Create service instance
			c := &ConsumerService{
				recurringPaymentClient: recurringPaymentClient,
				actorClient:            actorClient,
			}

			// Execute test
			got, err := c.fetchMerchantActorEntityForEnach(context.Background(), tt.args.recurringPaymentId)
			if tt.wantErr {
				require.Error(t, err)
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.want, got)
			}
		})
	}
}
