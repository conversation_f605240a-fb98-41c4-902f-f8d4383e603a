package recurringpayment

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"time"

	"github.com/golang/protobuf/ptypes"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	moneyPb "github.com/epifi/be-common/pkg/money"

	actorPb "github.com/epifi/gamma/api/actor"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	bePiPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	pb "github.com/epifi/gamma/api/recurringpayment"
	consumerPb "github.com/epifi/gamma/api/recurringpayment/consumer"
	"github.com/epifi/gamma/api/recurringpayment/enach"
	mandate2 "github.com/epifi/gamma/api/upi/mandate"
	"github.com/epifi/gamma/api/vendors"
	"github.com/epifi/gamma/api/vendors/federal/upi"
	upiPkg "github.com/epifi/gamma/pkg/upi"
	"github.com/epifi/gamma/recurringpayment/dao"
)

// GetRecurringPaymentsForActor fetches a list of recurring payments for a particular actor
// This rpc method returns the list in pages. It returns all the recurring payments from specified start_timestamp.
func (s *Service) GetRecurringPaymentsForActor(ctx context.Context, req *pb.GetRecurringPaymentsForActorRequest) (*pb.GetRecurringPaymentsForActorResponse, error) {

	var (
		res               = &pb.GetRecurringPaymentsForActorResponse{}
		recurringPayments []*pb.RecurringPayment
		err               error
	)

	startTime, err := ptypes.Timestamp(req.GetStartTimestamp())
	if err != nil {
		logger.Error(ctx, "failed to interpret time from proto timestamp",
			zap.Any(logger.START_TIME, req.GetStartTimestamp()),
			zap.Error(err))

		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	// publish the packet to fetch recurring payments that were set up by the user on third party apps like Groww
	// consious call to publish the packet before fetching from db, this is done to make sure we are fetching active mandates for the user from vendor
	// and updating our db in async call.
	_, publishErr := s.fetchAndCreateOffAppRecurringPaymentPublisher.Publish(ctx, &consumerPb.FetchAndCreateOffAppRecurringPaymentsRequest{
		ActorId: req.GetCurrentActorId(),
	})
	if publishErr != nil {
		logger.Error(ctx, "failed to publish the packet for fetchAndCreateOffAppRecurringPayment", zap.String(logger.ACTOR_ID_V2, req.GetCurrentActorId()), zap.Error(publishErr))
	}

	if recurringPayments, err = s.recurringPaymentDao.GetByActorId(ctx, req.GetCurrentActorId(), startTime,
		req.GetPageSize(), req.GetOffset(), req.GetDescending(), req.GetStates()); err != nil {
		logger.Error(ctx, "Failed to fetch recurring payments", zap.String(logger.ACTOR_ID, req.GetCurrentActorId()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) || errors.Is(err, gorm.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	// converting to the object that we want to show to the client
	res.RecurringPayments, err = s.getRecurringPaymentsList(ctx, recurringPayments, req.GetCurrentActorId())
	if err != nil {
		logger.Error(ctx, "Failed to fetch recurring payments", zap.String(logger.ACTOR_ID, req.GetCurrentActorId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = rpc.StatusOk()
	return res, nil

}

func (s *Service) getRecurringPaymentsList(ctx context.Context, payments []*pb.RecurringPayment, actorId string) ([]*pb.GetRecurringPaymentsForActorResponse_RecurringPaymentTile, error) {

	var (
		recurringPaymentsList []*pb.GetRecurringPaymentsForActorResponse_RecurringPaymentTile
		otherActorIds         []string
	)

	for _, payment := range payments {
		otherActorId := getOtherActorId(actorId, payment.GetFromActorId(), payment.GetToActorId())
		otherActorIds = append(otherActorIds, otherActorId)
	}

	entityDetailsRes, err := s.actorClient.GetEntityDetails(ctx, &actorPb.GetEntityDetailsRequest{ActorIds: otherActorIds})
	if err = epifigrpc.RPCError(entityDetailsRes, err); err != nil {
		return nil, fmt.Errorf("failed to fetch entity details: res: %s: %w", entityDetailsRes.GetStatus(), err)
	}

	actorIdToEntityDetailsMap := make(map[string]*actorPb.GetEntityDetailsResponse_EntityDetail)
	for _, entityDetail := range entityDetailsRes.GetEntityDetails() {
		actorIdToEntityDetailsMap[entityDetail.GetActorId()] = entityDetail
	}

	for _, payment := range payments {
		// update the recurring payment based upon the end date configs
		payment, err = s.checkAndUpdateRecurringPaymentState(ctx, payment)
		if err != nil {
			return nil, fmt.Errorf("error in checking recurring payments actions :%w", err)
		}
		var recurringPayment = &pb.GetRecurringPaymentsForActorResponse_RecurringPaymentTile{}
		recurringPayment.State = payment.GetState()
		otherActorId := getOtherActorId(actorId, payment.GetFromActorId(), payment.GetToActorId())
		// NOTE: other actor id is used here
		recurringPayment.Name = actorIdToEntityDetailsMap[otherActorId].GetName().ToString()
		recurringPayment.ImageUrl = actorIdToEntityDetailsMap[otherActorId].GetProfileImageUrl()
		recurringPayment.Id = payment.GetId()
		recurringPayment.Type = payment.GetType()
		recurringPayment.AllowedFrequency = payment.GetRecurrenceRule().GetAllowedFrequency()
		recurringPayment.CreationTimestamp = payment.GetCreatedAt()
		recurringPayment.Amount = payment.GetAmount()
		recurringPayment.ExternalId = payment.GetExternalId()
		recurringPayment.Interval = payment.GetInterval()

		if actorIdToEntityDetailsMap[otherActorId].GetProfileImageUrl() == "" {
			recurringPayment.BgColor = actorPb.GetColourCodeForActor(otherActorId)
		}

		// fill expiry time for the pending action
		if recurringPayment.GetState().IsAnyActionInProgress() {
			actionInProgress := getActionInProgressForRecurringPayment(recurringPayment.GetState())
			// TODO(Ankit): Convert this into a batch call
			actions, err := s.recurringPaymentActionsDao.GetByRecurringPaymentId(ctx, payment.GetId(),
				dao.WithActionsFilter([]pb.Action{actionInProgress}), dao.WithOrderByCreatedAt(true))
			if err != nil {
				return nil, fmt.Errorf("failed to fetch recurring payment actions :%w", err)
			}
			if actions == nil {
				return nil, fmt.Errorf("no recurring payment actions exist for this recurring paymnent "+
					"in progress state %s", recurringPayment.GetId())
			}
			recurringPayment.Expiry = actions[0].GetExpireAt()
		}
		recurringPaymentsList = append(recurringPaymentsList, recurringPayment)
	}
	return recurringPaymentsList, nil
}

// GetRecurringPaymentDetailsById fetches all the details of a recurring payment along with the count of successful executions
// via that recurring payment
// nolint:funlen
func (s *Service) GetRecurringPaymentDetailsById(ctx context.Context, req *pb.GetRecurringPaymentDetailsByIdRequest) (*pb.GetRecurringPaymentDetailsByIdResponse, error) {

	var (
		res                 = &pb.GetRecurringPaymentDetailsByIdResponse{}
		recurringPayment    *pb.RecurringPayment
		pendingActionParams *pb.PendingActionParams
		actions             []*pb.RecurringPaymentsAction
		urn                 string
		err                 error
		mandate             *mandate2.MandateEntity
	)

	if recurringPayment, err = s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId()); err != nil {
		logger.Error(ctx, "Failed to fetch recurring payments", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	err = validateActorIdForRequest(recurringPayment, req.GetCurrentActorId())
	if err != nil {
		logger.Error(ctx, "failed to validate actor", zap.String(logger.ACTOR_ID, req.GetCurrentActorId()))
		res.Status = rpc.StatusPermissionDenied()
		return res, nil
	}

	otherActorId := getOtherActorId(req.GetCurrentActorId(), recurringPayment.GetFromActorId(), recurringPayment.GetToActorId())

	entityRes, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: otherActorId})
	if err = epifigrpc.RPCError(entityRes, err); err != nil {
		logger.Error(ctx, "Failed to fetch entity details", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.RecurringPayment = recurringPayment
	res.Name = entityRes.GetName()
	imageUrl := entityRes.GetProfileImageUrl()
	res.ImageUrl = imageUrl
	if imageUrl == "" {
		res.BgColor = actorPb.GetColourCodeForActor(otherActorId)
	}

	if recurringPayment.GetType() == pb.RecurringPaymentType_UPI_MANDATES {
		// get mandate details to fill umn,urn etc.
		mandateRes, err := s.upiMandateClient.GetMandate(ctx,
			&mandate2.GetMandateRequest{Identifier: &mandate2.GetMandateRequest_RecurringPaymentId{
				RecurringPaymentId: recurringPayment.GetId(),
			},
			})
		if err = epifigrpc.RPCError(mandateRes, err); err != nil {
			logger.Error(ctx, "Failed to fetch mandates", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()),
				zap.String(logger.STATUS_CODE, mandateRes.GetStatus().String()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		mandate = mandateRes.GetMandate()
	}

	res.SupportedActions = s.getSupportedActions(ctx, req.GetCurrentActorId(), recurringPayment, mandate)

	successExecutions, err := s.recurringPaymentActionsDao.GetByRecurringPaymentId(ctx, recurringPayment.GetId(),
		dao.WithActionsFilter([]pb.Action{pb.Action_EXECUTE}), dao.WithActionStateFilter([]pb.ActionState{pb.ActionState_ACTION_SUCCESS}))
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "Failed to fetch recurring payments actions", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.ExecutionCount = int32(len(successExecutions))

	// fill pending action params
	if isActionPending(recurringPayment.GetState(), recurringPayment.GetInitiatedBy(), recurringPayment.GetType(),
		req.GetCurrentActorId(), recurringPayment.GetFromActorId()) {
		actions, pendingActionParams, err = s.getPendingActionParams(ctx, recurringPayment, req.GetCurrentActorId())
		if err != nil {
			logger.Error(ctx, "Failed to get pending action params for mandate",
				zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_UPI_MANDATES:
		otherActorVpa, err := s.getOtherActorVpa(ctx, req.GetCurrentActorId(), recurringPayment)
		if err != nil {
			logger.Error(ctx, "failed to get other actor vpa",
				zap.String(logger.ACTOR_ID, req.GetCurrentActorId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		// urn will be used to generate collect request for mandate execution
		if req.GetCurrentActorId() == recurringPayment.GetFromActorId() {
			urn, err = buildMandateUrn(recurringPayment, mandate.GetUmn(), otherActorVpa, s.config.UpiMandateUrn.CollectMandatePrefix)
			if err != nil {
				logger.Error(ctx, "error in building mandate urn", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.Error(err))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
		}

		upiMandateParams := &pb.UPIMandateParams{
			Umn: mandate.GetUmn(),
			Vpa: otherActorVpa,
			Urn: urn,
		}
		res.AdditionalParams = &pb.GetRecurringPaymentDetailsByIdResponse_UpiMandateParams{
			UpiMandateParams: upiMandateParams}

		// fill transaction attributes and transaction id in case of pending request actions like authorize/decline
		if pendingActionParams != nil && pendingActionParams.GetPendingRequestActions() != nil {
			if len(actions) != 0 {
				transactionAttributes, err := s.getTransactionAttributes(ctx, recurringPayment,
					actions, actions[0].GetVendorRequestId(), nil)
				if err != nil {
					logger.Error(ctx, "Failed to get transaction attributes",
						zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
					res.Status = rpc.StatusInternal()
					return res, nil
				}
				pendingActionParams.TransactionAttributes = transactionAttributes
				pendingActionParams.TransactionId = transactionAttributes[0].TransactionId
			}
		}

	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		// TODO(Ankit): For SI add transaction Id
	case pb.RecurringPaymentType_ENACH_MANDATES:
		enachMandate, enachMandateErr := s.enachClient.GetEnachMandate(ctx, &enach.GetEnachMandateRequest{
			Identifier: &enach.GetEnachMandateRequest_RecurringPaymentId{
				RecurringPaymentId: req.GetRecurringPaymentId(),
			},
		})
		if rpcErr := epifigrpc.RPCError(enachMandate, enachMandateErr); rpcErr != nil {
			logger.Error(ctx, "failed to fetch enach mandate", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(rpcErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		enachMandateParams := &pb.EnachMandateParams{
			Umrn: enachMandate.GetEnachMandate().GetUmrn(),
		}
		res.AdditionalParams = &pb.GetRecurringPaymentDetailsByIdResponse_EnachMandateParams{
			EnachMandateParams: enachMandateParams,
		}
	case pb.RecurringPaymentType_DEBIT_CARD_MANDATES:
		// no additional info to be added for dc mandates
	default:
		logger.Error(ctx, "Recurring payment type not supported",
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if pendingActionParams != nil {
		res.StateParams = &pb.GetRecurringPaymentDetailsByIdResponse_PendingActionParams{
			PendingActionParams: pendingActionParams}
	}
	res.SecondaryActorId = otherActorId
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) GetRecurringPaymentByExternalId(ctx context.Context, req *pb.GetRecurringPaymentByExternalIdRequest) (*pb.GetRecurringPaymentByExternalIdResponse, error) {

	var (
		res              = &pb.GetRecurringPaymentByExternalIdResponse{}
		recurringPayment *pb.RecurringPayment
		err              error
	)

	recurringPayment, err = s.recurringPaymentDao.GetByExternalId(ctx, req.GetExternalId())
	if err != nil {
		logger.Error(ctx, "Error in Getting Recurring payment by External Id", zap.String(logger.EXTERNAL_ID, req.GetExternalId()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.RecurringPayment = recurringPayment

	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) getSupportedActions(ctx context.Context, currActorId string, recurringPayment *pb.RecurringPayment, mandate *mandate2.MandateEntity) []pb.Action {

	var (
		endTime          = recurringPayment.GetInterval().GetEndTime().AsTime()
		fromActorId      = recurringPayment.GetFromActorId()
		supportedActions []pb.Action
	)

	switch {
	case recurringPayment.GetType() == pb.RecurringPaymentType_UPI_MANDATES:

		switch {
		case recurringPayment.GetState() == pb.RecurringPaymentState_ACTIVATED:
			currentTime := time.Now()
			if endTime.Sub(currentTime).Hours() > 24 && mandate.GetRevokeable() {
				supportedActions = append(supportedActions, pb.Action_REVOKE)
			}

			if s.isMandatePauseAllowed(ctx, currActorId, recurringPayment, mandate.GetRevokeable()) {
				supportedActions = append(supportedActions, pb.Action_PAUSE)
			}
		case recurringPayment.GetState() == pb.RecurringPaymentState_PAUSED:
			if currActorId == fromActorId && recurringPayment.GetRecurrenceRule().GetAllowedFrequency() != pb.AllowedFrequency_ONE_TIME {
				supportedActions = append(supportedActions, pb.Action_UNPAUSE)
			}
		}
	case recurringPayment.GetType() == pb.RecurringPaymentType_STANDING_INSTRUCTION:

		if recurringPayment.GetState() == pb.RecurringPaymentState_ACTIVATED {
			supportedActions = append(supportedActions, pb.Action_REVOKE)

			// SIs are payer initiated and can be modified by payer only
			if currActorId == fromActorId {
				supportedActions = append(supportedActions, pb.Action_MODIFY)
			}
		}
	}

	return supportedActions
}

// nolint: funlen
func (s *Service) getTransactionAttributes(ctx context.Context, recurringPayment *pb.RecurringPayment, actions []*pb.RecurringPaymentsAction, transactionId string, amount *money.Money) ([]*pb.TransactionAttribute, error) {
	var (
		requestId string
	)
	payerPiId := recurringPayment.GetPiFrom()
	payeePiId := recurringPayment.GetPiTo()
	payeeActorId := recurringPayment.GetToActorId()

	payerAccIdRes, err := s.accountPIRelationsClient.GetByPiId(ctx, &account_pi.GetByPiIdRequest{PiId: payerPiId})
	if err = epifigrpc.RPCError(payerAccIdRes, err); err != nil {
		return nil, fmt.Errorf("failed to fetch account id details %s %w", payerAccIdRes.GetStatus(), err)
	}
	payerAccId := payerAccIdRes.GetAccountId()

	payerPi, payeePi, err := s.getPiForPayerAndPayee(ctx, payerPiId, payeePiId)
	if err != nil {
		return nil, fmt.Errorf("failed to get payer/payee pi: %w", err)
	}

	payerPiStr, payeePiStr, payeePiDisplay, err := getPiStringForPayerAndPayee(payerPi, payeePi, recurringPayment.GetPreferredPaymentProtocol())
	if err != nil {
		return nil, fmt.Errorf("failed to get payer/payee string: %w", err)
	}

	if transactionId == "" {
		requestId = actions[0].GetVendorRequestId()
	} else {
		requestId = transactionId
	}

	entityRes, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: payeeActorId})
	if err = epifigrpc.RPCError(payerAccIdRes, err); err != nil {
		return nil, fmt.Errorf("failed to fetch entity details %s %w", entityRes.GetStatus(), err)
	}

	transactionAttribute := &pb.TransactionAttribute{
		PayerAccountId:                payerAccId,
		PaymentProtocol:               recurringPayment.GetPreferredPaymentProtocol(),
		Amount:                        recurringPayment.GetAmount(),
		PayerPaymentInstrument:        payerPiStr,
		PayeePaymentInstrument:        payeePiStr,
		DisplayPayeePaymentInstrument: payeePiDisplay,
		PayeeActorName:                entityRes.GetName().ToString(),
		PayerMaskedAccountNumber:      mask.GetMaskedAccountNumber(getMaskedAccountNumberFromPi(payerPi), ""),
		TransactionId:                 requestId,
		Remarks:                       recurringPayment.GetRemarks(),
	}

	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_UPI_MANDATES:
		// get mandate request parameters to fill traansaction id and transaction attributes
		mandateRequestParametersRes, err := s.upiMandateClient.GetMandateRequestParameters(ctx,
			&mandate2.GetMandateRequestParametersRequest{ReqId: requestId})
		if err = epifigrpc.RPCError(mandateRequestParametersRes, err); err != nil {
			return nil, fmt.Errorf("failed to fetch mandate request parameter: res: %s: %w",
				mandateRequestParametersRes.GetStatus(), err)
		}
		if mandateRequestParametersRes.GetPayeeVpa() != "" {
			transactionAttribute.PayeePaymentInstrument = mandateRequestParametersRes.GetPayeeVpa()
		}
		if mandateRequestParametersRes.GetPayerVpa() != "" {
			transactionAttribute.PayerPaymentInstrument = mandateRequestParametersRes.GetPayerVpa()
		}
		transactionAttribute.MerchantRefId = mandateRequestParametersRes.GetMerchantRefId()
		transactionAttribute.ReferenceUrl = mandateRequestParametersRes.GetRefUrl()
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		transactionAttribute.Amount = amount
	default:
		return nil, fmt.Errorf("invalid recurring payment type %s", recurringPayment.GetType().String())
	}

	return []*pb.TransactionAttribute{transactionAttribute}, nil
}

func (s *Service) getPiForPayerAndPayee(ctx context.Context, payerPiId, payeePiId string) (*bePiPb.PaymentInstrument, *bePiPb.PaymentInstrument, error) {

	var (
		payerPi *bePiPb.PaymentInstrument
		payeePi *bePiPb.PaymentInstrument
	)

	payerPiRes, err := s.piClient.GetPiById(ctx, &bePiPb.GetPiByIdRequest{Id: payerPiId})
	if err = epifigrpc.RPCError(payerPiRes, err); err != nil {
		return nil, nil, fmt.Errorf("failed to fetch PI details: res: %s: %w", payerPiRes.GetStatus(), err)
	}
	payerPi = payerPiRes.GetPaymentInstrument()

	payeePiRes, err := s.piClient.GetPiById(ctx, &bePiPb.GetPiByIdRequest{Id: payeePiId})
	if err = epifigrpc.RPCError(payeePiRes, err); err != nil {
		return nil, nil, fmt.Errorf("failed to fetch PI details: res: %s: %w", payeePiRes.GetStatus(), err)
	}
	payeePi = payeePiRes.GetPaymentInstrument()

	return payerPi, payeePi, nil
}

func getPiStringForPayerAndPayee(payerPi, payeePi *bePiPb.PaymentInstrument, paymentProtocol paymentPb.PaymentProtocol) (string, string, string, error) {

	var (
		payeePiStr     string
		payerPiStr     string
		payeePiDisplay string
		err            error
	)

	payerPiStr, _, err = piToString(payerPi, paymentProtocol)
	if err != nil {
		return "", "", "", err
	}

	payeePiStr, payeePiDisplay, err = piToString(payeePi, paymentProtocol)
	if err != nil {
		return "", "", "", err
	}

	return payerPiStr, payeePiStr, payeePiDisplay, nil
}

// piToString converts PI to string, which is then used in CL salts
// also returns the pi info to be displayed
func piToString(pi *bePiPb.PaymentInstrument, protocol paymentPb.PaymentProtocol) (string, string, error) {
	if pi == nil {
		return "", "", nil
	}

	// in case of UPI payment protocol, the PI has to be a VPA
	// i.e. even if PI is of type bank account, we translate it to
	// a normalized VPA
	if protocol == paymentPb.PaymentProtocol_UPI {
		vpa, err := pi.GetVPA()
		if err != nil {
			return "", "", err
		}
		switch {
		case pi.GetType() == bePiPb.PaymentInstrumentType_BANK_ACCOUNT:
			return vpa, pi.GetAccount().GetSecureAccountNumber(), nil
		case pi.GetType() == bePiPb.PaymentInstrumentType_UPI:
			return vpa, vpa, nil
		default:
			return "", "", fmt.Errorf("payment protocol UPI cannot be used with pi of type: %v", pi.GetType())
		}
	}

	switch {
	case pi.GetType() == bePiPb.PaymentInstrumentType_BANK_ACCOUNT:
		return pi.GetAccount().GetSecureAccountNumber(), pi.GetAccount().GetSecureAccountNumber(), nil
	case pi.GetType() == bePiPb.PaymentInstrumentType_CREDIT_CARD ||
		pi.GetType() == bePiPb.PaymentInstrumentType_DEBIT_CARD:
		return pi.GetCard().GetActualCardNumber(), pi.GetCard().GetActualCardNumber(), nil
	default:
		return "", "", nil
	}
}

// returns the masked/secure account number given a PI
func getMaskedAccountNumberFromPi(pi *bePiPb.PaymentInstrument) string {
	if pi == nil {
		return ""
	}
	switch {
	case pi.Type == bePiPb.PaymentInstrumentType_UPI:
		return pi.GetUpi().MaskedAccountNumber
	case pi.Type == bePiPb.PaymentInstrumentType_BANK_ACCOUNT:
		return pi.GetAccount().GetSecureAccountNumber()
	}
	return ""
}

func getOtherActorId(currActorId, fromActorId, toActorId string) string {
	otherActorId := fromActorId
	if currActorId == fromActorId {
		otherActorId = toActorId
	}
	return otherActorId
}

func (s *Service) getPendingActionParams(ctx context.Context, recurringPayment *pb.RecurringPayment, currentActorId string) ([]*pb.RecurringPaymentsAction, *pb.PendingActionParams, error) {

	pendingActionParams := &pb.PendingActionParams{}

	actionInProgress := getActionInProgressForRecurringPayment(recurringPayment.GetState())
	actions, err := s.recurringPaymentActionsDao.GetByRecurringPaymentId(ctx, recurringPayment.GetId(),
		dao.WithActionsFilter([]pb.Action{actionInProgress}), dao.WithOrderByCreatedAt(true))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to fetch recurring payment actions: %w", err)
	}

	if actions == nil {
		return nil, nil, fmt.Errorf("no recurring payment actions exist for this recurring payment"+
			"in progress state :%s", recurringPayment.GetId())
	}

	pendingActionParams.Expiry = actions[0].GetExpireAt()
	pendingActionParams.ClientRequestId = actions[0].GetClientRequestId()

	// when current actor is payer and mandate is initiated by payee, get the pending request actions if state of mandate is
	// MODIFY_INITIATED or CREATION_INITIATED
	if currentActorId == recurringPayment.GetFromActorId() && recurringPayment.GetInitiatedBy() == pb.InitiatedBy_PAYEE &&
		(recurringPayment.GetState() == pb.RecurringPaymentState_MODIFY_INITIATED ||
			recurringPayment.GetState() == pb.RecurringPaymentState_CREATION_INITIATED) {
		pendingActionParams.PendingRequestActions = []*pb.RecurringPaymentPendingRequestAction{
			{PendingAction: pb.PendingRequestAction_AUTHORIZE, IsAuthRequired: true},
			{PendingAction: pb.PendingRequestAction_DECLINE, IsAuthRequired: false},
		}
		var credBlockType pb.CredBlockType
		_, credBlockType, err = s.recurringPaymentProcessor.GetCredBlockType(
			recurringPayment.GetType(), getActorRole(currentActorId, recurringPayment))
		if err != nil {
			return nil, nil, fmt.Errorf("failed to fetch cred block type: %w", err)
		}
		pendingActionParams.CredBlockType = credBlockType
	}

	if actionInProgress == pb.Action_MODIFY {

		// updated params here are filled same for mandate and SI
		// in frontend it will be differentiated what is to be allowed to modify
		pendingActionParams.AdditionalParams = &pb.PendingActionParams_UpdatedParams{
			// taking the first entry of latest modify actions
			UpdatedParams: actions[0].GetActionMetadata().GetModifyActionMetadata().GetUpdatedParams()}
	}

	return actions, pendingActionParams, nil
}

func getActionInProgressForRecurringPayment(state pb.RecurringPaymentState) pb.Action {

	var actionInProgress pb.Action

	switch {
	case state.IsCreationInProgress():
		actionInProgress = pb.Action_CREATE
	case state.IsRevokeInProgress():
		actionInProgress = pb.Action_REVOKE
	case state.IsModificationInProgress():
		actionInProgress = pb.Action_MODIFY
	}
	return actionInProgress
}

func (s *Service) getOtherActorVpa(ctx context.Context, actorId string, recurringPayment *pb.RecurringPayment) (string, error) {

	var otherActorPiId string

	if actorId == recurringPayment.GetFromActorId() {
		otherActorPiId = recurringPayment.GetPiTo()
	} else {
		otherActorPiId = recurringPayment.GetPiFrom()
	}

	otherActorPiRes, err := s.piClient.GetPiById(ctx, &bePiPb.GetPiByIdRequest{Id: otherActorPiId})
	if err = epifigrpc.RPCError(otherActorPiRes, err); err != nil {
		return "", fmt.Errorf("failed to fetch PI details: res: %s: %w", otherActorPiRes.GetStatus(), err)
	}

	return otherActorPiRes.GetPaymentInstrument().GetVPA()
}

// isActionPending checks whether the pending action params need to be filled on not depending the type of recurring
// payment(currently only fills for mandate) and checks various states of the recurring payment, and depending on
// who initiates it, we decide whether to show the pending action params or not
// Also currently mandate request state is not taken into consideration
// Order for states is QUEUED -> INITIATED -> AUTHORIZED -> TERMINAL(EXPIRED/REVOKED)
// So <= means state before the current state and >= is vice versa
// Logic:
// Created by payer
// Current Actor Payer - pending params except in QUEUED state
// Current Actor Payee - state >= AUTHORISED + request status >= REQ_AUTH_RECEIVED
// Created by Payee
// Current Actor Payer - state >= INITIATED + request status >= REQ_AUTH_RECEIVED
// Current Actor Payee - pending params >= INITIATED
//
// Modified by payer
// Current Actor Payer - state >= AUTHORISED
// Current Actor Payee - state >= AUTHORISED + request status >= REQ_AUTH_RECEIVED
// Modified by Payee
// Current Actor Payer - state >= INITIATED + request status >= REQ_AUTH_RECEIVED
// Current Actor Payee - state >= INITIATED
//
// Revoke initiated by payer/payee
// if Current Actor Payer - state >= AUTHORISED + request status >= REQ_AUTH_RECEIVED
// if Current Actor Payee - state >= AUTHORISED`
func isActionPending(state pb.RecurringPaymentState, initiatedBy pb.InitiatedBy,
	recurringPaymentType pb.RecurringPaymentType, currActorId, fromActorId string) bool {

	isCurrentActorPayer := currActorId == fromActorId

	if recurringPaymentType == pb.RecurringPaymentType_UPI_MANDATES {
		switch {

		case state.IsCreationInProgress():
			if initiatedBy == pb.InitiatedBy_PAYER {
				return state == pb.RecurringPaymentState_CREATION_AUTHORISED || (isCurrentActorPayer && state == pb.RecurringPaymentState_CREATION_INITIATED)
			} else {
				return state != pb.RecurringPaymentState_CREATION_QUEUED
			}

		case state.IsModificationInProgress():
			if initiatedBy == pb.InitiatedBy_PAYER {
				return state == pb.RecurringPaymentState_MODIFY_AUTHORISED
			} else {
				return state != pb.RecurringPaymentState_MODIFY_QUEUED
			}

		case state.IsRevokeInProgress():
			return state == pb.RecurringPaymentState_REVOKE_AUTHORISED
		}
	}
	return false
}

// getActorRole checks if the current actor is payer or payee
// todo(Harleen Singh): evaluate moving this check to PI level
func getActorRole(currentActorId string, recurringPayment *pb.RecurringPayment) pb.ActorRole {
	if currentActorId == recurringPayment.GetFromActorId() {
		return pb.ActorRole_ACTOR_ROLE_PAYER
	}
	return pb.ActorRole_ACTOR_ROLE_PAYEE
}

// buildMandateUrn will build urn of type collect which can be used to execute mandate
func buildMandateUrn(recurringPayment *pb.RecurringPayment, umn, payeeVpa, collectMandatePrefix string) (string, error) {
	urnParams := url.Values{}

	amount := moneyPb.ToDecimal(recurringPayment.GetAmount())

	amountRule, ok := upi.AmountTypeEnumToStringMap[recurringPayment.GetAmountType()]
	if !ok {
		return "", fmt.Errorf("wrong amount type enum %s", recurringPayment.GetAmountType())
	}

	startDate := datetime.DateToString(datetime.TimeToDateInLoc(recurringPayment.GetInterval().GetStartTime().AsTime(), datetime.IST), upi.UPI_VALIDITY_DATE_FORMAT, datetime.IST)
	endDate := datetime.DateToString(datetime.TimeToDateInLoc(recurringPayment.GetInterval().GetEndTime().AsTime(), datetime.IST), upi.UPI_VALIDITY_DATE_FORMAT, datetime.IST)

	urnParams.Add(vendors.URNPayeeAddress, payeeVpa)
	urnParams.Add(vendors.MandateUrnUmn, umn)
	urnParams.Add(vendors.URNAmount, amount.String())
	urnParams.Add(vendors.MandateUrnNote, "Mandate execution")
	urnParams.Add(vendors.URNInitiationMode, vendors.DefaultInitiationModeMandateExecution)
	urnParams.Add(vendors.MandateUrnValidityStartDate, startDate)
	urnParams.Add(vendors.MandateUrnValidityEndDate, endDate)
	urnParams.Add(vendors.MandateUrnTxnAmountRule, amountRule)
	encodedURN, err := upiPkg.EncodeURL(urnParams)
	if err != nil {
		return "", err
	}
	return collectMandatePrefix + encodedURN, nil
}

func (s *Service) GetRecurringPayments(ctx context.Context, req *pb.GetRecurringPaymentsRequest) (*pb.GetRecurringPaymentsResponse, error) {
	var (
		res = &pb.GetRecurringPaymentsResponse{}
	)
	recurringPayments, err := s.recurringPaymentDao.GetByFromActorId(ctx, req.GetFromActorId(), dao.WithToActorFilter(req.GetToActorId()))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound):
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching recurring payments", zap.String(logger.ACTOR_ID, req.GetFromActorId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		res.RecurringPayments = recurringPayments
		res.Status = rpc.StatusOk()
		return res, nil
	}
}

// isMandatePauseAllowed checks if pause action is allowed on the mandate or not
// in case of any error we will send true to have a less restrictive check for pausing
func (s *Service) isMandatePauseAllowed(ctx context.Context, currentActorId string, recurringPayment *pb.RecurringPayment, isRevokeable bool) bool {
	if currentActorId != recurringPayment.GetFromActorId() || recurringPayment.GetRecurrenceRule().GetAllowedFrequency() == pb.AllowedFrequency_ONE_TIME {
		return false
	}
	// for certain mccs we want to block pause if the mandate isRevoke flag is N
	if !isRevokeable {
		getPiRes, err := s.piClient.GetPiById(ctx, &bePiPb.GetPiByIdRequest{Id: recurringPayment.GetPiTo()})
		if rpcErr := epifigrpc.RPCError(getPiRes, err); rpcErr != nil {
			logger.Error(ctx, "error fetching pi for PiId", zap.String(logger.PI_ID, getPiRes.GetPaymentInstrument().GetId()),
				zap.Error(err))
			return false
		}
		if lo.Contains(s.config.NonPauseableMccsForNonRevokeableMandates, getPiRes.GetPaymentInstrument().GetUpi().GetMerchantDetails().GetMcc()) {
			return false
		}
	}
	return true
}
