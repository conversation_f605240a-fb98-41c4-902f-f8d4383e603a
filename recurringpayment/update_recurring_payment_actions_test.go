package recurringpayment

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/dao/mocks"
)

func TestService_UpdateRecurringPaymentActions(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockRecurringPaymentActionsDao := mocks.NewMockRecurringPaymentsActionDao(ctr)

	svc := NewService(nil, nil, nil, mockRecurringPaymentActionsDao, nil, nil, nil, nil, nil, conf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	tests := []struct {
		name           string
		req            *recurringpayment.UpdateRecurringPaymentActionsRequest
		setupMockCalls func()
		want           *recurringpayment.UpdateRecurringPaymentActionsResponse
		wantErr        bool
	}{
		{
			name: "updated recurring payment actions with action detailed status successfully",
			req: &recurringpayment.UpdateRecurringPaymentActionsRequest{
				ReqId: "req-1",
				ActionDetailedStatus: &recurringpayment.ActionDetailedStatus{
					DetailedStatusList: []*recurringpayment.ActionDetailedStatus_DetailedStatus{
						{
							StatusCodePayer: "00",
							StatusCodePayee: "02",
						},
						{
							StatusCodePayer: "01",
							StatusCodePayee: "02",
						},
					},
				},
			},
			setupMockCalls: func() {
				mockRecurringPaymentActionsDao.EXPECT().GetByVendorRequestId(gomock.Any(), "req-1").Return(
					&recurringpayment.RecurringPaymentsAction{
						VendorRequestId: "req-1",
						State:           recurringpayment.ActionState_ACTION_CREATED,
					}, nil)
				mockRecurringPaymentActionsDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &recurringpayment.RecurringPaymentsAction{
					VendorRequestId: "req-1",
					ActionDetailedStatus: &recurringpayment.ActionDetailedStatus{
						DetailedStatusList: []*recurringpayment.ActionDetailedStatus_DetailedStatus{
							{
								StatusCodePayer: "00",
								StatusCodePayee: "02",
							},
							{
								StatusCodePayer: "01",
								StatusCodePayee: "02",
							},
						},
					},
					State: recurringpayment.ActionState_ACTION_CREATED,
				},
					[]recurringpayment.RecurringPaymentActionFieldMask{recurringpayment.RecurringPaymentActionFieldMask_ACTION_DETAILED_STATUS},
					recurringpayment.ActionState_ACTION_CREATED, recurringpayment.ActionState_ACTION_CREATED).
					Return(nil)
			},
			want: &recurringpayment.UpdateRecurringPaymentActionsResponse{
				Status: rpc.StatusOk(),
			},
		},
		{
			name: "failed to update the action detailed status due to record not found",
			req: &recurringpayment.UpdateRecurringPaymentActionsRequest{
				ReqId: "req-1",
				ActionDetailedStatus: &recurringpayment.ActionDetailedStatus{
					DetailedStatusList: []*recurringpayment.ActionDetailedStatus_DetailedStatus{
						{
							StatusCodePayer: "00",
							StatusCodePayee: "02",
						},
						{
							StatusCodePayer: "01",
							StatusCodePayee: "02",
						},
					},
				},
			},
			setupMockCalls: func() {
				mockRecurringPaymentActionsDao.EXPECT().GetByVendorRequestId(gomock.Any(), "req-1").Return(
					nil, epifierrors.ErrRecordNotFound)
			},
			want: &recurringpayment.UpdateRecurringPaymentActionsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "failed to update the action detailed status due to internal error",
			req: &recurringpayment.UpdateRecurringPaymentActionsRequest{
				ReqId: "req-1",
				ActionDetailedStatus: &recurringpayment.ActionDetailedStatus{
					DetailedStatusList: []*recurringpayment.ActionDetailedStatus_DetailedStatus{
						{
							StatusCodePayer: "00",
							StatusCodePayee: "02",
						},
						{
							StatusCodePayer: "01",
							StatusCodePayee: "02",
						},
					},
				},
			},
			setupMockCalls: func() {
				mockRecurringPaymentActionsDao.EXPECT().GetByVendorRequestId(gomock.Any(), "req-1").Return(
					nil, errors.New("internal error"))
			},
			want: &recurringpayment.UpdateRecurringPaymentActionsResponse{
				Status: rpc.StatusInternal(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.UpdateRecurringPaymentActions(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateRecurringPaymentActions() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateRecurringPaymentActions() got = %v, want %v", got, tt.want)
			}
		})
	}
}
