package recurringpayment

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	timestampPb "github.com/golang/protobuf/ptypes/timestamp"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/anypb"
	"gorm.io/gorm"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/queue"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	commspb "github.com/epifi/gamma/api/comms"
	depositPb "github.com/epifi/gamma/api/deposit"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	catalogManagerPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	merchantPb "github.com/epifi/gamma/api/merchant"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	pb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	savingsPb "github.com/epifi/gamma/api/savings"
	timelinePb "github.com/epifi/gamma/api/timeline"
	types "github.com/epifi/gamma/api/typesv2"
	upcomingTxnsPb "github.com/epifi/gamma/api/upcomingtransactions"
	upiPb "github.com/epifi/gamma/api/upi"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	userPb "github.com/epifi/gamma/api/user"
	usStocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	enachVg "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	"github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/pkg/recurringpayment"
	rpServerConfig "github.com/epifi/gamma/recurringpayment/config/server"
	rpServerGenConf "github.com/epifi/gamma/recurringpayment/config/server/genconf"
	"github.com/epifi/gamma/recurringpayment/dao"
	rpEvents "github.com/epifi/gamma/recurringpayment/events"
	"github.com/epifi/gamma/recurringpayment/internal"
	"github.com/epifi/gamma/recurringpayment/internal/actionstatusfetcher"
	"github.com/epifi/gamma/recurringpayment/internal/adaptor"
	"github.com/epifi/gamma/recurringpayment/internal/domaincreationprocessor"
	"github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor"
	"github.com/epifi/gamma/recurringpayment/internal/validationrules"
	types3 "github.com/epifi/gamma/recurringpayment/wire/types"
)

var (
	recurringPaymentProvenanceToOrderProvenanceMap = map[pb.RecurrencePaymentProvenance]orderPb.OrderProvenance{
		pb.RecurrencePaymentProvenance_RECURRENCE_PAYMENT_PROVENANCE_UNSPECIFIED: orderPb.OrderProvenance_ORDER_PROVENANCE_UNSPECIFIED,
		pb.RecurrencePaymentProvenance_USER_APP:                                  orderPb.OrderProvenance_USER_APP,
		pb.RecurrencePaymentProvenance_EXTERNAL:                                  orderPb.OrderProvenance_EXTERNAL,
	}
	recurringPaymentUIEntryPointToOrderUIEntryPoint = map[pb.UIEntryPoint]orderPb.UIEntryPoint{
		pb.UIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED: orderPb.UIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED,
		pb.UIEntryPoint_FIT:                        orderPb.UIEntryPoint_FITTT,
		pb.UIEntryPoint_TIMELINE:                   orderPb.UIEntryPoint_TIMELINE,
		pb.UIEntryPoint_AUTO_PAY_HUB:               orderPb.UIEntryPoint_AUTO_PAY_HUB,
		pb.UIEntryPoint_PRE_APPROVED_LOAN:          orderPb.UIEntryPoint_PRE_APPROVED_LOAN,
	}
	recurringPaymentActionToRequestTypeMap = map[pb.Action]siPb.RequestType{
		pb.Action_CREATE: siPb.RequestType_CREATE,
		pb.Action_MODIFY: siPb.RequestType_MODIFY,
		pb.Action_REVOKE: siPb.RequestType_REVOKE,
	}

	recurringPaymentToMandateInitiateByMap = map[pb.InitiatedBy]upiMandatePb.MandateInitiatedBy{
		pb.InitiatedBy_PAYER: upiMandatePb.MandateInitiatedBy_PAYER,
		pb.InitiatedBy_PAYEE: upiMandatePb.MandateInitiatedBy_PAYEE,
	}

	recurringPaymentToMandateAmountType = map[pb.AmountType]upiMandatePb.AmountRule{
		pb.AmountType_MAXIMUM: upiMandatePb.AmountRule_MAX,
		pb.AmountType_EXACT:   upiMandatePb.AmountRule_EXACT,
	}

	recurringPaymentRecurrenceRuleToMandateRule = map[pb.RecurrenceRule_RuleType]upiMandatePb.Recurrence_RecurrenceRule_RecurrenceRuleType{
		pb.RecurrenceRule_ON:     upiMandatePb.Recurrence_RecurrenceRule_ON,
		pb.RecurrenceRule_BEFORE: upiMandatePb.Recurrence_RecurrenceRule_BEFORE,
		pb.RecurrenceRule_AFTER:  upiMandatePb.Recurrence_RecurrenceRule_AFTER,
	}

	recurrencePatternToMandateRecurrencePattern = map[pb.AllowedFrequency]upiMandatePb.RecurrencePattern{
		pb.AllowedFrequency_DAILY:        upiMandatePb.RecurrencePattern_DAILY,
		pb.AllowedFrequency_WEEKLY:       upiMandatePb.RecurrencePattern_WEEKLY,
		pb.AllowedFrequency_MONTHLY:      upiMandatePb.RecurrencePattern_MONTHLY,
		pb.AllowedFrequency_AS_PRESENTED: upiMandatePb.RecurrencePattern_AS_PRESENTED,
		pb.AllowedFrequency_YEARLY:       upiMandatePb.RecurrencePattern_YEARLY,
		pb.AllowedFrequency_FORTNIGHTLY:  upiMandatePb.RecurrencePattern_FORTNIGHTLY,
		pb.AllowedFrequency_QUARTERLY:    upiMandatePb.RecurrencePattern_QUARTERLY,
		pb.AllowedFrequency_BI_MONTHLY:   upiMandatePb.RecurrencePattern_BI_MONTHLY,
		pb.AllowedFrequency_ONE_TIME:     upiMandatePb.RecurrencePattern_ONE_TIME,
		pb.AllowedFrequency_HALF_YEARLY:  upiMandatePb.RecurrencePattern_HALF_YEARLY,
	}

	recurringPaymentOwnershipToTimelineOwnershipMap = map[pb.RecurringPaymentOwnership]timelinePb.Ownership{
		pb.RecurringPaymentOwnership_EPIFI_TECH:   timelinePb.Ownership_EPIFI_TECH,
		pb.RecurringPaymentOwnership_EPIFI_WEALTH: timelinePb.Ownership_EPIFI_WEALTH,
	}

	recurringPaymentToMandateActorRole = map[pb.ActorRole]upiMandatePb.ActorRole{
		pb.ActorRole_ACTOR_ROLE_PAYEE: upiMandatePb.ActorRole_ACTOR_ROLE_PAYEE,
		pb.ActorRole_ACTOR_ROLE_PAYER: upiMandatePb.ActorRole_ACTOR_ROLE_PAYER,
	}

	failedPreconditionError = fmt.Errorf("failed precondition")
)

type Service struct {
	// UnimplementedPaymentServer is embedded to have forward compatible implementations
	pb.UnimplementedRecurringPaymentServiceServer
	recurringPaymentDao                           dao.RecurringPaymentDao
	orderClient                                   orderPb.OrderServiceClient
	recurringPaymentActionsDao                    dao.RecurringPaymentsActionDao
	siClient                                      siPb.StandingInstructionServiceClient
	savingsClient                                 savingsPb.SavingsClient
	actorClient                                   actorPb.ActorClient
	authClient                                    authPb.AuthClient
	userClient                                    userPb.UsersClient
	paymentClient                                 paymentPb.PaymentClient
	config                                        *rpServerConfig.Config
	dynamicConf                                   *rpServerGenConf.Config
	recurringPaymentProcessor                     internal.RecurringPaymentProcessor
	upiMandateClient                              upiMandatePb.MandateServiceClient
	accountPIRelationsClient                      accountPiPb.AccountPIRelationClient
	piClient                                      paymentinstrument.PiClient
	timelineClient                                timelinePb.TimelineServiceClient
	upiClient                                     upiPb.UPIClient
	commsClient                                   commspb.CommsClient
	siExecutionParamsConf                         *rpServerGenConf.SIExecutionParams
	featureFlags                                  *rpServerGenConf.FeatureFlags
	celestialProcessor                            internal.CelestialProcessor
	lockManager                                   lock.RwLockManager
	recurringPaymentRetryErrors                   map[string][]string
	inPaymentOrderPublisher                       queue.Publisher
	actionStatusFetcherFactory                    actionstatusfetcher.IActionStatusFetcherFactory
	enachClient                                   enachPb.EnachServiceClient
	domainCreationProcessorFactory                domaincreationprocessor.DomainCreationProcessorFactory
	paySavingsBalanceClient                       accountBalancePb.BalanceClient
	enachVgClient                                 enachVg.EnachClient
	celestialClient                               celestialPb.CelestialClient
	fetchAndCreateOffAppRecurringPaymentPublisher queue.Publisher
	domainExecutionProcessorFactory               domainexecutionprocessor.DomainExecutionProcessorFactory
	executionValidationChain                      validationrules.IValidationRule
	upcomingTxnClient                             upcomingTxnsPb.UpcomingTransactionsClient
	merchantClient                                merchantPb.MerchantServiceClient
	mfCatalogManagerClient                        catalogManagerPb.CatalogManagerClient
	depositClient                                 depositPb.DepositClient
	rpVendorDetailsDao                            dao.RecurringPaymentsVendorDetailsDao
	upiProcessor                                  internal.UpiProcessor
	txnExecutorProvider                           storageV2.IDbResourceProvider[storageV2.IdempotentTxnExecutor]
	eventBroker                                   events.Broker
	usStocksCatalogClient                         usStocksCatalogPb.CatalogManagerClient
}

func NewService(recurringPaymentDao dao.RecurringPaymentDao, orderClient orderPb.OrderServiceClient, siClient siPb.StandingInstructionServiceClient,
	recurringPaymentActionsDao dao.RecurringPaymentsActionDao, savingsClient savingsPb.SavingsClient, actorClient actorPb.ActorClient,
	authClient authPb.AuthClient, userClient userPb.UsersClient, paymentClient paymentPb.PaymentClient, config *rpServerConfig.Config,
	recurringPaymentProcessor internal.RecurringPaymentProcessor, upiMandateClient upiMandatePb.MandateServiceClient,
	accountPIRelationsClient accountPiPb.AccountPIRelationClient, piClient paymentinstrument.PiClient, timelineClient timelinePb.TimelineServiceClient,
	upiClient upiPb.UPIClient, commsClient commspb.CommsClient, siExecutionParamsConf *rpServerGenConf.SIExecutionParams,
	featureFlags *rpServerGenConf.FeatureFlags, celestialProcessor internal.CelestialProcessor, lockManager lock.RwLockManager,
	inPaymentOrderPublisher types3.InPaymentOrderUpdatePublisher, actionStatusFetcherFactory actionstatusfetcher.IActionStatusFetcherFactory,
	enachClient enachPb.EnachServiceClient, createRecurringPaymentFactory domaincreationprocessor.DomainCreationProcessorFactory,
	paySavingsBalanceClient accountBalancePb.BalanceClient, enachVgClient enachVg.EnachClient, celestialClient celestialPb.CelestialClient,
	fetchAndCreateOffAppRecurringPaymentPublisher types3.FetchAndCreateOffAppRecurringPaymentPublisher,
	domainExecutionProcessorFactory domainexecutionprocessor.DomainExecutionProcessorFactory, executionValidationChain validationrules.IValidationRule,
	upcomingTxnClient upcomingTxnsPb.UpcomingTransactionsClient, merchantClient merchantPb.MerchantServiceClient,
	mfCatalogManagerClient catalogManagerPb.CatalogManagerClient, depositClient depositPb.DepositClient, rpVendorDetailsDao dao.RecurringPaymentsVendorDetailsDao,
	upiProcessor internal.UpiProcessor, txnExecutorProvider storageV2.IDbResourceProvider[storageV2.IdempotentTxnExecutor], eventBroker events.Broker,
	usStocksCatalogClient usStocksCatalogPb.CatalogManagerClient, dynamicConf *rpServerGenConf.Config) *Service {
	return &Service{
		recurringPaymentDao:            recurringPaymentDao,
		orderClient:                    orderClient,
		siClient:                       siClient,
		recurringPaymentActionsDao:     recurringPaymentActionsDao,
		savingsClient:                  savingsClient,
		actorClient:                    actorClient,
		authClient:                     authClient,
		userClient:                     userClient,
		paymentClient:                  paymentClient,
		config:                         config,
		recurringPaymentProcessor:      recurringPaymentProcessor,
		upiMandateClient:               upiMandateClient,
		accountPIRelationsClient:       accountPIRelationsClient,
		piClient:                       piClient,
		timelineClient:                 timelineClient,
		upiClient:                      upiClient,
		commsClient:                    commsClient,
		siExecutionParamsConf:          siExecutionParamsConf,
		featureFlags:                   featureFlags,
		celestialProcessor:             celestialProcessor,
		recurringPaymentRetryErrors:    config.RecurringPaymentRetryErrors,
		lockManager:                    lockManager,
		inPaymentOrderPublisher:        inPaymentOrderPublisher,
		actionStatusFetcherFactory:     actionStatusFetcherFactory,
		enachClient:                    enachClient,
		domainCreationProcessorFactory: createRecurringPaymentFactory,
		paySavingsBalanceClient:        paySavingsBalanceClient,
		enachVgClient:                  enachVgClient,
		celestialClient:                celestialClient,
		fetchAndCreateOffAppRecurringPaymentPublisher: fetchAndCreateOffAppRecurringPaymentPublisher,
		domainExecutionProcessorFactory:               domainExecutionProcessorFactory,
		executionValidationChain:                      executionValidationChain,
		upcomingTxnClient:                             upcomingTxnClient,
		mfCatalogManagerClient:                        mfCatalogManagerClient,
		merchantClient:                                merchantClient,
		depositClient:                                 depositClient,
		rpVendorDetailsDao:                            rpVendorDetailsDao,
		upiProcessor:                                  upiProcessor,
		txnExecutorProvider:                           txnExecutorProvider,
		eventBroker:                                   eventBroker,
		usStocksCatalogClient:                         usStocksCatalogClient,
		dynamicConf:                                   dynamicConf,
	}
}

func (s *Service) checkRelationWithActor(ctx context.Context, currentActor, otherActor string) (*actorPb.GetRelationshipWithActorResponse, error) {
	return s.actorClient.GetRelationshipWithActor(ctx, &actorPb.GetRelationshipWithActorRequest{
		CurrentActorId: currentActor,
		OtherActorId:   otherActor,
	})
}

// nolint: funlen
func (s *Service) CreateRecurringPayment(ctx context.Context, req *pb.CreateRecurringPaymentRequest) (*pb.CreateRecurringPaymentResponse, error) {
	var (
		res                    = &pb.CreateRecurringPaymentResponse{}
		recurringPayment       *pb.RecurringPayment
		recurringPaymentAction *pb.RecurringPaymentsAction
		order                  *orderPb.Order
		status                 *rpc.Status
		transactionId          string
	)
	clientReqId := req.GetClientId().GetId()
	if clientReqId == "" {
		clientReqId = req.GetClientRequestId()
	}

	if clientReqId == "" {
		logger.Error(ctx, "ClientRequestId can't be empty")
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	block, err := s.checkRelationWithActor(ctx, req.FromActorId, req.ToActorId)
	if err != nil {
		logger.Error(ctx, "Failed to get Block actor", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if !block.GetStatus().IsSuccess() {
		logger.Error(ctx, "failed to get the from and to actor relation", zap.String(logger.FROM_ACTOR_ID, req.GetFromActorId()), zap.String(logger.TO_ACTOR_ID, req.GetToActorId()), zap.String(logger.STATUS, block.GetStatus().String()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if block.GetRelationship() == actorPb.GetRelationshipWithActorResponse_BLOCKED || block.GetRelationship() == actorPb.GetRelationshipWithActorResponse_REPORTED {
		res.Status = rpc.NewStatusWithoutDebug(uint32(pb.CreateRecurringPaymentResponse_BLOCKED_USER), "User is blocked")
		return res, nil
	}

	block1, err := s.checkRelationWithActor(ctx, req.GetToActorId(), req.GetFromActorId())

	if err != nil {
		logger.Error(ctx, "Failed to get Block actor", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if !block.Status.IsSuccess() {
		logger.Error(ctx, "failed to get the to and from actor relation", zap.String(logger.TO_ACTOR_ID, req.GetToActorId()), zap.String(logger.FROM_ACTOR_ID, req.GetFromActorId()), zap.String(logger.STATUS, block.GetStatus().String()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if block1.GetRelationship() == actorPb.GetRelationshipWithActorResponse_BLOCKED || block.GetRelationship() == actorPb.GetRelationshipWithActorResponse_REPORTED {
		res.Status = rpc.NewStatusWithoutDebug(uint32(pb.CreateRecurringPaymentResponse_BLOCKED_USER), "User is blocked")
		return res, nil
	}

	if s.featureFlags.EnableRecurringPaymentCreationViaCelestial() {
		var workflowStatusResponse *celestialPb.WorkflowRequest
		workflowStatusResponse, err = s.celestialProcessor.GetWorkflowByClientRequestId(ctx, clientReqId, workflowPb.Client_RECURRING_PAYMENT)
		status = rpc.StatusFromError(err)
		switch {
		case status.IsRecordNotFound():
			logger.Debug(ctx, "workflow record not found, new recurring payment creation requested",
				zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		case !status.IsSuccess():
			logger.Error(ctx, "failed to fetch workflow request for given client req id", zap.Error(err),
				zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
			res.Status = rpc.StatusFromError(err)
			return res, nil
		default:
			recurringPaymentAction, err = s.recurringPaymentProcessor.GetRecurringPaymentActionByClientReqId(ctx, clientReqId)
			if err != nil {
				logger.Error(ctx, "failed to fetch recurring payment action corresponding to the workflow", zap.Error(err),
					zap.String(logger.WORKFLOW_REQ_ID, workflowStatusResponse.GetId()))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			res.RecurringPaymentId = recurringPaymentAction.GetRecurringPaymentId()
			res.Status = rpc.StatusAlreadyExists()
			return res, nil
		}
	} else {
		order, status = s.getOrderByClientRequestId(ctx, clientReqId)
		switch {
		case status.IsSuccess():
			recurringPaymentAction, err = s.recurringPaymentProcessor.GetRecurringPaymentActionByClientReqId(ctx, clientReqId)
			if err != nil {
				logger.Error(ctx, "failed to fetch recurring payment action corresponding to the workflow", zap.Error(err),
					zap.String(logger.ORDER_ID, order.GetId()))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			res.RecurringPaymentId = recurringPaymentAction.GetRecurringPaymentId()
			res.Status = rpc.StatusAlreadyExists()
			res.OrderId = order.GetId()
			return res, nil
		case status.IsRecordNotFound():
			logger.Debug(ctx, "order record not found, new recurring payment creation requested",
				zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		default:
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}
	// create new reccuring payment
	status = s.validateRecurringPaymentCreation(
		req.GetRecurrenceRule().GetAllowedFrequency(),
		req.GetType(),
		req.GetInterval(),
		req.GetPayload(),
		req.GetAmount(),
	)
	if !status.IsSuccess() {
		logger.Error(ctx, "error while recurring payment creation")
		res.Status = status
		return res, nil
	}

	if req.GetTransactionId() == "" {
		transactionId, err = generateRecurringPaymentCreationTransactionId(req.GetType(), req.GetPartnerBank())
		if err != nil {
			logger.Error(ctx, "error while generating transaction id for recurring payment creation", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	} else {
		transactionId = req.GetTransactionId()
	}
	action, err := s.recurringPaymentProcessor.GetRecurringPaymentActionByClientReqId(ctx, clientReqId)
	status = rpc.StatusFromError(err)
	switch {
	case status.IsRecordNotFound():
		txnErr := storageV2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
			recurringPayment, err = s.createRecurringPaymentEntry(txnCtx, req)
			if err != nil {
				return fmt.Errorf("error while creating recurring payment entry %w", err)
			}
			_, err = s.createRecurringPaymentAction(txnCtx, recurringPayment.GetId(), clientReqId, transactionId,
				pb.Action_CREATE, nil, pb.ActionState_ACTION_CREATED, req.GetRemarks(), req.GetExpiry(),
				req.GetPostAuthorisationAction(), req.GetPostRecurringPaymentCreationDeeplink())
			if err != nil {
				return fmt.Errorf("error while creating action for recurring payment creation %w", err)
			}
			return nil
		})
		if txnErr != nil {
			logger.Error(ctx, "failed to commit transaction for creating recurring payment and action",
				zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(txnErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	case !status.IsSuccess():
		logger.Error(ctx, "error in fetching action for client request id", zap.String(logger.CLIENT_REQUEST_ID,
			clientReqId), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		// case when recurring payment and action already exists for the given client request id
		transactionId = action.GetVendorRequestId()
		recurringPayment, err = s.recurringPaymentDao.GetById(ctx, action.GetRecurringPaymentId())
		if err != nil {
			logger.Error(ctx, "error in fetching recurring payment for id", zap.String(logger.RECURRING_PAYMENT_ID,
				action.GetRecurringPaymentId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}
	isAuthRequired, credBlockType, err := s.recurringPaymentProcessor.GetCredBlockType(req.GetType(), getActorRole(req.GetCurrentActorId(), recurringPayment))
	if err != nil {
		logger.Error(ctx, "error in fetching cred block type", zap.Error(err))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}
	err = s.createDomainServiceEntity(ctx, recurringPayment, transactionId, req.GetCurrentActorId(), req.GetPayload(), req.GetPartnerBank())
	if err != nil {
		logger.Error(ctx, "error while creating domain service entity", zap.Error(err),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}

	if s.featureFlags.EnableRecurringPaymentCreationViaCelestial() {
		var rpPayload []byte
		rpPayload, err = protojson.Marshal(&pb.RecurringPaymentCreationInfo{
			RecurringPaymentId: recurringPayment.GetId(),
			ClientRequestId:    clientReqId,
			ReqId:              transactionId,
		})
		if err != nil {
			logger.Error(ctx, "error while marshalling recurring payment creation info %w", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		err = s.celestialProcessor.InitiateWorkflowV2(ctx, &workflowPb.ClientReqId{
			Id:     clientReqId,
			Client: workflowPb.Client_RECURRING_PAYMENT,
		}, req.GetFromActorId(), rpPayload,
			celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER), workflowPb.Version_V0, celestialPb.QoS_BEST_EFFORT)
		if err != nil {
			logger.Error(ctx, "error while initiating workflow", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	} else {
		order, err = s.createRecurringPaymentCreationOrder(ctx, req, recurringPayment.GetId(), transactionId, clientReqId)
		if err != nil {
			logger.Error(ctx, "error while creating order", zap.Error(err),
				zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}
	// creation already authorised triggering order processing
	if recurringPayment.GetState() == pb.RecurringPaymentState_CREATION_AUTHORISED {
		if s.featureFlags.EnableRecurringPaymentCreationViaCelestial() {
			// sending signal to workflow
			payload, marshalErr := protojson.Marshal(&payloadPb.CreateRecurringPaymentAuthSignal{})
			if marshalErr != nil {
				logger.Error(ctx, "error in marshalling create recurring payment auth signal payload", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			err = s.celestialProcessor.SignalWorkflow(ctx, clientReqId, string(rpNs.CreateRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, payload, recurringPayment.GetEntityOwnership(), recurringpayment.OwnershipToUseCaseForRecPay[recurringPayment.GetEntityOwnership()])
			if err != nil {
				logger.Error(ctx, "error while signaling workflow", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID,
					clientReqId))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
		} else {
			err = s.initiateOrderProcessing(ctx, clientReqId)
			if err != nil {
				logger.Error(ctx, "error while triggering order processing for creation", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID,
					clientReqId))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
		}
	}
	if isAuthRequired {
		transactionAttributes, err := s.getTransactionAttributes(ctx, recurringPayment, nil, transactionId, recurringPayment.GetAmount())
		if err != nil {
			logger.Error(ctx, "error in fetching transaction attributes", zap.Error(err), zap.String(logger.REQUEST_ID, transactionId))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.TransactionAttributes = transactionAttributes

		// please check the pin screen to be redirected here
		res.NextAction = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_FEDERAL_SECURE_PIN,
			ScreenOptions: &deeplinkPb.Deeplink_FederalSecurePinScreenOptions{
				FederalSecurePinScreenOptions: &deeplinkPb.FederalSecurePinScreenOptions{
					TxnAttributes: adaptor.ConvertToDeepLinkTransactionAttributes(transactionAttributes[0], clientReqId),
				},
			},
		}
	}
	if !s.featureFlags.EnableRecurringPaymentCreationViaCelestial() {
		_, err = s.ProcessRecurringPaymentsNotification(ctx, &orderPb.OrderUpdate{
			OrderWithTransactions: &orderPb.OrderWithTransactions{
				Order: order,
			},
		})
		if err != nil {
			logger.Error(ctx, "Error while sending recurringPayment notification: ",
				zap.String(logger.ORDER_ID, order.GetId()), zap.Error(err))
		}
	}
	logger.Info(ctx, "recurring payment creation triggered successfully", zap.String(logger.RECURRING_PAYMENT_ID,
		recurringPayment.GetId()), zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.String(logger.REQUEST_ID, transactionId))

	s.publishRecurringPaymentActionEvent(ctx, recurringPaymentAction, recurringPayment)

	res.IsAuthenticationRequired = isAuthRequired
	res.CredBlockType = credBlockType
	res.TransactionId = transactionId
	res.RecurringPaymentId = recurringPayment.GetId()
	res.OrderId = order.GetId()
	res.Status = rpc.StatusOk()
	return res, nil
}

// createDomainServiceEntity initiates creation of domain service entity such as Standing Instruction or UPI Mandates
func (s *Service) createDomainServiceEntity(
	ctx context.Context,
	recurringPayment *pb.RecurringPayment,
	requestId string,
	currentActorId string,
	creationPayload []byte,
	partnerBank commonvgpb.Vendor,
) error {
	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		res, err := s.siClient.Create(ctx, &siPb.CreateRequest{
			RecurringPaymentId: recurringPayment.GetId(),
			Vendor:             recurringPayment.GetPartnerBank(),
			RequestId:          requestId,
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			if res != nil && res.GetStatus().IsAlreadyExists() {
				return nil
			}
			return fmt.Errorf("error while creating standing instruction err %w %v", te, rpc.StatusAsError(rpc.StatusInternal()))
		}
		return nil
	case pb.RecurringPaymentType_UPI_MANDATES:
		var payload *upiMandatePb.MandateCreationPayload
		currentActorRole := upiMandatePb.ActorRole_ACTOR_ROLE_PAYEE
		if recurringPayment.GetFromActorId() == currentActorId {
			currentActorRole = upiMandatePb.ActorRole_ACTOR_ROLE_PAYER
		}
		if creationPayload != nil {
			payload = &upiMandatePb.MandateCreationPayload{}
			err := protojson.Unmarshal(creationPayload, payload)
			if err != nil {
				return fmt.Errorf("error unmarshalling payload %v %w", err, rpc.StatusAsError(rpc.StatusInternal()))
			}
		}

		createMandateReq := &upiMandatePb.CreateMandateRequest{
			RecurringPaymentId:    recurringPayment.GetId(),
			ReqId:                 requestId,
			Umn:                   payload.GetUmn(),
			Revokeable:            payload.GetRevokeable(),
			ShareToPayee:          payload.GetShareToPayee(),
			BlockFund:             payload.GetBlockFund(),
			InitiatedBy:           recurringPaymentToMandateInitiateByMap[recurringPayment.GetInitiatedBy()],
			MandateRequestPayload: payload.GetReqInfo(),
			CurrentActorRole:      currentActorRole,
			PartnerBank:           partnerBank,
			CurrentActorId:        currentActorId,
		}
		res, createErr := s.upiMandateClient.CreateMandate(ctx, createMandateReq)
		switch {
		case createErr != nil:
			return fmt.Errorf("error creating mandates %v %w", createErr, rpc.StatusAsError(rpc.StatusInternal()))
		case res.GetStatus().IsAlreadyExists():
			logger.Info(ctx, "mandate already exists", zap.String(logger.MANDATE_ID, res.GetMandate().GetId()))
			return nil
		case res.GetStatus().IsPermissionDenied():
			return fmt.Errorf("mandate creation not allowed %v %w", createErr, rpc.StatusAsError(rpc.StatusPermissionDenied()))
		case !res.GetStatus().IsSuccess():
			return fmt.Errorf("non success response while creating mandates %v %w", createErr, rpc.StatusAsError(rpc.StatusInternal()))
		}
		logger.Info(ctx, "mandate created successfully", zap.String(logger.MANDATE_ID, res.GetMandate().GetId()))
		return nil
	default:
		return fmt.Errorf("recurring payment type not supported %s %w", recurringPayment.GetType().String(), rpc.StatusAsError(rpc.StatusInvalidArgument()))
	}
}

// createRecurringPaymentCreationOrder creates an order with workflow as CREATE_RECURRING_PAYMENT and with
// payload as RecurringPaymentCreationInfo.
func (s *Service) createRecurringPaymentCreationOrder(ctx context.Context, req *pb.CreateRecurringPaymentRequest, recurringPaymentId string, reqId string, clientReqId string) (*orderPb.Order, error) {
	orderPayload, err := protojson.Marshal(&pb.RecurringPaymentCreationInfo{
		RecurringPaymentId: recurringPaymentId,
		ClientRequestId:    clientReqId,
		ReqId:              reqId,
	})
	if err != nil {
		return nil, fmt.Errorf("error while marshalling recurring payment creation info %w", err)
	}
	orderProvenance, ok := recurringPaymentProvenanceToOrderProvenanceMap[req.GetProvenance()]
	if !ok {
		return nil, fmt.Errorf("failed to fetch order provenance")
	}
	orderUIEntryPoint, ok := recurringPaymentUIEntryPointToOrderUIEntryPoint[req.GetUiEntryPoint()]
	if !ok {
		return nil, fmt.Errorf("failed to fetch order ui entry point")
	}
	createOrderRes, err := s.orderClient.CreateOrder(ctx, &orderPb.CreateOrderRequest{
		ActorFrom:              req.GetFromActorId(),
		ActorTo:                req.GetToActorId(),
		Workflow:               orderPb.OrderWorkflow_CREATE_RECURRING_PAYMENT,
		Provenance:             orderProvenance,
		OrderPayload:           orderPayload,
		Amount:                 req.GetAmount(),
		Status:                 orderPb.OrderStatus_CREATED,
		UiEntryPoint:           orderUIEntryPoint,
		IsDynamicQrInitialised: false,
		ClientReqId:            clientReqId,
	})
	if te := epifigrpc.RPCError(createOrderRes, err); te != nil {
		return nil, fmt.Errorf("error while creating order for recurringpaymentId %s, %w", recurringPaymentId, te)
	}
	return createOrderRes.GetOrder(), nil
}

// createRecurringPaymentEntry creates a new recurring payment entry with state as CREATION QUEUED
func (s *Service) createRecurringPaymentEntry(ctx context.Context, req *pb.CreateRecurringPaymentRequest) (*pb.RecurringPayment, error) {
	state, err := getRecurringPaymentRequestState(req.GetInitiatedBy(), req.GetFromActorId(), req.GetCurrentActorId(), pb.Action_CREATE)
	if err != nil {
		return nil, fmt.Errorf("error fetching recurring payment state")
	}
	recurringPayment, err := s.recurringPaymentDao.Create(ctx, &pb.RecurringPayment{
		FromActorId: req.GetFromActorId(),
		ToActorId:   req.GetToActorId(),
		Type:        req.GetType(),
		PiFrom:      req.GetPiFrom(),
		PiTo:        req.GetPiTo(),
		Amount:      req.GetAmount(),
		AmountType:  req.GetAmountType(),
		Interval:    req.GetInterval(),
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: req.GetRecurrenceRule().GetAllowedFrequency(),
			Day:              req.GetRecurrenceRule().GetDay(),
			RuleType:         req.GetRecurrenceRule().GetRuleType(),
		},
		MaximumAllowedTxns:       req.GetMaximumAllowedTxns(),
		PartnerBank:              req.GetPartnerBank(),
		PreferredPaymentProtocol: req.GetPreferredPaymentProtocol(),
		State:                    state,
		Ownership:                req.GetOwnership(),
		Provenance:               req.GetProvenance(),
		UiEntryPoint:             req.GetUiEntryPoint(),
		InitiatedBy:              req.GetInitiatedBy(),
		ShareToPayee:             req.GetShareToPayee(),
		Remarks:                  req.GetRemarks(),
	}, commontypes.Ownership_EPIFI_TECH)
	if err != nil {
		return nil, fmt.Errorf("error while creating recurring payment entry %w", err)
	}
	return recurringPayment, nil
}

func (s *Service) createRecurringPaymentAction(ctx context.Context, recurringPaymentId, clientReqId, requestId string,
	action pb.Action, actionMetadata *pb.ActionMetadata, state pb.ActionState, remarks string, expiry *timestampPb.Timestamp, postAuthorisationAction *deeplinkPb.Deeplink,
	postActionDeeplink *deeplinkPb.Deeplink) (*pb.RecurringPaymentsAction, error) {
	rpAction := &pb.RecurringPaymentsAction{
		RecurringPaymentId: recurringPaymentId,
		ClientRequestId:    clientReqId,
		Action:             action,
		ActionMetadata:     actionMetadata,
		State:              state,
		VendorRequestId:    requestId,
		Remarks:            remarks,
		ExpireAt:           expiry,
	}
	if postActionDeeplink != nil {
		rpAction.PostActionDeeplink = lo.Must(anypb.New(postActionDeeplink))
	}
	if postAuthorisationAction != nil {
		rpAction.PostAuthDeeplink = lo.Must(anypb.New(postAuthorisationAction))
	}
	recurringPaymentsActon, err := s.recurringPaymentActionsDao.Create(ctx, rpAction)
	if err != nil {
		return nil, fmt.Errorf("error while creating recurring payment action entry %w", err)
	}
	return recurringPaymentsActon, nil
}

func generateRecurringPaymentCreationTransactionId(recurringPaymentType pb.RecurringPaymentType, partnerBank commonvgpb.Vendor) (string, error) {
	switch recurringPaymentType {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		switch partnerBank {
		case commonvgpb.Vendor_FEDERAL_BANK:
			return idgen.FederalRandomDigitsSequence(pay.FederalCreateStandingInstructionPrefix, 5), nil
		default:
			return "", fmt.Errorf("vendor not supported for standing instruction creation %s", partnerBank.String())
		}
	case pb.RecurringPaymentType_UPI_MANDATES:
		switch partnerBank {
		case commonvgpb.Vendor_FEDERAL_BANK:
			return pay.GenerateVendorRequestId(partnerBank, paymentPb.PaymentProtocol_UPI)
		default:
			return "", fmt.Errorf("vendor not supported for standing instruction creation %s", partnerBank.String())
		}
	default:
		return "", fmt.Errorf("recurring payment type not supported %s", recurringPaymentType.String())
	}
}

func (s *Service) getOrderByClientRequestId(ctx context.Context, clientRequestId string) (*orderPb.Order, *rpc.Status) {
	getOrderRes, err := s.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{
		Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: clientRequestId},
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error while fetching order for client request id",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId), zap.Error(err))
		return nil, rpc.StatusInternal()
	case getOrderRes.GetStatus().IsSuccess():
		logger.Info(ctx, "fetched order successfully for client request id",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId))
		return getOrderRes.GetOrder(), rpc.StatusOk()
	case getOrderRes.GetStatus().IsRecordNotFound():
		logger.Debug(ctx, "order record not found, new recurring payment creation requested",
			zap.String(logger.CLIENT_REQUEST_ID, clientRequestId))
		return nil, rpc.StatusRecordNotFound()
	default:
		logger.Error(ctx, "non success status while fetching order", zap.Uint32(logger.STATUS_CODE, getOrderRes.GetStatus().GetCode()))
		return nil, rpc.StatusInternal()
	}
}

func (s *Service) getOrdersWithTransactions(ctx context.Context, orderWithTransactionsReq *orderPb.GetOrdersWithTransactionsRequest,
) ([]*orderPb.OrderWithTransactions, *rpc.Status) {
	ordersRes, err := s.orderClient.GetOrdersWithTransactions(ctx, orderWithTransactionsReq)
	switch {
	case err != nil:
		logger.Error(ctx, "error while fetching orders with Transactions", zap.Error(err))
		return nil, rpc.StatusInternal()
	case ordersRes.GetStatus().IsRecordNotFound():
		logger.Debug(ctx, "record not found while fetching orders with Transactions")
		return nil, rpc.StatusRecordNotFound()
	case ordersRes.GetStatus().IsSuccess():
		logger.Info(ctx, "fetched orders with Transactions successfully")
		return ordersRes.GetOrderWithTransactions(), rpc.StatusOk()
	default:
		logger.Error(ctx, "non success status while fetching order with Transactions", zap.Uint32(logger.STATUS_CODE, ordersRes.GetStatus().GetCode()))
		return nil, rpc.StatusInternal()
	}
}

func (s *Service) GetRecurringPaymentsCountForActor(ctx context.Context, req *pb.GetRecurringPaymentsCountForActorRequest) (*pb.GetRecurringPaymentsCountForActorResponse, error) {
	var (
		res = &pb.GetRecurringPaymentsCountForActorResponse{}
	)
	stateToCountMap, err := s.recurringPaymentDao.GetStateCountByActorId(ctx, req.GetActorId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no recurring payment exists for actor id")
		res.Status = rpc.StatusOk()
	case err != nil:
		logger.Error(ctx, "error in fetching recurring payments for actor", zap.Error(err))
		res.Status = rpc.StatusRecordNotFound()
	default:
		res.Count = stateToCountMap
		res.Status = rpc.StatusOk()
	}
	return res, nil
}

// nolint: funlen
func (s *Service) DeclineRecurringPaymentAction(ctx context.Context, req *pb.DeclineRecurringPaymentActionRequest) (*pb.DeclineRecurringPaymentActionResponse, error) {
	var (
		res           = &pb.DeclineRecurringPaymentActionResponse{}
		stateToUpdate pb.RecurringPaymentState
	)
	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "recurring payment record not found", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment fetched successfully", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
	}
	err = validateActorIdForRequest(recurringPayment, req.GetCurrentActorId())
	if err != nil {
		logger.Error(ctx, "failed to validate actor", zap.String(logger.ACTOR_ID, req.GetCurrentActorId()), zap.Error(err))
		res.Status = rpc.StatusPermissionDenied()
		return res, nil
	}
	recurringPaymentAction, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, req.GetClientRequestId(), false)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "action not found for client request id", zap.String(logger.RECURRING_PAYMENT_ID,
			req.GetRecurringPaymentId()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
		res.Status = rpc.StatusFailedPrecondition()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment action fetched successfully", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
	}
	if recurringPaymentAction.GetState() != pb.ActionState_ACTION_CREATED {
		logger.Error(ctx, "action not in expected state for declining request", zap.String(logger.STATE, recurringPaymentAction.GetState().String()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusFailedPrecondition()
		return res, nil
	}
	switch req.GetAction() {
	case pb.Action_CREATE:
		if !(recurringPayment.GetState() == pb.RecurringPaymentState_CREATION_QUEUED ||
			recurringPayment.GetState() == pb.RecurringPaymentState_CREATION_INITIATED) {
			logger.Error(ctx, "recurring payment not in expected state for decline creation request",
				zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.STATE, recurringPayment.GetState().String()))
			res.Status = rpc.StatusFailedPrecondition()
			return res, nil
		}
		stateToUpdate = pb.RecurringPaymentState_FAILED
	case pb.Action_MODIFY:
		if !(recurringPayment.GetState() == pb.RecurringPaymentState_MODIFY_QUEUED ||
			recurringPayment.GetState() == pb.RecurringPaymentState_MODIFY_INITIATED) {
			logger.Error(ctx, "recurring payment not in expected state for decline modify request",
				zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.STATE, recurringPayment.GetState().String()))
			res.Status = rpc.StatusFailedPrecondition()
			return res, nil
		}
		// We will only allow modify request in case of activated state.
		// So we will rollback state to ACTIVATED in case of decline
		stateToUpdate = pb.RecurringPaymentState_ACTIVATED
	case pb.Action_REVOKE:
		if !(recurringPayment.GetState() == pb.RecurringPaymentState_REVOKE_QUEUED ||
			recurringPayment.GetState() == pb.RecurringPaymentState_REVOKE_INITIATED) {
			logger.Error(ctx, "recurring payment not in expected state for decline revoke request",
				zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.STATE, recurringPayment.GetState().String()))
			res.Status = rpc.StatusFailedPrecondition()
			return res, nil
		}
		// We will only allow modify request in case of activated state.
		// So we will rollback state to ACTIVATED in case of decline
		stateToUpdate = pb.RecurringPaymentState_ACTIVATED
	default:
		logger.Error(ctx, "unsupported actions for decline", zap.String(logger.ACTION_TYPE, req.GetAction().String()),
			zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}
	status := s.declineActionAtDomainService(ctx, recurringPayment, req.GetAction(), recurringPaymentAction.GetVendorRequestId())
	if !status.IsSuccess() {
		logger.Error(ctx, "non success state in declining action at domain service",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()))
		res.Status = status
		return res, nil
	}
	err = s.updateRecurringPaymentAndActionInTxnBlock(ctx, recurringPaymentAction, recurringPayment,
		nil, nil, stateToUpdate, pb.ActionState_ACTION_FAILURE)
	if err != nil {
		logger.Error(ctx, "error in updating recurring payment and action state", zap.String(logger.RECURRING_PAYMENT_ID,
			recurringPayment.GetId()), zap.String(logger.STATE, stateToUpdate.String()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) declineActionAtDomainService(ctx context.Context, recurringPayment *pb.RecurringPayment, action pb.Action, requestId string) *rpc.Status {
	switch recurringPayment.GetType() {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		requestType, ok := recurringPaymentActionToRequestTypeMap[action]
		if !ok {
			logger.Error(ctx, "invalid action", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
				zap.String(logger.ACTION_TYPE, action.String()))
			return rpc.StatusInternalWithDebugMsg(fmt.Sprintf("invalid action %s", action.String()))
		}
		declineSIRes, err := s.siClient.DeclineAction(ctx, &siPb.DeclineActionRequest{
			RecurringPaymentId: recurringPayment.GetId(),
			RequestType:        requestType,
		})
		switch {
		case err != nil:
			logger.Error(ctx, "error in declining SI action", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
				zap.String(logger.ACTION_TYPE, action.String()), zap.Error(err))
			return rpc.StatusInternal()
		default:
			return declineSIRes.GetStatus()
		}
	case pb.RecurringPaymentType_UPI_MANDATES:
		declineMandateRes, err := s.upiMandateClient.DeclineMandateAction(ctx, &upiMandatePb.DeclineMandateActionRequest{ReqId: requestId})
		switch {
		case err != nil:
			logger.Error(ctx, "error in declining mandates action", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
				zap.String(logger.ACTION_TYPE, action.String()), zap.Error(err))
			return rpc.StatusInternal()
		default:
			return declineMandateRes.GetStatus()
		}
	default:
		logger.Error(ctx, "unsupported recurring payment type")
		return rpc.StatusInvalidArgument()
	}
}

// nolint: funlen, dupl
// GetRecurringPaymentActionStatus checks the order status and returns corresponding action states based on them
// We are using the client as workflowPb.Client_RECURRING_PAYMENT across recurring payment workflows to prevent exposure of backend information on Client side
func (s *Service) GetRecurringPaymentActionStatus(ctx context.Context, req *pb.GetRecurringPaymentActionStatusRequest) (*pb.GetRecurringPaymentActionStatusResponse, error) {
	var (
		res         = &pb.GetRecurringPaymentActionStatusResponse{}
		actionState pb.GetRecurringPaymentActionStatusResponse_ActionState
		status      *rpc.Status
		order       *orderPb.Order
	)
	clientReqId := req.GetClientId().GetId()
	if clientReqId == "" {
		clientReqId = req.GetClientRequestId()
	}

	if clientReqId == "" {
		logger.Error(ctx, "ClientRequestId can't be empty")
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	recurringPayment, err := s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound) || errors.Is(err, gorm.ErrRecordNotFound):
		logger.Error(ctx, "recurring payment record not found", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment fetched successfully", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
	}
	err = validateActorIdForRequest(recurringPayment, req.GetCurrentActorId())
	if err != nil {
		logger.Error(ctx, "failed to validate actor", zap.String(logger.ACTOR_ID, req.GetCurrentActorId()), zap.Error(err))
		res.Status = rpc.StatusPermissionDenied()
		return res, nil
	}

	action, err := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, clientReqId, false)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "action not found for client request id", zap.String(logger.RECURRING_PAYMENT_ID,
			req.GetRecurringPaymentId()), zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching recurring payment", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()),
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		logger.Debug(ctx, "recurring payment action fetched successfully", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()))
	}
	res.ActionDetailedStatus = recurringpayment.GetActionDetailedStatus(recurringPayment.GetPreferredPaymentProtocol(), action.GetActionDetailedStatus())

	switch req.GetAction() {
	case pb.Action_CREATE:
		if s.featureFlags.EnableRecurringPaymentCreationViaCelestial() {
			actionState, err = s.getActionStateUsingCelestial(ctx, &celestialPb.ClientReqId{
				Id:     clientReqId,
				Client: req.GetClientId().GetClient(),
			})
			status = rpc.StatusFromError(err)
			if !status.IsSuccess() {
				logger.Error(ctx, "failed to fetch action state using workflow", zap.Error(err),
					zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
				res.Status = rpc.StatusFromError(err)
				return res, nil
			}
		} else {
			actionState, err = s.getActionStateUsingOMS(ctx, clientReqId)
			status = rpc.StatusFromError(err)
			if !status.IsSuccess() {
				logger.Error(ctx, "failed to fetch action state using order", zap.Error(err),
					zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
				res.Status = rpc.StatusFromError(err)
				return res, nil
			}
		}
	case pb.Action_MODIFY:
		if s.featureFlags.EnableRecurringPaymentModificationViaCelestial() {
			actionState, err = s.getActionStateUsingCelestial(ctx, &celestialPb.ClientReqId{
				Id:     clientReqId,
				Client: req.GetClientId().GetClient(),
			})
			status = rpc.StatusFromError(err)
			if !status.IsSuccess() {
				logger.Error(ctx, "failed to fetch action state using workflow", zap.Error(err),
					zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
				res.Status = rpc.StatusFromError(err)
				return res, nil
			}
		} else {
			actionState, err = s.getActionStateUsingOMS(ctx, clientReqId)
			status = rpc.StatusFromError(err)
			if !status.IsSuccess() {
				logger.Error(ctx, "failed to fetch action state using order", zap.Error(err),
					zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
				res.Status = rpc.StatusFromError(err)
				return res, nil
			}
		}
	case pb.Action_REVOKE:
		if s.featureFlags.EnableRecurringPaymentRevokeViaCelestial() {
			actionState, err = s.getActionStateUsingCelestial(ctx, &celestialPb.ClientReqId{
				Id:     clientReqId,
				Client: req.GetClientId().GetClient(),
			})
			status = rpc.StatusFromError(err)
			if !status.IsSuccess() {
				logger.Error(ctx, "failed to fetch action state using workflow", zap.Error(err),
					zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
				res.Status = rpc.StatusFromError(err)
				return res, nil
			}
		} else {
			actionState, err = s.getActionStateUsingOMS(ctx, clientReqId)
			status = rpc.StatusFromError(err)
			if !status.IsSuccess() {
				logger.Error(ctx, "failed to fetch action state using order", zap.Error(err),
					zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
				res.Status = rpc.StatusFromError(err)
				return res, nil
			}
		}
	case pb.Action_PAUSE, pb.Action_UNPAUSE:
		if s.featureFlags.EnableRecurringPaymentRevokeViaCelestial() {
			actionState, err = s.getActionStateUsingCelestial(ctx, &celestialPb.ClientReqId{
				Id:     clientReqId,
				Client: req.GetClientId().GetClient(),
			})
			status = rpc.StatusFromError(err)
			if !status.IsSuccess() {
				logger.Error(ctx, "failed to fetch action state using workflow", zap.Error(err),
					zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
				res.Status = rpc.StatusFromError(err)
				return res, nil
			}
		} else {
			actionState, err = s.getActionStateUsingOMS(ctx, clientReqId)
			status = rpc.StatusFromError(err)
			if !status.IsSuccess() {
				logger.Error(ctx, "failed to fetch action state using order", zap.Error(err),
					zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
				res.Status = rpc.StatusFromError(err)
				return res, nil
			}
		}
	case pb.Action_EXECUTE:
		order, status = s.getOrderByClientRequestId(ctx, clientReqId)
		if !status.IsSuccess() {
			res.Status = status
			return res, nil
		}
		actionState, err = s.getActionStateForExecution(ctx, req, recurringPayment, order)
		status = rpc.StatusFromError(err)
		if !status.IsSuccess() {
			logger.Error(ctx, "failed to fetch action state for recurring payment execution", zap.Error(err),
				zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
			res.Status = rpc.StatusFromError(err)
			return res, nil
		}
		txns, _, err := s.getTxnsOfOrder(ctx, order.GetId())
		if err != nil {
			logger.Error(ctx, "error in fetching txns for order", zap.String(logger.ORDER_ID, order.GetId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		if len(txns) == 0 {
			logger.Error(ctx, "transactions does not exist for order", zap.String(logger.ORDER_ID, order.GetId()))
			res.ActionState = pb.GetRecurringPaymentActionStatusResponse_FAILURE
			res.Status = rpc.StatusOk()
			return res, nil
		}

		// We will only have a single successful transaction for each order
		// In case of transient failure, service will re-initiate execution request which will create a new transaction.
		// Since order with transactions return transactions sorted by created_at, to get latest transaction
		// taking last transaction.
		lastElementIndex := len(txns) - 1
		if lastElementIndex < 0 {
			logger.Error(ctx, "no transaction found against order", zap.String(logger.ORDER_ID, order.GetId()))
			res.ActionState = pb.GetRecurringPaymentActionStatusResponse_FAILURE
			return res, nil
		}
		txn := txns[lastElementIndex]
		var (
			executedAt          *timestampPb.Timestamp
			failureResponseCode string
		)
		switch txn.GetStatus() {
		case paymentPb.TransactionStatus_SUCCESS:
			executedAt = txn.GetExecutionTS()
		case paymentPb.TransactionStatus_FAILED:
			executedAt = txn.GetExecutionTS()
			if recurringPayment.GetFromActorId() == req.GetCurrentActorId() {
				failureResponseCode = txn.GetLatestTxnDetailedStatus().GetStatusCodePayer()
			} else {
				failureResponseCode = txn.GetLatestTxnDetailedStatus().GetStatusCodePayee()
			}
			if failureResponseCode == "" {
				failureResponseCode = txn.GetLatestTxnDetailedStatus().GetRawStatusCode()
			}
		default:
			logger.Debug(ctx, "txn not in success/failure state", zap.String(logger.TXN_ID, txn.GetId()))
		}
		res.MetaData = &pb.GetRecurringPaymentActionStatusResponse_ExecutionMetaData{
			ExecutionMetaData: &pb.ExecutionMetaData{
				UtrRefNumber:         txn.GetUtr(),
				TransactionTimestamp: executedAt,
				FailureResponseCode:  failureResponseCode,
				ExecProtocol:         txn.GetPaymentProtocol(),
			},
		}
	default:
		logger.Error(ctx, "invalid action type", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()),
			zap.String(logger.ACTION_TYPE, req.GetAction().String()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if res.WarningMessage, err = s.getMccWarningMessage(ctx, recurringPayment); err != nil {
		logger.Error(ctx, "error in fetching mcc-warning-message",
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
			zap.String(logger.PI_ID, recurringPayment.GetPiTo()), zap.Error(err))
	}

	postActionDeeplink, err := getPostActionDeeplink(action)
	if err != nil {
		logger.Error(ctx, "error while getting post action deeplink", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, action.GetRecurringPaymentId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if postActionDeeplink != nil {
		res.PostActionDeeplink = postActionDeeplink
	}
	res.ActionState = actionState
	res.Status = rpc.StatusOk()
	return res, nil
}

func getPostActionDeeplink(recurringPaymentAction *pb.RecurringPaymentsAction) (*deeplinkPb.Deeplink, error) {
	if recurringPaymentAction.GetPostActionDeeplink() == nil {
		return nil, nil
	}

	dl := &deeplinkPb.Deeplink{}
	err := recurringPaymentAction.GetPostActionDeeplink().UnmarshalTo(dl)
	if err != nil {
		return nil, fmt.Errorf("failed to generate next action deeplink: %w", err)
	}

	return dl, nil
}

// getRecurringPaymentState returns the relevant recurring payment state for the action
// based on if the request is initiate payer/payee and if the current actor is payer/payee
// for creation
// if the current actor is payer
//
//	if initiated by payer then returns CREATION_QUEUED
//	 if initiated by payee then returns CREATION_INITIATED
//
// if the current actor is payee
//
//	if initiated by payer then returns CREATION_AUTHORISED
//	if initiated by payee then returns CREATION_QUEUED
func getRecurringPaymentRequestState(
	initiatedBy pb.InitiatedBy,
	fromActorId, currentActorId string,
	action pb.Action,
) (pb.RecurringPaymentState, error) {
	isCurrentActorPayer := currentActorId == fromActorId
	isPayerInitiated := initiatedBy == pb.InitiatedBy_PAYER

	switch {
	case (isCurrentActorPayer && isPayerInitiated) || (!isPayerInitiated && !isCurrentActorPayer):
		switch action {
		case pb.Action_CREATE:
			return pb.RecurringPaymentState_CREATION_QUEUED, nil
		case pb.Action_MODIFY:
			return pb.RecurringPaymentState_MODIFY_QUEUED, nil
		case pb.Action_REVOKE:
			return pb.RecurringPaymentState_REVOKE_QUEUED, nil
		case pb.Action_PAUSE:
			return pb.RecurringPaymentState_PAUSE_QUEUED, nil
		case pb.Action_UNPAUSE:
			return pb.RecurringPaymentState_UNPAUSE_QUEUED, nil
		default:
			return 0, fmt.Errorf("action not supported :%s", action.String())
		}
	case isCurrentActorPayer && !isPayerInitiated:
		switch action {
		case pb.Action_CREATE:
			return pb.RecurringPaymentState_CREATION_INITIATED, nil
		case pb.Action_MODIFY:
			return pb.RecurringPaymentState_MODIFY_INITIATED, nil
		case pb.Action_REVOKE:
			// revoke is already authorised by the other actor
			return pb.RecurringPaymentState_REVOKE_AUTHORISED, nil
		case pb.Action_PAUSE:
			return pb.RecurringPaymentState_PAUSE_INITIATED, nil
		case pb.Action_UNPAUSE:
			return pb.RecurringPaymentState_UNPAUSE_INITIATED, nil
		default:
			return 0, fmt.Errorf("action not supported :%s", action.String())
		}
	case !isCurrentActorPayer && isPayerInitiated:
		switch action {
		case pb.Action_CREATE:
			return pb.RecurringPaymentState_CREATION_AUTHORISED, nil
		case pb.Action_MODIFY:
			return pb.RecurringPaymentState_MODIFY_AUTHORISED, nil
		case pb.Action_REVOKE:
			return pb.RecurringPaymentState_REVOKE_AUTHORISED, nil
		case pb.Action_PAUSE:
			return pb.RecurringPaymentState_PAUSE_AUTHORISED, nil
		case pb.Action_UNPAUSE:
			return pb.RecurringPaymentState_UNPAUSE_AUTHORISED, nil
		default:
			return 0, fmt.Errorf("action not supported :%s", action.String())
		}

	}
	return pb.RecurringPaymentState_RECURRING_PAYMENT_STATE_UNSPECIFIED, fmt.Errorf("unable to fetch recurring payment state")
}

// checkMandateRequestStatus checks the mandate request with domain service
func (s *Service) checkMandateRequestStatus(
	ctx context.Context,
	reqId string,
	recurringPayment *pb.RecurringPayment,
) error {

	internalPiId, err := s.getInternalPiId(ctx, recurringPayment.GetPiFrom(), recurringPayment.GetPiTo())
	if err != nil {
		return fmt.Errorf("error fetchif internal pi id: %w", err)
	}

	req := &upiMandatePb.FetchAndUpdateRequestStatusRequest{
		ReqId:           reqId,
		PartnerBank:     recurringPayment.GetPartnerBank(),
		EpifiCustomerPi: internalPiId,
	}
	statusRes, err := s.upiMandateClient.FetchAndUpdateRequestStatus(ctx, req)

	switch {
	case err != nil:
		err = fmt.Errorf("error fetching mandate status :%v :%w", err, epifierrors.ErrTransient)
	case statusRes.GetStatus().GetCode() == uint32(upiMandatePb.FetchAndUpdateRequestStatusResponse_IN_PROGRESS):
		err = fmt.Errorf("mandate request in progress :%w", epifierrors.ErrInProgress)
	case statusRes.GetStatus().GetCode() == uint32(upiMandatePb.FetchAndUpdateRequestStatusResponse_TRANSIENT_FAILURE):
		err = fmt.Errorf("mandate request failed with transient failure :%w", epifierrors.ErrTransient)
	case statusRes.GetStatus().GetCode() == uint32(upiMandatePb.FetchAndUpdateRequestStatusResponse_PERMANENT_FAILURE):
		err = fmt.Errorf("mandate request failed with permanent failure :%w", errActionFailed)
	case statusRes.GetStatus().GetCode() == uint32(upiMandatePb.FetchAndUpdateRequestStatusResponse_TOO_EARLY_FOR_VENDOR_CALL):
		err = fmt.Errorf("too early to make vendor call for mandate request: %w", epifierrors.ErrInProgress)
	case statusRes.GetStatus().IsSuccess():
		err = nil
	default:
		err = fmt.Errorf("return unknown status code while fetching status for mandate :%s %w",
			statusRes.GetStatus().String(), epifierrors.ErrTransient)
	}

	if statusRes.GetActionDetailedStatus() != nil {
		updateRecurringPaymentActionRes, updateErr := s.UpdateRecurringPaymentActions(ctx, &pb.UpdateRecurringPaymentActionsRequest{
			ReqId:                reqId,
			ActionDetailedStatus: statusRes.GetActionDetailedStatus(),
		})

		if rpcErr := epifigrpc.RPCError(updateRecurringPaymentActionRes, updateErr); rpcErr != nil {
			return fmt.Errorf("error updating recurring payment actions: %v %w", rpcErr, epifierrors.ErrTransient)
		}
	}

	return err
}

// getInternalPiId returns the pi id belonging to internal user
func (s *Service) getInternalPiId(ctx context.Context, piFrom, piTo string) (string, error) {
	res, err := s.piClient.GetPIsByIds(ctx, &paymentinstrument.GetPIsByIdsRequest{Ids: []string{piFrom, piTo}})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		if res.GetStatus().IsRecordNotFound() {
			return "", fmt.Errorf("no pi found :%v :%w", rpcErr, epifierrors.ErrPermanent)
		}
		return "", fmt.Errorf("error fetching pi :%v :%w", rpcErr, epifierrors.ErrTransient)
	}

	for _, pi := range res.GetPaymentinstruments() {
		if pi.IsIssuedInternally() {
			return pi.GetId(), nil
		}
	}
	return "", fmt.Errorf("no internal pis found :%w", epifierrors.ErrPermanent)
}

// authoriseMandateAction calls the mandate
// service to authorise a mandate action
func (s *Service) authoriseMandateAction(
	ctx context.Context,
	credential *pb.Credential,
	recurringPayment *pb.RecurringPayment,
	recurringPaymentAction *pb.RecurringPaymentsAction,
	currentActorId string,
) (*pb.ActionDetailedStatus_DetailedStatus, error) {

	mandateValidity, amount, err := getMandateValidityAndAmount(recurringPaymentAction, recurringPayment)
	if err != nil {
		return nil, fmt.Errorf("error getting validity and amount for mandate :%w", err)
	}
	initiateMandateReq := &upiMandatePb.AuthoriseMandateActionRequest{
		AuthHeader: &upiMandatePb.AuthoriseMandateActionRequest_AuthHeader{
			Device:        credential.GetMandateHeader().GetDevice(),
			NpciCredBlock: credential.GetMandateHeader().GetNpciCredBlock(),
		},
		ReqId:           recurringPaymentAction.GetVendorRequestId(),
		CurrentActorId:  currentActorId,
		FromActorId:     recurringPayment.GetFromActorId(),
		ToActorId:       recurringPayment.GetToActorId(),
		FromPiId:        recurringPayment.GetPiFrom(),
		ToPiId:          recurringPayment.GetPiTo(),
		MandateValidity: mandateValidity,
		PartnerBank:     recurringPayment.GetPartnerBank(),
		Amount:          amount,
		AmountRule:      recurringPaymentToMandateAmountType[recurringPayment.GetAmountType()],
		Recurrence: &upiMandatePb.Recurrence{
			RecurrencePattern: recurrencePatternToMandateRecurrencePattern[recurringPayment.GetRecurrenceRule().GetAllowedFrequency()],
		},
		Remarks: recurringPaymentAction.GetRemarks(),
	}
	// recurrence rule should only be present if frequency is not daily, one time, as presented
	if recurringPayment.GetRecurrenceRule().GetAllowedFrequency() != pb.AllowedFrequency_DAILY &&
		recurringPayment.GetRecurrenceRule().GetAllowedFrequency() != pb.AllowedFrequency_ONE_TIME &&
		recurringPayment.GetRecurrenceRule().GetAllowedFrequency() != pb.AllowedFrequency_AS_PRESENTED {
		initiateMandateReq.Recurrence.RecurrenceRule = &upiMandatePb.Recurrence_RecurrenceRule{
			Value:              strconv.Itoa(int(recurringPayment.GetRecurrenceRule().GetDay())),
			RecurrenceRuleType: recurringPaymentRecurrenceRuleToMandateRule[recurringPayment.GetRecurrenceRule().GetRuleType()],
		}
	}

	initiateRes, err := s.upiMandateClient.AuthoriseMandateAction(ctx, initiateMandateReq)
	if resErr := epifigrpc.RPCError(initiateRes, err); resErr != nil {
		// suppressing the error as we will fallback to enquiry for the final mandate creation status
		logger.Error(ctx, "error authorising upi mandate", zap.Error(resErr))
	}

	// update recurring payment action detailed status
	if initiateRes.GetActionDetailedStatus() != nil {

		updateRecurringPaymentActionRes, err := s.UpdateRecurringPaymentActions(ctx, &pb.UpdateRecurringPaymentActionsRequest{
			ReqId:                recurringPaymentAction.GetVendorRequestId(),
			ActionDetailedStatus: initiateRes.GetActionDetailedStatus(),
		})

		if rpcErr := epifigrpc.RPCError(updateRecurringPaymentActionRes, err); rpcErr != nil {
			logger.Error(ctx, "error updating action detailed status", zap.Error(rpcErr))
		}
	}
	return recurringpayment.GetActionDetailedStatus(recurringPayment.GetPreferredPaymentProtocol(), initiateRes.GetActionDetailedStatus()), nil
}

// getRecurringPaymentStateForCreation returns the state to
// which recurring payment will be updated after initiation
func (s *Service) getRecurringPaymentStateForInitiation(
	ctx context.Context,
	currentActorId, fromActorId string,
	action pb.Action,
) (pb.RecurringPaymentState, error) {
	var (
		isPayerInternal bool
		err             error
	)
	isPayerInternal, err = s.isActorInternal(ctx, fromActorId)
	if err != nil {
		return pb.RecurringPaymentState_RECURRING_PAYMENT_STATE_UNSPECIFIED,
			fmt.Errorf("error checking if payer actor is internal :%w", err)
	}

	if isPayerInternal && fromActorId != currentActorId {
		switch action {
		case pb.Action_CREATE:
			return pb.RecurringPaymentState_CREATION_INITIATED, nil
		case pb.Action_MODIFY:
			return pb.RecurringPaymentState_MODIFY_INITIATED, nil
		case pb.Action_REVOKE:
			// for revoke the other actor doesn't needs to authorise
			return pb.RecurringPaymentState_REVOKE_AUTHORISED, nil
		case pb.Action_PAUSE:
			return pb.RecurringPaymentState_PAUSE_INITIATED, nil
		case pb.Action_UNPAUSE:
			return pb.RecurringPaymentState_UNPAUSE_INITIATED, nil
		default:
			return pb.RecurringPaymentState_RECURRING_PAYMENT_STATE_UNSPECIFIED,
				fmt.Errorf("action not supported: %s", action.String())
		}
	}

	switch action {
	case pb.Action_CREATE:
		return pb.RecurringPaymentState_CREATION_AUTHORISED, nil
	case pb.Action_MODIFY:
		return pb.RecurringPaymentState_MODIFY_AUTHORISED, nil
	case pb.Action_REVOKE:
		return pb.RecurringPaymentState_REVOKE_AUTHORISED, nil
	case pb.Action_PAUSE:
		return pb.RecurringPaymentState_PAUSE_AUTHORISED, nil
	case pb.Action_UNPAUSE:
		return pb.RecurringPaymentState_UNPAUSE_AUTHORISED, nil
	default:
		return pb.RecurringPaymentState_RECURRING_PAYMENT_STATE_UNSPECIFIED,
			fmt.Errorf("action not supported: %s", action.String())
	}
}

// isActorInternal checks if the actor is internal or not
func (s *Service) isActorInternal(ctx context.Context, actorId string) (bool, error) {
	res, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return false, fmt.Errorf("error fetching actor by id :%s :%w", actorId, rpcErr)
	}
	return res.GetActor().GetType() == types.Actor_USER || res.GetActor().GetType() == types.Actor_MERCHANT, nil
}

func (s *Service) validateRecurringPaymentCreation(
	allowedFrequency pb.AllowedFrequency,
	recurringPaymentType pb.RecurringPaymentType,
	interval *types.Interval,
	creationPayload []byte,
	recurringPaymentAmount *money.Money,
) *rpc.Status {

	var (
		payload   = &upiMandatePb.MandateCreationPayload{}
		startTime = interval.GetStartTime().AsTime()
		endTime   = interval.GetEndTime().AsTime()
		currTime  = time.Now().UTC()
		status    = rpc.StatusOk()
	)

	switch {
	// if start time is greater than end time
	case startTime.After(endTime):
		status = rpc.NewStatusWithoutDebug(uint32(pb.CreateRecurringPaymentResponse_INVALID_START_AND_END_DATE),
			"invalid start and end date for recurring payment")
	// if current time is greater than start time
	case currTime.After(endTime):
		status = rpc.NewStatusWithoutDebug(uint32(pb.CreateRecurringPaymentResponse_INVALID_START_AND_END_DATE),
			"invalid start and end date for recurring payment")
	}

	if !status.IsSuccess() {
		return status
	}

	if recurringPaymentType == pb.RecurringPaymentType_UPI_MANDATES {
		if creationPayload != nil {
			err := protojson.Unmarshal(creationPayload, payload)
			if err != nil {
				return rpc.StatusInvalidArgument()
			}
		}
		status = s.validateMandateCreation(
			endTime,
			allowedFrequency,
			payload.GetReqInfo().GetMcc(),
			payload.GetReqInfo().GetPurpose(),
			recurringPaymentAmount,
		)
	}

	return status
}

// validateActionExpiry checks expiry of the action. If the action is expired we will update the action state to expired and
// recurring payment state to the input stateToUpdate
func (s *Service) validateActionExpiry(ctx context.Context, action *pb.RecurringPaymentsAction, recurringPayment *pb.RecurringPayment,
	stateToUpdate pb.RecurringPaymentState) *rpc.Status {
	if action.GetExpireAt() != nil && time.Since(action.GetExpireAt().AsTime()) > 0 {
		logger.Error(ctx, "action expired", zap.String(logger.RECURRING_PAYMENT_ID, recurringPayment.GetId()),
			zap.String(logger.CLIENT_REQUEST_ID, action.GetClientRequestId()))
		err := s.updateRecurringPaymentAndActionInTxnBlock(ctx, action, recurringPayment,
			nil, nil, stateToUpdate, pb.ActionState_ACTION_EXPIRED)
		if err != nil {
			logger.Error(ctx, "failed to update recurring payment and action", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID,
				recurringPayment.GetId()))
			return rpc.StatusInternal()
		}
		return rpc.StatusFailedPrecondition()
	}
	return nil
}

// checkAndUpdateRecurringPaymentState checks the recurring payment end date and moves it to completed state if current time is greater than end time
// It also check current recurring payment state and updates the state to pause/activated based on the pause interval
func (s *Service) checkAndUpdateRecurringPaymentState(ctx context.Context, recurringPayment *pb.RecurringPayment) (*pb.RecurringPayment, error) {
	var (
		updateFieldMask []pb.RecurringPaymentFieldMask
		stateToUpdate   pb.RecurringPaymentState
	)
	switch {
	case recurringPayment.GetInterval().GetEndTime() != nil && time.Since(recurringPayment.GetInterval().GetEndTime().AsTime()) >= 0 && recurringPayment.GetState() != pb.RecurringPaymentState_COMPLETED:
		stateToUpdate = pb.RecurringPaymentState_COMPLETED
	case recurringPayment.GetState() == pb.RecurringPaymentState_PAUSED && recurringPayment.GetPauseInterval() != nil &&
		time.Since(recurringPayment.GetPauseInterval().GetEndTime().AsTime()) > 0:
		recurringPayment.PauseInterval = nil
		updateFieldMask = append(updateFieldMask, pb.RecurringPaymentFieldMask_PAUSE_INTERVAL)
		stateToUpdate = pb.RecurringPaymentState_ACTIVATED
	// current time greater than end time
	case recurringPayment.GetState() == pb.RecurringPaymentState_TO_BE_PAUSED &&
		recurringPayment.GetPauseInterval() != nil && time.Since(recurringPayment.GetPauseInterval().GetEndTime().AsTime()) > 0:
		recurringPayment.PauseInterval = nil
		stateToUpdate = pb.RecurringPaymentState_ACTIVATED
		updateFieldMask = append(updateFieldMask, pb.RecurringPaymentFieldMask_PAUSE_INTERVAL)
		// current time greater than start time and less than end time
	case recurringPayment.GetState() == pb.RecurringPaymentState_TO_BE_PAUSED &&
		recurringPayment.GetPauseInterval() != nil && time.Since(recurringPayment.GetPauseInterval().GetStartTime().AsTime()) > 0:
		stateToUpdate = pb.RecurringPaymentState_PAUSED
	default:
		return recurringPayment, nil
	}
	if stateToUpdate != pb.RecurringPaymentState_RECURRING_PAYMENT_STATE_UNSPECIFIED {
		err := s.updateRecurringPayment(ctx, recurringPayment, updateFieldMask,
			recurringPayment.GetState(), stateToUpdate)
		if err != nil {
			return nil, fmt.Errorf("error in updating recurring payment state %w", err)
		}
		recurringPayment.State = stateToUpdate
	}
	return recurringPayment, nil
}

func getMandateValidityAndAmount(recurringPaymentAction *pb.RecurringPaymentsAction, recurringPayment *pb.RecurringPayment) (*upiMandatePb.Validity, *money.Money, error) {

	var (
		mandateValidity *upiMandatePb.Validity
		amount          *money.Money
		err             error
	)

	switch recurringPaymentAction.GetAction() {
	case pb.Action_MODIFY:
		updatedParams := recurringPaymentAction.GetActionMetadata().GetModifyActionMetadata().GetUpdatedParams()
		mandateValidity = &upiMandatePb.Validity{
			Start: datetime.TimeToDateInLoc(updatedParams.GetInterval().GetStartTime().AsTime(), datetime.IST),
			End:   datetime.TimeToDateInLoc(updatedParams.GetInterval().GetEndTime().AsTime(), datetime.IST),
		}
		amount = recurringPaymentAction.GetActionMetadata().GetModifyActionMetadata().GetUpdatedParams().GetMaximumAmountLimit()
	case pb.Action_PAUSE:
		mandateValidity = &upiMandatePb.Validity{
			Start: datetime.TimeToDateInLoc(recurringPaymentAction.GetActionMetadata().GetPauseActionMetadata().GetPauseInterval().GetStartTime().AsTime(), datetime.IST),
			End:   datetime.TimeToDateInLoc(recurringPaymentAction.GetActionMetadata().GetPauseActionMetadata().GetPauseInterval().GetEndTime().AsTime(), datetime.IST),
		}
		amount = recurringPayment.GetAmount()
	case pb.Action_CREATE, pb.Action_REVOKE, pb.Action_UNPAUSE:
		mandateValidity = &upiMandatePb.Validity{
			Start: datetime.TimeToDateInLoc(recurringPayment.GetInterval().GetStartTime().AsTime(), datetime.IST),
			End:   datetime.TimeToDateInLoc(recurringPayment.GetInterval().GetEndTime().AsTime(), datetime.IST),
		}
		amount = recurringPayment.GetAmount()
	default:
		err = fmt.Errorf("unsupported recurring payment action")
	}

	return mandateValidity, amount, err
}

// checkPendingActions checks if pending actions exists for a given action
// If actions exists then we will check if the action client request id same as the client request id received in request
// which implies that the same request is being re-tried otherwise we will return error
func (s *Service) checkPendingActions(ctx context.Context, recurringPaymentId, clientRequestId string, action pb.Action) error {
	actions, err := s.recurringPaymentActionsDao.GetByRecurringPaymentId(ctx, recurringPaymentId,
		dao.WithActionsFilter([]pb.Action{action}), dao.WithActionStateFilter([]pb.ActionState{pb.ActionState_ACTION_CREATED}))
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil
	case err != nil:
		return fmt.Errorf("error while fetching pending actions %w", err)
	case len(actions) == 1 && actions[0].GetClientRequestId() == clientRequestId:
		logger.Info(ctx, "retrying the same request", zap.String(logger.CLIENT_REQUEST_ID, clientRequestId),
			zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId))
		return nil
	default:
		return failedPreconditionError
	}
}

// initiateOrderProcessing initiates order processing for an order
// We will initiate order processing post user authorisation is done
func (s *Service) initiateOrderProcessing(ctx context.Context, clientReqId string) error {
	triggerOrderProcessingRes, err := s.orderClient.InitiateOrderProcessing(ctx, &orderPb.InitiateOrderProcessingRequest{
		Identifier: &orderPb.InitiateOrderProcessingRequest_ClientReqId{ClientReqId: clientReqId},
	})
	switch {
	case err != nil:
		return fmt.Errorf("error in initiating order processing %w", err)
	case triggerOrderProcessingRes.GetStatus().IsAlreadyProcessed() || triggerOrderProcessingRes.GetStatus().IsAlreadyExists():
		logger.Info(ctx, "order processing already initiated", zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		return nil
	case triggerOrderProcessingRes.GetStatus().IsSuccess():
		return nil
	default:
		return fmt.Errorf("non success state for initiating order processing %s", triggerOrderProcessingRes.GetStatus().String())
	}
}

func (s *Service) getActionStateUsingOMS(ctx context.Context, clientReqId string) (
	pb.GetRecurringPaymentActionStatusResponse_ActionState, error) {
	var actionState pb.GetRecurringPaymentActionStatusResponse_ActionState
	order, status := s.getOrderByClientRequestId(ctx, clientReqId)
	if !status.IsSuccess() {
		return pb.GetRecurringPaymentActionStatusResponse_ACTION_STATE_UNSPECIFIED, fmt.Errorf("invalid workflow status %w", rpc.StatusAsError(status))
	}
	switch order.GetStatus() {
	case orderPb.OrderStatus_CREATED, orderPb.OrderStatus_IN_FULFILLMENT, orderPb.OrderStatus_MANUAL_INTERVENTION:
		actionState = pb.GetRecurringPaymentActionStatusResponse_IN_PROGRESS
	case orderPb.OrderStatus_FULFILLED:
		actionState = pb.GetRecurringPaymentActionStatusResponse_SUCCESS
	case orderPb.OrderStatus_FULFILLMENT_FAILED:
		actionState = pb.GetRecurringPaymentActionStatusResponse_FAILURE
	default:
		return pb.GetRecurringPaymentActionStatusResponse_ACTION_STATE_UNSPECIFIED, fmt.Errorf("invalid order status %w", rpc.StatusAsError(rpc.StatusInternal()))
	}

	return actionState, nil
}

func (s *Service) getActionStateUsingCelestial(ctx context.Context, clientId *celestialPb.ClientReqId) (pb.GetRecurringPaymentActionStatusResponse_ActionState, error) {
	var (
		actionState pb.GetRecurringPaymentActionStatusResponse_ActionState
		workflow    *celestialPb.WorkflowRequest
		err         error
	)
	workflow, err = s.celestialProcessor.GetWorkflowByClientRequestId(ctx, clientId.GetId(), workflowPb.Client_RECURRING_PAYMENT)
	workflowStatusFromErr := rpc.StatusFromError(err)
	// fallback to the client passed in the request if record is not found via workflowPb.Client_RECURRING_PAYMENT
	// NOTE: this is only for backward compatability since we have moved to workflowPb.Client_RECURRING_PAYMENT and the fallback logic is for the workflows created before this change
	if workflowStatusFromErr.IsRecordNotFound() {
		workflow, err = s.celestialProcessor.GetWorkflowByClientRequestId(ctx, clientId.GetId(), clientId.GetClient())
		workflowStatusFromErr = rpc.StatusFromError(err)
	}
	switch {
	case workflowStatusFromErr.IsRecordNotFound():
		return s.getActionStateUsingOMS(ctx, clientId.GetId())
	case !workflowStatusFromErr.IsSuccess():
		return pb.GetRecurringPaymentActionStatusResponse_ACTION_STATE_UNSPECIFIED, err
	default:
		switch workflow.GetStatus() {
		case stagePb.Status_BLOCKED, stagePb.Status_INITIATED, stagePb.Status_MANUAL_INTERVENTION, stagePb.Status_CREATED:
			actionState = pb.GetRecurringPaymentActionStatusResponse_IN_PROGRESS
		case stagePb.Status_SUCCESSFUL:
			actionState = pb.GetRecurringPaymentActionStatusResponse_SUCCESS
		case stagePb.Status_FAILED:
			actionState = pb.GetRecurringPaymentActionStatusResponse_FAILURE
		default:
			return pb.GetRecurringPaymentActionStatusResponse_ACTION_STATE_UNSPECIFIED, fmt.Errorf("invalid workflow status %w", rpc.StatusAsError(rpc.StatusInternal()))
		}
	}
	return actionState, nil
}

// getActionStateForExecution - fetches the action state for recurring payment based on feature flags and recurring payment type
func (s *Service) getActionStateForExecution(ctx context.Context, req *pb.GetRecurringPaymentActionStatusRequest,
	recurringPayment *pb.RecurringPayment, order *orderPb.Order) (pb.GetRecurringPaymentActionStatusResponse_ActionState, error) {
	clientReqId := req.GetClientRequestId()
	if clientReqId == "" {
		clientReqId = req.GetClientId().GetId()
	}
	switch {
	case order.GetWorkflowRefId() != "" && recurringPayment.GetType() == pb.RecurringPaymentType_STANDING_INSTRUCTION &&
		s.featureFlags.EnableRecurringPaymentExecutionWithoutAuthViaCelestial(),
		recurringPayment.GetType() == pb.RecurringPaymentType_UPI_MANDATES &&
			s.featureFlags.EnableRecurringPaymentExecutionWithAuthViaCelestial():
		return s.getActionStateUsingCelestial(ctx, &celestialPb.ClientReqId{
			Id:     clientReqId,
			Client: req.GetClientId().GetClient(),
		})
	default:
		switch order.GetStatus() {
		case orderPb.OrderStatus_CREATED, orderPb.OrderStatus_IN_PAYMENT, orderPb.OrderStatus_COLLECT_REGISTERED, orderPb.OrderStatus_MANUAL_INTERVENTION:
			return pb.GetRecurringPaymentActionStatusResponse_IN_PROGRESS, nil
		case orderPb.OrderStatus_PAID:
			return pb.GetRecurringPaymentActionStatusResponse_SUCCESS, nil
		case orderPb.OrderStatus_PAYMENT_FAILED:
			return pb.GetRecurringPaymentActionStatusResponse_FAILURE, nil
		default:
			return pb.GetRecurringPaymentActionStatusResponse_ACTION_STATE_UNSPECIFIED, fmt.Errorf("invalid order status %w", rpc.StatusAsError(rpc.StatusInternal()))
		}

	}
}

// getMccWarningMessage fetches the mcc specific warning message to be shown to the user
func (s *Service) getMccWarningMessage(ctx context.Context, recurringPayment *pb.RecurringPayment) (*commontypes.Text, error) {
	var (
		pi  *paymentinstrument.GetPiByIdResponse
		err error
	)

	// show mcc info to the user when recurring payment is created successfully
	if recurringPayment.GetState() != pb.RecurringPaymentState_ACTIVATED {
		return nil, nil
	}

	if pi, err = s.piClient.GetPiById(ctx, &paymentinstrument.GetPiByIdRequest{Id: recurringPayment.GetPiTo()}); err != nil {
		return nil, fmt.Errorf("failed to fetch pi for given piId: err = %w", err)
	}

	mcc := pi.GetPaymentInstrument().GetUpi().GetMerchantDetails().GetMcc()
	warningMessage, ok := s.config.MccWarningMessageMap[mcc]
	if !ok {
		// No specific warning message for this mcc
		return nil, nil
	}
	return &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: warningMessage.DisplayValue,
		},
		BgColor:   warningMessage.BgColour,
		FontColor: warningMessage.FontColour,
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: (commontypes.FontStyle)(commontypes.FontStyle_value[warningMessage.FontStyle]),
		},
	}, nil
}

// validateMandateCreation validates the mandate creation requests
// checks for end data and amount related validations
func (s *Service) validateMandateCreation(
	endTime time.Time,
	allowedFrequency pb.AllowedFrequency,
	mcc string,
	purpose string,
	recurringPaymentAmount *money.Money,
) *rpc.Status {
	status := rpc.StatusOk()
	currTime := time.Now().UTC()
	// if period between end day and current day is greater than 90 days for one time mandate
	if int(endTime.Sub(currTime).Hours()) > 24*s.config.UpiMandateValidationParams.MaximumAllowedDurationBetweenStartAndEndDate {
		if allowedFrequency == pb.AllowedFrequency_ONE_TIME {
			status = rpc.NewStatusWithoutDebug(uint32(pb.CreateRecurringPaymentResponse_INVALID_START_AND_END_DATE),
				"invalid start and end date for recurring payment")
			return status
		}
	}

	// for some mccs and purpose code there is limit on the mandate amount
	amt := rpServerConfig.GetMaxAmountForMccAndPurposeCode(mcc, purpose)
	if amt != nil && moneyPb.Compare(recurringPaymentAmount, amt) == 1 {
		status = rpc.NewStatusWithoutDebug(uint32(pb.CreateRecurringPaymentResponse_AMOUNT_LIMIT_EXCEEDED),
			"amount limit exceeded for mandate creation")
		return status
	}

	return status
}

// GetRecurringPaymentIdByVendorRequestId rpc is useful to fetch recurring payment by recurring payment type and vendor request id that was used for recurring payment creation.
func (s *Service) GetRecurringPaymentIdByVendorRequestId(ctx context.Context, req *pb.GetRecurringPaymentIdByVendorRequestIdRequest) (*pb.GetRecurringPaymentIdByVendorRequestIdResponse, error) {
	var recurringPaymentId string

	switch req.GetRecurringPaymentType() {
	case pb.RecurringPaymentType_STANDING_INSTRUCTION:
		// todo (team) : add standing instructions handling here
		return &pb.GetRecurringPaymentIdByVendorRequestIdResponse{Status: rpc.StatusUnimplemented()}, nil

	case pb.RecurringPaymentType_UPI_MANDATES:
		// todo (team) : add upi handling here
		return &pb.GetRecurringPaymentIdByVendorRequestIdResponse{Status: rpc.StatusUnimplemented()}, nil

	case pb.RecurringPaymentType_ENACH_MANDATES:
		recurringPaymentIdRes, err := s.enachClient.GetRecurringPaymentIdByVendorReqId(ctx, &enachPb.GetRecurringPaymentIdByVendorReqIdRequest{ActionType: enachEnumsPb.EnachActionType_ACTION_TYPE_CREATE, VendorReqId: req.GetVendorRequestId()})
		if rpcErr := epifigrpc.RPCError(recurringPaymentIdRes, err); rpcErr != nil {
			logger.Error(ctx, "enachClient.GetRecurringPaymentIdByVendorReqId rpc call failed", zap.String(logger.VENDOR_REQUEST, req.GetVendorRequestId()), zap.Error(err))
			return &pb.GetRecurringPaymentIdByVendorRequestIdResponse{Status: rpc.StatusInternalWithDebugMsg("enachClient.GetRecurringPaymentIdByVendorReqId rpc call failed")}, nil
		}
		recurringPaymentId = recurringPaymentIdRes.GetRecurringPaymentId()
	default:
		logger.Error(ctx, "unsupported recurring payment type", zap.String(logger.RECURRING_PAYMENT_TYPE, req.GetRecurringPaymentType().String()))
		return &pb.GetRecurringPaymentIdByVendorRequestIdResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("unsupported recurring payment type")}, nil
	}

	return &pb.GetRecurringPaymentIdByVendorRequestIdResponse{
		Status:             rpc.StatusOk(),
		RecurringPaymentId: recurringPaymentId,
	}, nil
}

func (s *Service) publishRecurringPaymentActionEvent(ctx context.Context, rpAction *pb.RecurringPaymentsAction, rp *pb.RecurringPayment) {
	switch rp.GetType() {
	case pb.RecurringPaymentType_UPI_MANDATES:
		goroutine.RunWithDefaultTimeout(epificontext.WithEventAttributesV2(ctx), func(gctx context.Context) {
			s.eventBroker.AddToBatch(gctx, rpEvents.NewUpiMandateAction(rp.GetFromActorId(), rpAction.GetInitiatedBy().String(), rpAction.GetAction().String(), rp.GetState().String(), rpAction.GetState().String(), rp.GetId(), moneyPb.ToDisplayStringWithoutSymbol(rp.GetAmount()), rp.GetRecurrenceRule().GetAllowedFrequency().String(), rp.GetInterval().GetStartTime().AsTime(), rp.GetInterval().GetEndTime().AsTime()))
		})
	default:
		// Publish other events as required for other recurring payment types
	}
}

// GetRecurringPaymentsActionByVendorRequestId fetches the RecurringPaymentsAction entity using the provided vendor request ID.
// Returns a record not found status if no action exists for the given ID, or an internal error for unexpected failures.
func (s *Service) GetRecurringPaymentsActionByVendorRequestId(ctx context.Context, req *pb.GetRecurringPaymentsActionByVendorRequestIdRequest) (*pb.GetRecurringPaymentsActionByVendorRequestIdResponse, error) {
	var (
		res = &pb.GetRecurringPaymentsActionByVendorRequestIdResponse{}
	)
	if req.GetVendorRequestId() == "" {
		res.Status = rpc.StatusInvalidArgumentWithDebugMsg("vendor request Id in request cannot be empty")
		return res, nil
	}
	recurringPaymentAction, err := s.recurringPaymentActionsDao.GetByVendorRequestId(ctx, req.GetVendorRequestId())
	switch {
	case err != nil && errors.Is(err, epifierrors.ErrRecordNotFound):
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case recurringPaymentAction != nil:
		res.Status = rpc.StatusOk()
		res.RecurringPaymentsAction = recurringPaymentAction
		return res, nil
	default:
		logger.Error(ctx, "error fetching recurring payment action by vendor request id",
			zap.String(logger.VENDOR_REQUEST, req.GetVendorRequestId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
	}
	return res, nil
}
