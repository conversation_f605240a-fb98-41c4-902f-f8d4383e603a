package recurringpayment

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/epifi/be-common/api/rpc"

	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/api/order/domain"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	pb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/recurringpayment/standinginstruction/mocks"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	mocks3 "github.com/epifi/gamma/api/upi/mandate/mocks"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
)

func TestService_ProcessRecurringPaymentCreation(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockSIClient := mocks.NewMockStandingInstructionServiceClient(ctr)
	mockRecurringPaymentActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockMandateClient := mocks3.NewMockMandateServiceClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)

	svc := NewService(mockRecurringPaymentDao, nil, mockSIClient, mockRecurringPaymentActionDao, nil, nil, nil, nil, nil, conf, nil, mockMandateClient, nil, mockPiClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	queuedRecurringPayment := &pb.RecurringPayment{
		Type:  pb.RecurringPaymentType_STANDING_INSTRUCTION,
		State: pb.RecurringPaymentState_CREATION_QUEUED,
	}
	activatedRecurringPayment := &pb.RecurringPayment{
		Type:  pb.RecurringPaymentType_STANDING_INSTRUCTION,
		State: pb.RecurringPaymentState_ACTIVATED,
	}
	initiatedRecurringPayment := &pb.RecurringPayment{
		Id:    "rp-1",
		Type:  pb.RecurringPaymentType_STANDING_INSTRUCTION,
		State: pb.RecurringPaymentState_CREATION_AUTHORISED,
	}
	initiatedRecurringPaymentMandate := &pb.RecurringPayment{
		Id:          "rp-1",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		State:       pb.RecurringPaymentState_CREATION_AUTHORISED,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
	}
	failedRecurringPayment := &pb.RecurringPayment{
		Type:  pb.RecurringPaymentType_STANDING_INSTRUCTION,
		State: pb.RecurringPaymentState_FAILED,
	}
	creationInfo := &pb.RecurringPaymentCreationInfo{
		RecurringPaymentId: "rp-1",
		ClientRequestId:    "client-req-1",
		ReqId:              "req-id-1",
	}
	marshalledCreationInfo, _ := protojson.Marshal(creationInfo)
	tests := []struct {
		name           string
		req            *domain.ProcessFulfilmentRequest
		setupMockCalls func()
		want           *domain.ProcessFulfilmentResponse
		wantErr        bool
	}{
		{
			name: "record not found for recurring payment id",
			req: &domain.ProcessFulfilmentRequest{
				RequestHeader: &domain.DomainRequestHeader{IsLastAttempt: false},
				Payload:       marshalledCreationInfo,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_PERMANENT_FAILURE}},
			wantErr: false,
		},
		{
			name: "error in fetching recurring payment",
			req: &domain.ProcessFulfilmentRequest{
				RequestHeader: &domain.DomainRequestHeader{IsLastAttempt: false},
				Payload:       marshalledCreationInfo,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(nil, fmt.Errorf("error"))
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_TRANSIENT_FAILURE}},
			wantErr: false,
		},
		{
			name: "recurring payment created",
			req: &domain.ProcessFulfilmentRequest{
				RequestHeader: &domain.DomainRequestHeader{
					IsLastAttempt:   false,
					ClientRequestId: "order-1",
				},
				Payload: marshalledCreationInfo,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(activatedRecurringPayment, nil)
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_SUCCESS}},
			wantErr: false,
		},
		{
			name: "recurring payment queued",
			req: &domain.ProcessFulfilmentRequest{
				RequestHeader: &domain.DomainRequestHeader{IsLastAttempt: false},
				Payload:       marshalledCreationInfo,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(queuedRecurringPayment, nil)
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_NO_OP}},
			wantErr: false,
		},
		{
			name: "recurring payment failed",
			req: &domain.ProcessFulfilmentRequest{
				RequestHeader: &domain.DomainRequestHeader{
					IsLastAttempt:   false,
					ClientRequestId: "order-1",
				},
				Payload: marshalledCreationInfo,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(failedRecurringPayment, nil)
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_PERMANENT_FAILURE}},
			wantErr: false,
		},
		{
			name: "recurring payment initiated",
			req: &domain.ProcessFulfilmentRequest{
				RequestHeader: &domain.DomainRequestHeader{IsLastAttempt: false},
				Payload:       marshalledCreationInfo,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(initiatedRecurringPayment, nil)
				mockSIClient.EXPECT().GetActionStatus(gomock.Any(), &siPb.GetActionStatusRequest{
					RecurringPaymentId: "rp-1",
					RequestId:          "req-id-1",
					Action:             siPb.RequestType_CREATE,
				}).Return(&siPb.GetActionStatusResponse{
					Status: rpc.StatusOk(),
					State:  siPb.State_SUCCESS,
				}, nil)
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-id-1",
					State:              pb.ActionState_ACTION_CREATED,
				}, nil)
				mockRecurringPaymentActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-id-1",
					State:              pb.ActionState_ACTION_CREATED,
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), initiatedRecurringPayment, nil,
					pb.RecurringPaymentState_CREATION_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_SUCCESS}},
			wantErr: false,
		},
		{
			name: "recurring payment initiated mandate",
			req: &domain.ProcessFulfilmentRequest{
				RequestHeader: &domain.DomainRequestHeader{IsLastAttempt: false},
				Payload:       marshalledCreationInfo,
			},
			setupMockCalls: func() {
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(initiatedRecurringPaymentMandate, nil)
				mockMandateClient.EXPECT().FetchAndUpdateRequestStatus(gomock.Any(), &upiMandatePb.FetchAndUpdateRequestStatusRequest{
					ReqId:           "req-id-1",
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					EpifiCustomerPi: "pi-1",
				}).Return(&upiMandatePb.FetchAndUpdateRequestStatusResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockRecurringPaymentActionDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-id-1",
					State:              pb.ActionState_ACTION_CREATED,
				}, nil)
				mockRecurringPaymentActionDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    "client-req-id-1",
					State:              pb.ActionState_ACTION_CREATED,
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
				mockRecurringPaymentDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), initiatedRecurringPaymentMandate, nil,
					pb.RecurringPaymentState_CREATION_AUTHORISED, pb.RecurringPaymentState_ACTIVATED).Return(nil)
				mockPiClient.EXPECT().GetPIsByIds(gomock.Any(), &piPb.GetPIsByIdsRequest{Ids: []string{
					"pi-1",
					"pi-2",
				}}).Return(&piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Id:                   "pi-1",
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
						},
					},
				}, nil)
			},
			want:    &domain.ProcessFulfilmentResponse{ResponseHeader: &domain.DomainResponseHeader{Status: domain.DomainProcessingStatus_SUCCESS}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.ProcessRecurringPaymentCreation(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessRecurringPaymentCreation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessRecurringPaymentCreation() got = %v, want %v", got, tt.want)
			}
		})
	}
}
