package recurringpayment

import (
	"context"
	"errors"
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpPbPayload "github.com/epifi/gamma/api/recurringpayment/payload"
	"github.com/epifi/gamma/pkg/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/internal/domainexecutionprocessor"
	"github.com/epifi/gamma/recurringpayment/internal/validationrules"
)

// ExecuteRecurringPaymentV1 rpc is used to execute the recurring payment corresponding to the given recurring payment id
// steps followed for exeuction:
//  1. Check if action already exists for given client req id (idempotency check)
//  2. Validate the execution request
//     a) recurring payment should be in CREATED state
//     b) amount checks based upon the amount type of the recurring payment (maximum or exact)
//     c) number of execution should be less than the maximum allowed executions
//  3. Initate recurring payment execution workflow
//
// nolint: funlen
func (s *Service) ExecuteRecurringPaymentV1(ctx context.Context, req *rpPb.ExecuteRecurringPaymentV1Request) (*rpPb.ExecuteRecurringPaymentV1Response, error) {
	fetchedAction, fetchedActionErr := s.recurringPaymentActionsDao.GetByClientRequestId(ctx, req.GetClientRequestId(), true)
	switch {
	case fetchedActionErr != nil && !errors.Is(fetchedActionErr, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "error while fetching recurring payment action", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(fetchedActionErr))
		return &rpPb.ExecuteRecurringPaymentV1Response{
			Status: rpc.StatusInternalWithDebugMsg(fetchedActionErr.Error()),
		}, nil
	case fetchedAction != nil:
		logger.Info(ctx, "recurring payment action already exists for given client request id", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
		return &rpPb.ExecuteRecurringPaymentV1Response{
			Status: rpc.StatusOk(),
		}, nil
	}

	fetchedRecurringPayment, fetchedRecurringPaymentErr := s.recurringPaymentDao.GetById(ctx, req.GetRecurringPaymentId())
	if fetchedRecurringPaymentErr != nil {
		logger.Error(ctx, "error while fetching recurring payment by id", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(fetchedRecurringPaymentErr))
		return &rpPb.ExecuteRecurringPaymentV1Response{
			Status: rpc.StatusInternalWithDebugMsg(fetchedRecurringPaymentErr.Error()),
		}, nil
	}
	ctx = epificontext.WithOwnership(ctx, fetchedRecurringPayment.GetEntityOwnership())
	updatedRecurringPayment, updatedRecurringPaymentErr := s.checkAndUpdateRecurringPaymentState(ctx, fetchedRecurringPayment)
	if updatedRecurringPaymentErr != nil {
		logger.Error(ctx, "failed to update the recurring payment state", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(updatedRecurringPaymentErr))
		return &rpPb.ExecuteRecurringPaymentV1Response{
			Status: rpc.StatusInternalWithDebugMsg(updatedRecurringPaymentErr.Error()),
		}, nil
	}
	// take the distributed lock to avoid multiple executions
	// we only allow execution if there is no execution in non-terminal state for the given recurring payment
	// lock is taken on recurring payment id because if multiple executions are trigerred with same/different client request id, only 1 should be treated and other should fail
	// if lock is not taken, there can be race condition since the action is created post validation and multiple executions can happen which is not desired
	lockRelease, lockErr := s.acquireRecurringPaymentExecutionLock(ctx, req.GetRecurringPaymentId())
	if lockErr != nil {
		logger.Error(ctx, "failed to aquire lock on client request id", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(lockErr))
		return &rpPb.ExecuteRecurringPaymentV1Response{
			Status: rpc.StatusInternalWithDebugMsg("failed to aquire the lock"),
		}, nil
	}
	defer lockRelease()
	validationErr := s.executionValidationChain.Validate(ctx, &validationrules.ValidateReq{
		Recurringpayment: updatedRecurringPayment,
		Amount:           req.GetAmount(),
	})
	if validationErr != nil {
		logger.Error(ctx, "execution validation failed", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Any(logger.REQUEST, req), zap.Error(validationErr))
		rpcStatus, ok := validationrules.ErrorToExecutionStatusMap[validationErr]
		if !ok {
			rpcStatus = rpc.StatusFailedPreconditionWithDebugMsg(validationErr.Error())
		}
		return &rpPb.ExecuteRecurringPaymentV1Response{
			Status: rpcStatus,
		}, nil
	}
	action, createActionErr := s.createRecurringPaymentAction(ctx, req.GetRecurringPaymentId(), req.GetClientRequestId(), "", rpPb.Action_EXECUTE, &rpPb.ActionMetadata{ExecuteActionMetadate: &rpPb.ExecuteActionMetaData{Amount: req.GetAmount()}}, rpPb.ActionState_ACTION_CREATED, "", nil, nil, nil)
	if createActionErr != nil {
		logger.Error(ctx, "error while creating recurring payment action", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(createActionErr))
		return &rpPb.ExecuteRecurringPaymentV1Response{
			Status: rpc.StatusInternalWithDebugMsg("error while creating recurring payment execution action"),
		}, nil
	}
	isAuthRequired, isAuthRequiredErr := s.isAuthRequiredForExecution(ctx, updatedRecurringPayment, req.GetRecurringPaymentTypeSpecificPayload())
	if isAuthRequiredErr != nil {
		logger.Error(ctx, "error while checking if auth is required for execution", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(isAuthRequiredErr))
		return &rpPb.ExecuteRecurringPaymentV1Response{
			Status: rpc.StatusInternalWithDebugMsg("error while checking if auth is required"),
		}, nil
	}
	initiateWorkflowErr := s.initiateExecutionV1Workflow(ctx, req.GetRecurringPaymentId(), action.GetId(), req.GetRecurringPaymentTypeSpecificPayload(), isAuthRequired, fetchedRecurringPayment.GetEntityOwnership())
	if initiateWorkflowErr != nil {
		logger.Error(ctx, "error while initiating workflow", zap.String(logger.RECURRING_PAYMENT_ID, req.GetRecurringPaymentId()), zap.Error(initiateWorkflowErr))
		return &rpPb.ExecuteRecurringPaymentV1Response{
			Status: rpc.StatusInternalWithDebugMsg("failed to initialise the workflow"),
		}, nil
	}
	return &rpPb.ExecuteRecurringPaymentV1Response{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) initiateExecutionV1Workflow(ctx context.Context, recurringPaymentId, recurringPaymentActionId string, payload *rpPbPayload.RecurringPaymentTypeSpecificExecutionPayload, isAuthRequired bool, entityOwnership commontypes.Ownership) error {
	if !isAuthRequired {
		workflowPayload, marshalErr := protojson.Marshal(&rpPbPayload.ExecuteRecurringPaymentWithoutAuthV1WorkflowPayload{
			RecurringPaymentId:                  recurringPaymentId,
			RecurringPaymentActionId:            recurringPaymentActionId,
			RecurringPaymentTypeSpecificPayload: payload,
		})
		if marshalErr != nil {
			return fmt.Errorf("error while marshalling recurring payment without auth execution workflow payload,err: %w", marshalErr)
		}
		initiateWorkflowRes, initiateWorfkflowErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
			Params: &celestialPb.WorkflowCreationRequestParams{
				ClientReqId: &workflowPb.ClientReqId{
					Id:     recurringPaymentActionId,
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
				Payload:          workflowPayload,
				Ownership:        entityOwnership,
				QualityOfService: celestialPb.QoS_GUARANTEED,
				Version:          workflowPb.Version_V0,
				Type:             celestial.GetTypeEnumFromWorkflowType(rpNs.ExecuteRecurringPaymentWithoutAuthV1),
				UseCase:          recurringpayment.OwnershipToUseCaseForRecPay[entityOwnership],
			},
		})
		if err := epifigrpc.RPCError(initiateWorkflowRes, initiateWorfkflowErr); err != nil {
			return fmt.Errorf("error while initiating recurring payment without auth execution v1 workflow, err: %w", err)
		}
		return nil
	} else {
		return fmt.Errorf("workflow with not auth is not implemented yet")
	}
}

func (s *Service) isAuthRequiredForExecution(ctx context.Context, recurringPayment *rpPb.RecurringPayment, payload *rpPbPayload.RecurringPaymentTypeSpecificExecutionPayload) (bool, error) {
	processor, processorErr := s.domainExecutionProcessorFactory.GetProcessor(recurringPayment.GetType(), recurringPayment.GetPaymentRoute())
	if processorErr != nil {
		return false, fmt.Errorf("failed to fetch the domain processor, err: %w", processorErr)
	}
	isRequired, isRequiredErr := processor.IsAuthRequired(ctx, &domainexecutionprocessor.IsAuthRequiredReq{
		RecurringPaymentId: recurringPayment.GetId(),
		Payload:            payload,
	})
	if isRequiredErr != nil {
		return false, isRequiredErr
	}
	return isRequired, nil
}

// created new method for aquiring the lock since the other method is being called in a consumer method and taking lock on client request id
// so to avoid the confusion, new method is created for aquiring lock on recurring payment id
func (s *Service) acquireRecurringPaymentExecutionLock(ctx context.Context, id string) (func(), error) {
	lk, err := s.lockManager.Lock(ctx, s.getProcessRecurringPaymentExecutionLock(id), s.config.RecurringPaymentExecutionParams.ProcessRecurringPaymentExecutionLockLeaseDuration)
	if err != nil {
		return nil, fmt.Errorf("failed to acquire write lock while process recurring payment execution: %w", err)
	}

	return func() {
		relErr := lk.Release(ctx)
		if relErr != nil {
			logger.Error(ctx, "failed to release read lock process recurring payment execution", zap.Error(relErr))
		}
	}, nil
}
