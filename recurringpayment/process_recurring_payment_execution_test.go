package recurringpayment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	lockMocks "github.com/epifi/be-common/pkg/lock/mocks"
	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"

	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"

	types "github.com/epifi/gamma/api/typesv2"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	upiMocks "github.com/epifi/gamma/api/upi/mocks"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/money"

	orderPb "github.com/epifi/gamma/api/order"
	domainPb "github.com/epifi/gamma/api/order/domain"
	mocks2 "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	mocks4 "github.com/epifi/gamma/api/order/payment/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	pb "github.com/epifi/gamma/api/recurringpayment"
	siPb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	"github.com/epifi/gamma/api/recurringpayment/standinginstruction/mocks"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	upiPb "github.com/epifi/gamma/api/upi"
	mocks3 "github.com/epifi/gamma/api/upi/mandate/mocks"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
)

func TestService_ProcessRecurringPaymentExecution(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockOrderClient := mocks2.NewMockOrderServiceClient(ctr)
	mockPaymentClient := mocks4.NewMockPaymentClient(ctr)
	mockRecurringPaymentActionsDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockSIClient := mocks.NewMockStandingInstructionServiceClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockUpiClient := upiMocks.NewMockUPIClient(ctr)
	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockMandateClient := mocks3.NewMockMandateServiceClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockILockManager := lockMocks.NewMockRwLockManager(ctr)
	mockILock := lockMocks.NewMockILock(ctr)
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)

	svc := NewService(mockRecurringPaymentDao, mockOrderClient, mockSIClient, mockRecurringPaymentActionsDao, mockSavingsClient, mockActorClient, nil, nil, mockPaymentClient, conf, nil, mockMandateClient, nil, mockPiClient, nil, mockUpiClient, nil, dynamicConf.SIExecutionParams(), nil, nil, mockILockManager, mockInPaymentPublisher, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	amount := money.AmountINR(100).GetPb()
	payload := &pb.RecurringPaymentExecutionInfo{
		RecurringPaymentId: "rp-1",
		Amount:             amount,
	}
	marshalledPayload, _ := protojson.Marshal(payload)

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))

	recurringPayment := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(5000).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
	}
	recurringPaymentMandate := &pb.RecurringPayment{
		Id:          "rp-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_UPI_MANDATES,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.AmountINR(5000).Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &types.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_UPI,
		State:                    pb.RecurringPaymentState_ACTIVATED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
	}

	tests := []struct {
		name           string
		req            *domainPb.ProcessPaymentRequest
		setupMockCalls func()
		want           *domainPb.ProcessPaymentResponse
		wantErr        bool
	}{
		{
			name: "initiated payment execution successfully",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockILockManager.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any())
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type: types.Actor_USER,
					},
				}, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).
					Return(&orderPb.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &orderPb.OrderWithTransactions{
							Order: &orderPb.Order{
								Id: "id-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:              "txn-id-1",
									PiFrom:          "pi-1",
									PiTo:            "pi-2",
									PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
									Amount:          amount,
									Status:          paymentPb.TransactionStatus_CREATED,
									PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
								},
							},
						},
					}, nil)
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: "txn-id-1"},
					GetReqInfo: true,
				}).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id-1",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						Amount:          amount,
						Status:          paymentPb.TransactionStatus_CREATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "request-id-1",
					},
				}, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), &paymentPb.UpdateTransactionRequest{
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id-1",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						Amount:          amount,
						Status:          paymentPb.TransactionStatus_INITIATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					},
					FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS},
				}).Return(&paymentPb.UpdateTransactionResponse{Status: rpc.StatusOk()}, nil)
				mockSIClient.EXPECT().Execute(gomock.Any(), &siPb.ExecuteRequest{
					RecurringPaymentId: "rp-1",
					Amount:             amount,
					Protocol:           recurringPayment.GetPreferredPaymentProtocol(),
					RequestId:          "request-id-1",
				}).Return(&siPb.ExecuteResponse{
					Status:               rpc.StatusOk(),
					VendorResponseCode:   "response-code-1",
					VendorResponseReason: "response-reason-1",
					StatusCode:           "status-code-1",
				}, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), gomock.Any()).Return(&paymentPb.UpdateTransactionResponse{Status: rpc.StatusOk()}, nil)
			},

			want:    &domainPb.ProcessPaymentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE}},
			wantErr: false,
		},
		{
			name: "payment execution moved to created due to deadline exceeded",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockILockManager.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any())
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type: types.Actor_USER,
					},
				}, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).
					Return(&orderPb.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &orderPb.OrderWithTransactions{
							Order: &orderPb.Order{
								Id:          "id-1",
								ClientReqId: "client-req-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:              "txn-id-1",
									PiFrom:          "pi-1",
									PiTo:            "pi-2",
									PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
									Amount:          amount,
									Status:          paymentPb.TransactionStatus_CREATED,
									PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
								},
							},
						},
					}, nil)
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: "txn-id-1"},
					GetReqInfo: true,
				}).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id-1",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						Amount:          amount,
						Status:          paymentPb.TransactionStatus_CREATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "request-id-1",
					},
				}, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), &paymentPb.UpdateTransactionRequest{
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id-1",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						Amount:          amount,
						Status:          paymentPb.TransactionStatus_INITIATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					},
					FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS},
				}).Return(&paymentPb.UpdateTransactionResponse{Status: rpc.StatusOk()}, nil)
				mockSIClient.EXPECT().Execute(gomock.Any(), &siPb.ExecuteRequest{
					RecurringPaymentId: "rp-1",
					Amount:             amount,
					Protocol:           recurringPayment.GetPreferredPaymentProtocol(),
					RequestId:          "request-id-1",
				}).Return(&siPb.ExecuteResponse{
					Status: rpc.StatusDeadlineExceeded(),
				}, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), gomock.Any()).Return(&paymentPb.UpdateTransactionResponse{Status: rpc.StatusOk()}, nil)
			},
			want:    &domainPb.ProcessPaymentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_IN_PROGRESS}},
			wantErr: false,
		},
		{
			name: "payment execution failed due to transaction limit execeeded",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockILockManager.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any())
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type: types.Actor_USER,
					},
				}, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).
					Return(&orderPb.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &orderPb.OrderWithTransactions{
							Order: &orderPb.Order{
								Id:          "id-1",
								ClientReqId: "client-req-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:              "txn-id-1",
									PiFrom:          "pi-1",
									PiTo:            "pi-2",
									PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
									Amount:          amount,
									Status:          paymentPb.TransactionStatus_CREATED,
									PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
								},
							},
						},
					}, nil)
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: "txn-id-1"},
					GetReqInfo: true,
				}).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id-1",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						Amount:          amount,
						Status:          paymentPb.TransactionStatus_CREATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "request-id-1",
					},
				}, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), &paymentPb.UpdateTransactionRequest{
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id-1",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						Amount:          amount,
						Status:          paymentPb.TransactionStatus_INITIATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					},
					FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS},
				}).Return(&paymentPb.UpdateTransactionResponse{Status: rpc.StatusOk()}, nil)
				mockSIClient.EXPECT().Execute(gomock.Any(), &siPb.ExecuteRequest{
					RecurringPaymentId: "rp-1",
					Amount:             amount,
					Protocol:           recurringPayment.GetPreferredPaymentProtocol(),
					RequestId:          "request-id-1",
				}).Return(&siPb.ExecuteResponse{
					Status:               rpc.NewStatusWithoutDebug(uint32(siPb.ExecuteResponse_TRANSACTIONS_LIMIT_EXCEEDED), ""),
					VendorResponseCode:   "response-code-1",
					VendorResponseReason: "response-reason-1",
					StatusCode:           "status-code-1",
				}, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), gomock.Any()).Return(&paymentPb.UpdateTransactionResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					State: pb.ActionState_ACTION_CREATED,
				}, nil)
				mockRecurringPaymentActionsDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					State: pb.ActionState_ACTION_CREATED,
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_FAILURE).Return(nil)
			},
			want:    &domainPb.ProcessPaymentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE}},
			wantErr: false,
		},
		{
			name: "fetched payment status successfully",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockILockManager.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any())
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type: types.Actor_USER,
					},
				}, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).
					Return(&orderPb.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &orderPb.OrderWithTransactions{
							Order: &orderPb.Order{
								Id:          "id-1",
								ClientReqId: "client-req-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:              "txn-id-1",
									PiFrom:          "pi-1",
									PiTo:            "pi-2",
									PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
									Amount:          amount,
									Status:          paymentPb.TransactionStatus_INITIATED,
									PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
								},
							},
						},
					}, nil)
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: "txn-id-1"},
					GetReqInfo: true,
				}).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id-1",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						Amount:          amount,
						Status:          paymentPb.TransactionStatus_CREATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "request-id-1",
					},
				}, nil)
				mockSIClient.EXPECT().GetExecutionStatus(gomock.Any(), &siPb.GetExecutionStatusRequest{
					OriginalRequestId: "request-id-1",
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					ActorId:           recurringPayment.GetFromActorId(),
					PaymentProtocol:   paymentPb.PaymentProtocol_INTRA_BANK,
				}).Return(&siPb.GetExecutionStatusResponse{
					Status: rpc.StatusOk(),
					Utr:    "utr-1",
				}, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), gomock.Any()).Return(&paymentPb.UpdateTransactionResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					State: pb.ActionState_ACTION_CREATED,
				}, nil)
				mockRecurringPaymentActionsDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					State: pb.ActionState_ACTION_CREATED,
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
			},
			want:    &domainPb.ProcessPaymentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_SUCCESS}},
			wantErr: false,
		},
		{
			name: "payment status in progress",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockILockManager.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any())
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type: types.Actor_USER,
					},
				}, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).
					Return(&orderPb.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &orderPb.OrderWithTransactions{
							Order: &orderPb.Order{
								Id:          "id-1",
								ClientReqId: "client-req-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:              "txn-id-1",
									PiFrom:          "pi-1",
									PiTo:            "pi-2",
									PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
									Amount:          amount,
									Status:          paymentPb.TransactionStatus_INITIATED,
									PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
								},
							},
						},
					}, nil)
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: "txn-id-1"},
					GetReqInfo: true,
				}).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id-1",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						Amount:          amount,
						Status:          paymentPb.TransactionStatus_CREATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "request-id-1",
					},
				}, nil)
				mockSIClient.EXPECT().GetExecutionStatus(gomock.Any(), &siPb.GetExecutionStatusRequest{
					OriginalRequestId: "request-id-1",
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					ActorId:           recurringPayment.GetFromActorId(),
					PaymentProtocol:   paymentPb.PaymentProtocol_INTRA_BANK,
				}).Return(&siPb.GetExecutionStatusResponse{
					Status: rpc.ExtendedStatusInProgress(),
					Utr:    "utr-1",
				}, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), gomock.Any()).Return(&paymentPb.UpdateTransactionResponse{Status: rpc.StatusOk()}, nil)
			},
			want:    &domainPb.ProcessPaymentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_IN_PROGRESS}},
			wantErr: false,
		},
		{
			name: "not found in payment status",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockILockManager.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any())
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type: types.Actor_USER,
					},
				}, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).
					Return(&orderPb.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &orderPb.OrderWithTransactions{
							Order: &orderPb.Order{
								Id:          "id-1",
								ClientReqId: "client-req-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:              "txn-id-1",
									PiFrom:          "pi-1",
									PiTo:            "pi-2",
									PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
									Amount:          amount,
									Status:          paymentPb.TransactionStatus_INITIATED,
									PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
								},
							},
						},
					}, nil)
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: "txn-id-1"},
					GetReqInfo: true,
				}).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id-1",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						Amount:          amount,
						Status:          paymentPb.TransactionStatus_CREATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
						CreatedAt:       timestampPb.Now(),
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "request-id-1",
					},
				}, nil)
				mockSIClient.EXPECT().GetExecutionStatus(gomock.Any(), &siPb.GetExecutionStatusRequest{
					OriginalRequestId: "request-id-1",
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					ActorId:           recurringPayment.GetFromActorId(),
					PaymentProtocol:   paymentPb.PaymentProtocol_INTRA_BANK,
				}).Return(&siPb.GetExecutionStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), gomock.Any()).Return(&paymentPb.UpdateTransactionResponse{Status: rpc.StatusOk()}, nil)
			},
			want:    &domainPb.ProcessPaymentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_IN_PROGRESS}},
			wantErr: false,
		},
		{
			name: "transaction in MANUAL_INTERVENTION state and force retry is false",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockILockManager.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any())
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type: types.Actor_USER,
					},
				}, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).
					Return(&orderPb.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &orderPb.OrderWithTransactions{
							Order: &orderPb.Order{
								Id:          "id-1",
								ClientReqId: "client-req-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:              "txn-id-1",
									PiFrom:          "pi-1",
									PiTo:            "pi-2",
									PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
									Amount:          amount,
									Status:          paymentPb.TransactionStatus_MANUAL_INTERVENTION,
									PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
								},
							},
						},
					}, nil)
			},
			want:    &domainPb.ProcessPaymentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_IN_PROGRESS}},
			wantErr: false,
		},
		{
			name: "transaction in MANUAL_INTERVENTION state and force retry is true",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId:         "order-id-1",
					IsLastAttempt:           false,
					ShouldForceProcessOrder: true,
				},
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockILockManager.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any())
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type: types.Actor_USER,
					},
				}, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).
					Return(&orderPb.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &orderPb.OrderWithTransactions{
							Order: &orderPb.Order{
								Id:          "id-1",
								ClientReqId: "client-req-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:              "txn-id-1",
									PiFrom:          "pi-1",
									PiTo:            "pi-2",
									PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
									Amount:          amount,
									Status:          paymentPb.TransactionStatus_MANUAL_INTERVENTION,
									PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
								},
							},
						},
					}, nil)
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: "txn-id-1"},
					GetReqInfo: true,
				}).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id-1",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						Amount:          amount,
						Status:          paymentPb.TransactionStatus_MANUAL_INTERVENTION,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "request-id-1",
					},
				}, nil)
				mockSIClient.EXPECT().GetExecutionStatus(gomock.Any(), &siPb.GetExecutionStatusRequest{
					OriginalRequestId: "request-id-1",
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					ActorId:           recurringPayment.GetFromActorId(),
					PaymentProtocol:   paymentPb.PaymentProtocol_INTRA_BANK,
				}).Return(&siPb.GetExecutionStatusResponse{
					Status: rpc.StatusOk(),
					Utr:    "utr-1",
				}, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), gomock.Any()).Return(&paymentPb.UpdateTransactionResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					State: pb.ActionState_ACTION_CREATED,
				}, nil)
				mockRecurringPaymentActionsDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					State: pb.ActionState_ACTION_CREATED,
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
			},
			want:    &domainPb.ProcessPaymentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_SUCCESS}},
			wantErr: false,
		},
		{
			name: "fetched mandate status successfully",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockILockManager.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any())
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPaymentMandate, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).
					Return(&orderPb.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &orderPb.OrderWithTransactions{
							Order: &orderPb.Order{
								Id:          "id-1",
								ClientReqId: "client-req-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:              "txn-id-1",
									PiFrom:          "pi-1",
									PiTo:            "pi-2",
									PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
									Amount:          amount,
									Status:          paymentPb.TransactionStatus_INITIATED,
									PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
								},
							},
						},
					}, nil)
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: "txn-id-1"},
					GetReqInfo: true,
				}).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id-1",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						Amount:          amount,
						Status:          paymentPb.TransactionStatus_CREATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "request-id-1",
					},
				}, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type: types.Actor_USER,
					},
				}, nil)
				mockUpiClient.EXPECT().CheckTxnStatus(gomock.Any(), &upiPb.CheckTxnStatusRequest{
					TxnId: "txn-id-1",
				}).Return(&upiPb.CheckTxnStatusResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), gomock.Any()).Return(&paymentPb.UpdateTransactionResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					State: pb.ActionState_ACTION_CREATED,
				}, nil)
				mockRecurringPaymentActionsDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					State: pb.ActionState_ACTION_CREATED,
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
			},
			want:    &domainPb.ProcessPaymentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_SUCCESS}},
			wantErr: false,
		},
		{
			name: "Should not fetch mandate status from vendor if time elapsed is less than 90 sec from txn creation",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockILockManager.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any())
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPaymentMandate, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).
					Return(&orderPb.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &orderPb.OrderWithTransactions{
							Order: &orderPb.Order{
								Id:          "id-1",
								ClientReqId: "client-req-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:              "txn-id-1",
									PiFrom:          "pi-1",
									PiTo:            "pi-2",
									PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
									Amount:          amount,
									Status:          paymentPb.TransactionStatus_INITIATED,
									PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
									CreatedAt:       timestampPb.New(time.Now().Add(-1 * 1 * time.Second)),
								},
							},
						},
					}, nil)
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: "txn-id-1"},
					GetReqInfo: true,
				}).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id-1",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						Amount:          amount,
						Status:          paymentPb.TransactionStatus_CREATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
						CreatedAt:       timestampPb.New(time.Now().Add(-1 * 1 * time.Second)),
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "request-id-1",
					},
				}, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type: types.Actor_USER,
					},
				}, nil)
			},
			want:    &domainPb.ProcessPaymentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_IN_PROGRESS}},
			wantErr: false,
		},
		{
			name: "Should  fetch mandate status from vendor if time elapsed is more than 90 sec from txn creation",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockILockManager.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any())
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPaymentMandate, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).
					Return(&orderPb.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &orderPb.OrderWithTransactions{
							Order: &orderPb.Order{
								Id:          "id-1",
								ClientReqId: "client-req-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:              "txn-id-1",
									PiFrom:          "pi-1",
									PiTo:            "pi-2",
									PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
									Amount:          amount,
									Status:          paymentPb.TransactionStatus_INITIATED,
									PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
									CreatedAt:       timestampPb.New(time.Now().Add(-1 * 95 * time.Second)),
								},
							},
						},
					}, nil)
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: "txn-id-1"},
					GetReqInfo: true,
				}).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id-1",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						Amount:          amount,
						Status:          paymentPb.TransactionStatus_CREATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
						CreatedAt:       timestampPb.New(time.Now().Add(-1 * 95 * time.Second)),
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "request-id-1",
					},
				}, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type: types.Actor_USER,
					},
				}, nil)
				mockUpiClient.EXPECT().CheckTxnStatus(gomock.Any(), &upiPb.CheckTxnStatusRequest{
					TxnId: "txn-id-1",
				}).Return(&upiPb.CheckTxnStatusResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), gomock.Any()).Return(&paymentPb.UpdateTransactionResponse{Status: rpc.StatusOk()}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).Return(&pb.RecurringPaymentsAction{
					State: pb.ActionState_ACTION_CREATED,
				}, nil)
				mockRecurringPaymentActionsDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					State: pb.ActionState_ACTION_CREATED,
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_SUCCESS).Return(nil)
			},
			want:    &domainPb.ProcessPaymentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_SUCCESS}},
			wantErr: false,
		},
		{
			name: "error while updating txn",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockILockManager.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any())
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type: types.Actor_USER,
					},
				}, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).
					Return(&orderPb.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &orderPb.OrderWithTransactions{
							Order: &orderPb.Order{
								Id:          "id-1",
								ClientReqId: "client-req-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:              "txn-id-1",
									PiFrom:          "pi-1",
									PiTo:            "pi-2",
									PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
									Amount:          amount,
									Status:          paymentPb.TransactionStatus_INITIATED,
									PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
								},
							},
						},
					}, nil)
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: "txn-id-1"},
					GetReqInfo: true,
				}).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id-1",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						Amount:          amount,
						Status:          paymentPb.TransactionStatus_CREATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "request-id-1",
					},
				}, nil)
				mockSIClient.EXPECT().GetExecutionStatus(gomock.Any(), &siPb.GetExecutionStatusRequest{
					OriginalRequestId: "request-id-1",
					PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					ActorId:           recurringPayment.GetFromActorId(),
					PaymentProtocol:   paymentPb.PaymentProtocol_INTRA_BANK,
				}).Return(&siPb.GetExecutionStatusResponse{
					Status: rpc.StatusOk(),
					Utr:    "utr-1",
				}, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), gomock.Any()).Return(&paymentPb.UpdateTransactionResponse{Status: rpc.StatusInternal()}, nil)
			},
			want:    &domainPb.ProcessPaymentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_IN_PROGRESS}},
			wantErr: false,
		},
		{
			name: "neft payment failed due to neft unavailability and getting retried",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockILockManager.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockILock, nil)
				mockILock.EXPECT().Release(gomock.Any())
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "rp-1").Return(recurringPayment, nil)
				mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type: types.Actor_USER,
					},
				}, nil)
				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id-1"}).
					Return(&orderPb.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &orderPb.OrderWithTransactions{
							Order: &orderPb.Order{
								Id:          "id-1",
								ClientReqId: "client-req-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:              "txn-id-1",
									PiFrom:          "pi-1",
									PiTo:            "pi-2",
									PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
									Amount:          amount,
									Status:          paymentPb.TransactionStatus_FAILED,
									PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
									DetailedStatus: &paymentPb.TransactionDetailedStatus{
										DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
											{
												RawStatusCode: "F025",
											},
										},
									},
								},
							},
						},
					}, nil)
				mockPaymentClient.EXPECT().CreateTransaction(gomock.Any(), newCreateTransactionReqMatcher(&paymentPb.CreateTransactionRequest{
					PiFrom:          "pi-1",
					PiTo:            "pi-2",
					Amount:          amount,
					OrderId:         "id-1",
					PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
					Status:          paymentPb.TransactionStatus_CREATED,
					ReqInfo:         &paymentPb.PaymentRequestInformation{},
					Ownership:       commontypes.Ownership_FEDERAL_BANK,
				})).Return(&paymentPb.CreateTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						Amount:          amount,
						OrderId:         "id-1",
						PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
						Status:          paymentPb.TransactionStatus_CREATED,
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "",
					},
				}, nil)
				mockPaymentClient.EXPECT().GetTransaction(gomock.Any(), &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: "txn-id"},
					GetReqInfo: true,
				}).Return(&paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						Amount:          amount,
						OrderId:         "id-1",
						PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
						Status:          paymentPb.TransactionStatus_CREATED,
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "request-id",
					},
				}, nil)
				mockRecurringPaymentActionsDao.EXPECT().GetByClientRequestId(gomock.Any(), "client-req-1", false).
					Return(&pb.RecurringPaymentsAction{
						Id:                 "rp-action-1",
						RecurringPaymentId: "rp-id-1",
						ClientRequestId:    "client-req-1",
						Action:             pb.Action_CREATE,
						State:              pb.ActionState_ACTION_CREATED,
						VendorRequestId:    "vendor-req-1",
					}, nil)
				mockRecurringPaymentActionsDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), &pb.RecurringPaymentsAction{
					Id:                 "rp-action-1",
					RecurringPaymentId: "rp-id-1",
					ClientRequestId:    "client-req-1",
					Action:             pb.Action_CREATE,
					State:              pb.ActionState_ACTION_CREATED,
					VendorRequestId:    "vendor-req-1",
				}, nil, pb.ActionState_ACTION_CREATED, pb.ActionState_ACTION_FAILURE).
					Return(nil)
				mockRecurringPaymentActionsDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), &paymentPb.UpdateTransactionRequest{
					Transaction: &paymentPb.Transaction{
						Id:              "txn-id",
						PiFrom:          "pi-1",
						PiTo:            "pi-2",
						Amount:          amount,
						OrderId:         "id-1",
						PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
						Status:          paymentPb.TransactionStatus_INITIATED,
					},
					FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS},
				}).Return(&paymentPb.UpdateTransactionResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockSIClient.EXPECT().Execute(gomock.Any(), &siPb.ExecuteRequest{
					RecurringPaymentId: "rp-1",
					Amount:             amount,
					Protocol:           paymentPb.PaymentProtocol_NEFT,
					RequestId:          "request-id",
				}).Return(&siPb.ExecuteResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockPaymentClient.EXPECT().UpdateTransaction(gomock.Any(), gomock.Any()).Return(&paymentPb.UpdateTransactionResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mockOrderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), &orderPb.GetOrderWithTransactionsRequest{OrderId: "id-1"}).
					Return(&orderPb.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &orderPb.OrderWithTransactions{
							Order: &orderPb.Order{
								Id: "id-1",
							},
							Transactions: []*paymentPb.Transaction{
								{
									Id:              "txn-id-1",
									PiFrom:          "pi-1",
									PiTo:            "pi-2",
									PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
									Amount:          amount,
									Status:          paymentPb.TransactionStatus_CREATED,
									PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
								},
							},
						}}, nil)

				mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), &orderPb.UpdateInPaymentOrderTimelineRequest{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id: "id-1",
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PiFrom:          "pi-1",
								PiTo:            "pi-2",
								PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
								Amount:          amount,
								Status:          paymentPb.TransactionStatus_CREATED,
								PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
							},
						},
					},
				}).Return("123", nil)
			},

			want:    &domainPb.ProcessPaymentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_IN_PROGRESS}},
			wantErr: false,
		},
		{
			name: "transient failure when acquire lock fails",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id-1",
					IsLastAttempt:   false,
				},
				Payload: marshalledPayload,
			},
			setupMockCalls: func() {
				mockILockManager.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("acquire lock fail"))
			},
			want:    &domainPb.ProcessPaymentResponse{ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_TRANSIENT_FAILURE}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.ProcessRecurringPaymentExecution(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessRecurringPaymentExecution() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessRecurringPaymentExecution() got = %v, want %v", got, tt.want)
			}
		})
	}
}

type CreateTransactionReqMatcher struct {
	want *paymentPb.CreateTransactionRequest
}

func newCreateTransactionReqMatcher(want *paymentPb.CreateTransactionRequest) *CreateTransactionReqMatcher {
	return &CreateTransactionReqMatcher{
		want: want,
	}
}

func (ce *CreateTransactionReqMatcher) Matches(x interface{}) bool {
	got, ok := x.(*paymentPb.CreateTransactionRequest)
	if !ok {
		return false
	}

	ce.want.ReqInfo.ReqId = got.ReqInfo.ReqId
	return reflect.DeepEqual(ce.want, got)
}

func (ce *CreateTransactionReqMatcher) String() string {
	return fmt.Sprintf("want: %v", ce.want)
}
