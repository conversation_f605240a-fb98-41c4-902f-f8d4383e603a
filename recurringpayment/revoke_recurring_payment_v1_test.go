package recurringpayment

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	celestialPb "github.com/epifi/be-common/api/celestial"
	mockCelestial "github.com/epifi/be-common/api/celestial/mocks"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	mocks3 "github.com/epifi/be-common/pkg/lock/mocks"
	storagev2Mocks "github.com/epifi/be-common/pkg/storage/v2/mocks"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpPbPayload "github.com/epifi/gamma/api/recurringpayment/payload"
	"github.com/epifi/gamma/pkg/recurringpayment"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	"github.com/epifi/gamma/recurringpayment/internal/validationrules/mocks"
)

func TestService_RevokeRecurringPaymentV1(t *testing.T) {
	t.Parallel()

	defaultClientReqId := "client-req-id-1"

	type args struct {
		ctx context.Context
		req *rpPb.RevokeRecurringPaymentV1Request
	}
	type mockDependencies struct {
		rpaDao          *daoMocks.MockRecurringPaymentsActionDao
		rpDao           *daoMocks.MockRecurringPaymentDao
		celestialClient *mockCelestial.MockCelestialClient
		txnExecProvider *storagev2Mocks.MockPassthroughDbResourceProvider
	}

	tests := []struct {
		name       string
		args       args
		want       *rpPb.RevokeRecurringPaymentV1Response
		setupMocks func(t *testing.T, deps *mockDependencies)
		wantErr    bool
	}{
		{
			name: "should successfully invoke the revoke recurring payment v1 workflow after creating all the initial entities",
			args: args{
				ctx: context.Background(),
				req: &rpPb.RevokeRecurringPaymentV1Request{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    defaultClientReqId,
				},
			},
			want: &rpPb.RevokeRecurringPaymentV1Response{
				Status: rpc.StatusOk(),
			},
			setupMocks: func(t *testing.T, deps *mockDependencies) {
				recurringPayment1 := &rpPb.RecurringPayment{
					Id:          "rp-1",
					FromActorId: "from-actor-id-1",
					ToActorId:   "to-actor-id-1",
					Ownership:   rpPb.RecurringPaymentOwnership_STOCK_GUARDIAN_TSP,
					State:       rpPb.RecurringPaymentState_ACTIVATED,
				}
				clientReqId1 := defaultClientReqId
				rpa1 := &rpPb.RecurringPaymentsAction{
					Id:                 "rpa-1",
					RecurringPaymentId: "rp-1",
					ClientRequestId:    defaultClientReqId,
					Action:             rpPb.Action_REVOKE,
					State:              rpPb.ActionState_ACTION_CREATED,
				}

				workflowPayload, marshalErr := protojson.Marshal(&rpPbPayload.RevokeRecurringPaymentV1WorkflowPayload{
					RecurringPaymentId:            recurringPayment1.GetId(),
					OriginalRecurringPaymentState: recurringPayment1.GetState().ToNewRecurringPaymentState(),
				})
				require.NoError(t, marshalErr, "error in marshalling expected revoke recurring payment workflow payload")

				deps.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), clientReqId1, true).Return(nil, epifierrors.ErrRecordNotFound)
				deps.rpDao.EXPECT().GetById(gomock.Any(), recurringPayment1.GetId()).Return(recurringPayment1, nil)
				txnExec, err := deps.txnExecProvider.GetResourceForOwnership(recurringPayment1.GetEntityOwnership())
				require.NoError(t, err, "error in getting txn executor for ownership")
				txnErr := txnExec.RunTxn(context.Background(), func(txnCtx context.Context) error {
					deps.rpDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment1, nil, recurringPayment1.GetState(), rpPb.RecurringPaymentState_REVOKE_QUEUED).Return(nil)

					deps.rpaDao.EXPECT().Create(gomock.Any(), &rpPb.RecurringPaymentsAction{
						RecurringPaymentId: rpa1.GetRecurringPaymentId(),
						ClientRequestId:    rpa1.GetClientRequestId(),
						Action:             rpa1.GetAction(),
						State:              rpPb.ActionState_ACTION_CREATED,
					}).Return(rpa1, nil)

					deps.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), &celestialPb.InitiateWorkflowRequest{
						Params: &celestialPb.WorkflowCreationRequestParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id:     rpa1.GetId(),
								Client: workflowPb.Client_RECURRING_PAYMENT,
							},
							Payload:          workflowPayload,
							Ownership:        recurringPayment1.GetEntityOwnership(),
							QualityOfService: celestialPb.QoS_GUARANTEED,
							Version:          workflowPb.Version_V0,
							Type:             celestial.GetTypeEnumFromWorkflowType(rpNs.RevokeRecurringPaymentV1),
							UseCase:          recurringpayment.OwnershipToUseCaseForRecPay[recurringPayment1.GetEntityOwnership()],
						},
					}).Return(&celestialPb.InitiateWorkflowResponse{
						Status: rpc.StatusOk(),
					}, nil)

					return nil
				})
				require.NoError(t, txnErr, "error in running transaction executor")
			},
			wantErr: false,
		},
		{
			name: "should fail and rollback all the DB state changes if initiate workflow fails",
			args: args{
				ctx: context.Background(),
				req: &rpPb.RevokeRecurringPaymentV1Request{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    defaultClientReqId,
				},
			},
			want: &rpPb.RevokeRecurringPaymentV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error in initiating recurring payment revoke flow"),
			},
			setupMocks: func(t *testing.T, deps *mockDependencies) {
				recurringPayment1 := &rpPb.RecurringPayment{
					Id:          "rp-1",
					FromActorId: "from-actor-id-1",
					ToActorId:   "to-actor-id-1",
					Ownership:   rpPb.RecurringPaymentOwnership_STOCK_GUARDIAN_TSP,
					State:       rpPb.RecurringPaymentState_ACTIVATED,
				}
				clientReqId1 := defaultClientReqId
				rpa1 := &rpPb.RecurringPaymentsAction{
					Id:                 "rpa-1",
					RecurringPaymentId: "rp-1",
					ClientRequestId:    defaultClientReqId,
					Action:             rpPb.Action_REVOKE,
					State:              rpPb.ActionState_ACTION_CREATED,
				}

				workflowPayload, marshalErr := protojson.Marshal(&rpPbPayload.RevokeRecurringPaymentV1WorkflowPayload{
					RecurringPaymentId:            recurringPayment1.GetId(),
					OriginalRecurringPaymentState: recurringPayment1.GetState().ToNewRecurringPaymentState(),
				})
				require.NoError(t, marshalErr, "error in marshalling expected revoke recurring payment workflow payload")

				deps.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), clientReqId1, true).Return(nil, epifierrors.ErrRecordNotFound)
				deps.rpDao.EXPECT().GetById(gomock.Any(), recurringPayment1.GetId()).Return(recurringPayment1, nil)
				txnExec, err := deps.txnExecProvider.GetResourceForOwnership(recurringPayment1.GetEntityOwnership())
				require.NoError(t, err, "error in getting txn executor for ownership")
				txnErr := txnExec.RunTxn(context.Background(), func(txnCtx context.Context) error {
					deps.rpDao.EXPECT().UpdateAndChangeStatus(gomock.Any(), recurringPayment1, nil, recurringPayment1.GetState(), rpPb.RecurringPaymentState_REVOKE_QUEUED).Return(nil)

					deps.rpaDao.EXPECT().Create(gomock.Any(), &rpPb.RecurringPaymentsAction{
						RecurringPaymentId: rpa1.GetRecurringPaymentId(),
						ClientRequestId:    rpa1.GetClientRequestId(),
						Action:             rpa1.GetAction(),
						State:              rpPb.ActionState_ACTION_CREATED,
					}).Return(rpa1, nil)

					deps.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), &celestialPb.InitiateWorkflowRequest{
						Params: &celestialPb.WorkflowCreationRequestParams{
							ClientReqId: &workflowPb.ClientReqId{
								Id:     rpa1.GetId(),
								Client: workflowPb.Client_RECURRING_PAYMENT,
							},
							Payload:          workflowPayload,
							Ownership:        recurringPayment1.GetEntityOwnership(),
							QualityOfService: celestialPb.QoS_GUARANTEED,
							Version:          workflowPb.Version_V0,
							Type:             celestial.GetTypeEnumFromWorkflowType(rpNs.RevokeRecurringPaymentV1),
							UseCase:          recurringpayment.OwnershipToUseCaseForRecPay[recurringPayment1.GetEntityOwnership()],
						},
					}).Return(&celestialPb.InitiateWorkflowResponse{
						Status: rpc.StatusInternal(),
					}, nil)

					return nil
				})
				require.NoError(t, txnErr, "error in running transaction executor")
			},
			wantErr: false,
		},
		{
			name: "should return AlreadyExists status if recurring payment action with the client request ID already exists",
			args: args{
				ctx: context.Background(),
				req: &rpPb.RevokeRecurringPaymentV1Request{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    defaultClientReqId,
				},
			},
			want: &rpPb.RevokeRecurringPaymentV1Response{
				Status: rpc.StatusAlreadyExists(),
			},
			setupMocks: func(t *testing.T, deps *mockDependencies) {
				clientReqId1 := defaultClientReqId
				rpa1 := &rpPb.RecurringPaymentsAction{
					Id:                 "rpa-1",
					RecurringPaymentId: "rp-1",
					ClientRequestId:    defaultClientReqId,
					Action:             rpPb.Action_REVOKE,
					State:              rpPb.ActionState_ACTION_CREATED,
				}
				deps.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), clientReqId1, true).Return(rpa1, nil)
			},
			wantErr: false,
		},
		{
			name: "should return FailedPrecondition status if recurring payment entity is not in a state to be revoked",
			args: args{
				ctx: context.Background(),
				req: &rpPb.RevokeRecurringPaymentV1Request{
					RecurringPaymentId: "rp-2",
					ClientRequestId:    defaultClientReqId,
				},
			},
			want: &rpPb.RevokeRecurringPaymentV1Response{
				Status: rpc.StatusFailedPrecondition(),
			},
			setupMocks: func(t *testing.T, deps *mockDependencies) {
				clientReqId1 := defaultClientReqId
				recurringPayment2 := &rpPb.RecurringPayment{
					Id:          "rp-2",
					FromActorId: "from-actor-id-2",
					ToActorId:   "to-actor-id-2",
					Ownership:   rpPb.RecurringPaymentOwnership_STOCK_GUARDIAN_TSP,
					State:       rpPb.RecurringPaymentState_MODIFY_QUEUED,
				}
				deps.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), clientReqId1, true).Return(nil, epifierrors.ErrRecordNotFound)
				deps.rpDao.EXPECT().GetById(gomock.Any(), recurringPayment2.GetId()).Return(recurringPayment2, nil)
			},
			wantErr: false,
		},
		{
			name: "should return Internal status for failure in fetching recurring payment action",
			args: args{
				ctx: context.Background(),
				req: &rpPb.RevokeRecurringPaymentV1Request{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    defaultClientReqId,
				},
			},
			want: &rpPb.RevokeRecurringPaymentV1Response{
				Status: rpc.StatusInternalWithDebugMsg("failure in fetching RPA"),
			},
			setupMocks: func(t *testing.T, deps *mockDependencies) {
				clientReqId1 := defaultClientReqId
				deps.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), clientReqId1, true).Return(nil, fmt.Errorf("failure in fetching RPA"))
			},
			wantErr: false,
		},
		{
			name: "should return Internal status for failure in fetching recurring payment entity",
			args: args{
				ctx: context.Background(),
				req: &rpPb.RevokeRecurringPaymentV1Request{
					RecurringPaymentId: "rp-1",
					ClientRequestId:    defaultClientReqId,
				},
			},
			want: &rpPb.RevokeRecurringPaymentV1Response{
				Status: rpc.StatusInternalWithDebugMsg("failure in fetching recurring payment"),
			},
			setupMocks: func(t *testing.T, deps *mockDependencies) {
				recurringPayment1 := &rpPb.RecurringPayment{
					Id:          "rp-1",
					FromActorId: "from-actor-id-1",
					ToActorId:   "to-actor-id-1",
					Ownership:   rpPb.RecurringPaymentOwnership_STOCK_GUARDIAN_TSP,
					State:       rpPb.RecurringPaymentState_ACTIVATED,
				}
				clientReqId1 := defaultClientReqId

				deps.rpaDao.EXPECT().GetByClientRequestId(gomock.Any(), clientReqId1, true).Return(nil, epifierrors.ErrRecordNotFound)
				deps.rpDao.EXPECT().GetById(gomock.Any(), recurringPayment1.GetId()).Return(nil, fmt.Errorf("failure in fetching recurring payment"))
			},
			wantErr: false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			actionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
			dao := daoMocks.NewMockRecurringPaymentDao(ctr)
			ivalidateRules := mocks.NewMockIValidationRule(ctr)
			celestialClient := mockCelestial.NewMockCelestialClient(ctr)
			mockIlockManager := mocks3.NewMockRwLockManager(ctr)
			mockRecurringPaymentVendorDetailsDao := daoMocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)
			mockTxnExecProvider := storagev2Mocks.NewMockPassthroughDbResourceProvider(storagev2Mocks.NewPassThroughMockIdempotentTxnExecutor())
			s := NewService(dao, nil, nil, actionDao, nil, nil, nil, nil, nil, conf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockIlockManager, nil, nil, nil, nil, nil, nil, celestialClient, nil, nil, ivalidateRules, nil, nil, nil, nil, mockRecurringPaymentVendorDetailsDao, nil, mockTxnExecProvider, nil, nil, nil)

			tc.setupMocks(t, &mockDependencies{
				rpaDao:          actionDao,
				rpDao:           dao,
				celestialClient: celestialClient,
				txnExecProvider: mockTxnExecProvider,
			})
			got, err := s.RevokeRecurringPaymentV1(tc.args.ctx, tc.args.req)
			if (err != nil) != tc.wantErr {
				t.Errorf("RevokeRecurringPaymentV1() error = %v, wantErr %v", err, tc.wantErr)
				return
			}
			if !proto.Equal(got, tc.want) {
				t.Errorf("RevokeRecurringPaymentV1() got = %v, want %v", got, tc.want)
			}
		})
	}
}
