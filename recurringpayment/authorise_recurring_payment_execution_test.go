package recurringpayment

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	mocks2 "github.com/epifi/gamma/api/actor/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	pb "github.com/epifi/gamma/api/recurringpayment"
	payloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	"github.com/epifi/gamma/api/recurringpayment/standinginstruction/mocks"
	"github.com/epifi/gamma/api/typesv2"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	upiMandateMocks "github.com/epifi/gamma/api/upi/mandate/mocks"
	daoMocks "github.com/epifi/gamma/recurringpayment/dao/mocks"
	internalMocks "github.com/epifi/gamma/recurringpayment/internal/mocks"
)

func TestService_AuthoriseExecute(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockRecurringPaymentDao := daoMocks.NewMockRecurringPaymentDao(ctr)
	mockRecurringPaymentVendorDetailsDao := daoMocks.NewMockRecurringPaymentsVendorDetailsDao(ctr)
	mockSIClient := mocks.NewMockStandingInstructionServiceClient(ctr)
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
	mockRecurringPaymentActionDao := daoMocks.NewMockRecurringPaymentsActionDao(ctr)
	mockActorClient := mocks2.NewMockActorClient(ctr)
	mockCelestialProcessor := internalMocks.NewMockCelestialProcessor(ctr)
	mockUpiMandateClient := upiMandateMocks.NewMockMandateServiceClient(ctr)

	orderPayload, _ := protojson.Marshal(&pb.RecurringPaymentExecutionInfo{
		RecurringPaymentId: "id-1",
		Amount:             money.ZeroINR().Pb,
	})
	payload, _ := protojson.Marshal(&payloadPb.ExecuteRecurringPaymentAuthSignal{})

	startDate := timestampPb.New(time.Now())
	endDate := timestampPb.New(time.Now().Add(48 * time.Hour))

	svc := NewService(mockRecurringPaymentDao, mockOrderClient, mockSIClient, mockRecurringPaymentActionDao, nil, mockActorClient, nil, nil, nil, conf, nil, mockUpiMandateClient, nil, nil, nil, nil, nil, nil, dynamicConf.FeatureFlags(), mockCelestialProcessor, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockRecurringPaymentVendorDetailsDao, nil, nil, nil, nil, nil)

	recurringPayment := &pb.RecurringPayment{
		Id:          "id-1",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Type:        pb.RecurringPaymentType_STANDING_INSTRUCTION,
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		Amount:      money.ZeroINR().Pb,
		AmountType:  pb.AmountType_MAXIMUM,
		Interval: &typesv2.Interval{
			StartTime: startDate,
			EndTime:   endDate,
		},
		RecurrenceRule: &pb.RecurrenceRule{
			AllowedFrequency: pb.AllowedFrequency_AS_PRESENTED,
			Day:              5,
		},
		MaximumAllowedTxns:       10,
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		PreferredPaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		State:                    pb.RecurringPaymentState_CREATION_QUEUED,
		Ownership:                pb.RecurringPaymentOwnership_EPIFI_TECH,
		Provenance:               pb.RecurrencePaymentProvenance_USER_APP,
		UiEntryPoint:             pb.UIEntryPoint_FIT,
	}
	tests := []struct {
		name           string
		req            *pb.AuthoriseExecuteRequest
		setupMockCalls func()
		want           *pb.AuthoriseExecuteResponse
		wantErr        bool
	}{
		{
			name: "authorised successfully",
			req: &pb.AuthoriseExecuteRequest{
				Credential:      &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId: "client-req-1",
				CurrentActorId:  "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				_ = dynamicConf.FeatureFlags().SetEnableRecurringPaymentExecutionWithAuthViaCelestial(false, false, nil)
				mockOrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &orderPb.Order{
							Id:           "order-id",
							OrderPayload: orderPayload,
							Workflow:     orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH,
						},
					}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "id-1").Return(recurringPayment, nil)
				mockUpiMandateClient.EXPECT().InitiateMandateExecution(gomock.Any(), &upiMandatePb.InitiateMandateExecutionRequest{
					OrderId:    "order-id",
					ActorId:    recurringPayment.FromActorId,
					AuthHeader: &upiMandatePb.InitiateMandateExecutionRequest_AuthHeader{},
				}).Return(&upiMandatePb.InitiateMandateExecutionResponse{Status: rpc.StatusOk()}, nil)
				mockOrderClient.EXPECT().
					InitiateOrderProcessing(gomock.Any(), &orderPb.InitiateOrderProcessingRequest{Identifier: &orderPb.InitiateOrderProcessingRequest_ClientReqId{ClientReqId: "client-req-1"}}).
					Return(&orderPb.InitiateOrderProcessingResponse{Status: rpc.StatusOk()}, nil)

			},
			want:    &pb.AuthoriseExecuteResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "failed to authorise, because order not found given client request ID",
			req: &pb.AuthoriseExecuteRequest{
				Credential:      &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId: "client-req-1",
				CurrentActorId:  "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				_ = dynamicConf.FeatureFlags().SetEnableRecurringPaymentExecutionWithAuthViaCelestial(false, false, nil)
				mockOrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

			},
			want:    &pb.AuthoriseExecuteResponse{Status: rpc.StatusRecordNotFound()},
			wantErr: false,
		},
		{
			name: "failed to authorise because recurring payment not found for given ID",
			req: &pb.AuthoriseExecuteRequest{
				Credential:      &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId: "client-req-1",
				CurrentActorId:  "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				_ = dynamicConf.FeatureFlags().SetEnableRecurringPaymentExecutionWithAuthViaCelestial(false, false, nil)
				mockOrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &orderPb.Order{
							Id:           "order-id",
							OrderPayload: orderPayload,
							Workflow:     orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH,
						},
					}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "id-1").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    &pb.AuthoriseExecuteResponse{Status: rpc.StatusRecordNotFound()},
			wantErr: false,
		},
		{
			name: "failed to authorise as InitiateOrderProcessing returned not success status",
			req: &pb.AuthoriseExecuteRequest{
				Credential:      &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId: "client-req-1",
				CurrentActorId:  "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				_ = dynamicConf.FeatureFlags().SetEnableRecurringPaymentExecutionWithAuthViaCelestial(false, false, nil)
				mockOrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &orderPb.Order{
							Id:           "order-id",
							OrderPayload: orderPayload,
							Workflow:     orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH,
						},
					}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "id-1").Return(recurringPayment, nil)
				mockUpiMandateClient.EXPECT().InitiateMandateExecution(gomock.Any(), &upiMandatePb.InitiateMandateExecutionRequest{
					OrderId:    "order-id",
					ActorId:    recurringPayment.FromActorId,
					AuthHeader: &upiMandatePb.InitiateMandateExecutionRequest_AuthHeader{},
				}).Return(&upiMandatePb.InitiateMandateExecutionResponse{Status: rpc.StatusInternal()}, nil)
				mockOrderClient.EXPECT().
					InitiateOrderProcessing(gomock.Any(), &orderPb.InitiateOrderProcessingRequest{Identifier: &orderPb.InitiateOrderProcessingRequest_ClientReqId{ClientReqId: "client-req-1"}}).
					Return(&orderPb.InitiateOrderProcessingResponse{Status: rpc.StatusOk()}, nil)

			},
			want:    &pb.AuthoriseExecuteResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "authorised successfully with celestial workflow",
			req: &pb.AuthoriseExecuteRequest{
				Credential:      &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId: "client-req-1",
				CurrentActorId:  "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				_ = dynamicConf.FeatureFlags().SetEnableRecurringPaymentExecutionWithAuthViaCelestial(true, false, nil)
				mockOrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &orderPb.Order{
							Id:           "order-id",
							OrderPayload: orderPayload,
							Workflow:     orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH,
						},
					}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "id-1").Return(recurringPayment, nil)
				mockUpiMandateClient.EXPECT().InitiateMandateExecution(gomock.Any(), &upiMandatePb.InitiateMandateExecutionRequest{
					OrderId:    "order-id",
					ActorId:    recurringPayment.FromActorId,
					AuthHeader: &upiMandatePb.InitiateMandateExecutionRequest_AuthHeader{},
				}).Return(&upiMandatePb.InitiateMandateExecutionResponse{Status: rpc.StatusOk()}, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{
						Id:        "workflow_req_id",
						CreatedAt: timestampPb.Now(),
					}, nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), "client-req-1", string(rpNs.ExecuteRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, payload, gomock.Any(), gomock.Any()).Return(nil)
			},
			want:    &pb.AuthoriseExecuteResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "failed to authorise because workflow request not found for given client req id",
			req: &pb.AuthoriseExecuteRequest{
				Credential:      &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId: "client-req-1",
				CurrentActorId:  "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				_ = dynamicConf.FeatureFlags().SetEnableRecurringPaymentExecutionWithAuthViaCelestial(true, false, nil)
				mockOrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &orderPb.Order{
							Id:           "order-id",
							OrderPayload: orderPayload,
							Workflow:     orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH,
						},
					}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "id-1").Return(recurringPayment, nil)

				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(nil, fmt.Errorf("record not found %v %w", epifierrors.ErrRecordNotFound, rpc.StatusAsError(rpc.StatusRecordNotFound())))
			},
			want:    &pb.AuthoriseExecuteResponse{Status: rpc.StatusRecordNotFound()},
			wantErr: false,
		},
		{
			name: "failed to authorise because signalling workflow failed",
			req: &pb.AuthoriseExecuteRequest{
				Credential:      &pb.Credential{Params: &pb.Credential_PartnerSdkCredBlock{PartnerSdkCredBlock: "asdfg"}},
				ClientRequestId: "client-req-1",
				CurrentActorId:  "actor-1",
				ClientId: &celestialPb.ClientReqId{
					Id:     "client-req-1",
					Client: workflowPb.Client_RECURRING_PAYMENT,
				},
			},
			setupMockCalls: func() {
				_ = dynamicConf.FeatureFlags().SetEnableRecurringPaymentExecutionWithAuthViaCelestial(true, false, nil)
				mockOrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "client-req-1"}}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &orderPb.Order{
							Id:           "order-id",
							OrderPayload: orderPayload,
							Workflow:     orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH,
						},
					}, nil)
				mockRecurringPaymentDao.EXPECT().GetById(gomock.Any(), "id-1").Return(recurringPayment, nil)
				mockCelestialProcessor.EXPECT().GetWorkflowByClientRequestId(gomock.Any(), "client-req-1", workflowPb.Client_RECURRING_PAYMENT).
					Return(&celestialPb.WorkflowRequest{
						Id:        "workflow_req_id",
						CreatedAt: timestampPb.Now(),
					}, nil)
				mockCelestialProcessor.EXPECT().SignalWorkflow(gomock.Any(), "client-req-1", string(rpNs.ExecuteRecurringPaymentAuthSignal), workflowPb.Client_RECURRING_PAYMENT, payload, gomock.Any(), gomock.Any()).Return(errors.New("failed to signal"))
			},
			want:    &pb.AuthoriseExecuteResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.AuthoriseExecute(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AuthoriseExecute() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("AuthoriseExecute() got = %v, want %v", got, tt.want)
			}
		})
	}
}
