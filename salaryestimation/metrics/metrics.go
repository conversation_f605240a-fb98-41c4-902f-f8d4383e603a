package metrics

import (
	"github.com/prometheus/client_golang/prometheus"

	sePb "github.com/epifi/gamma/api/salaryestimation"
)

type SalaryEstimationMetrics struct {
	seAttemptDaoMetrics *prometheus.CounterVec
}

var salaryEstimationMetricsRecorder = initializeSalaryEstimationMetrics()

func initializeSalaryEstimationMetrics() *SalaryEstimationMetrics {
	seMetrics := &SalaryEstimationMetrics{
		seAttemptDaoMetrics: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "salary_estimation_attempts_total",
			Help: "Metrics counter for SE attempts primarily to record all updates and creations",
		}, []string{"step", "status", "sub_status"}),
	}
	prometheus.MustRegister(seMetrics.seAttemptDaoMetrics)
	return seMetrics
}

func RecordSeDaoMetric(step sePb.AttemptStep, status sePb.AttemptStatus, subStatus sePb.AttemptSubStatus) {
	salaryEstimationMetricsRecorder.seAttemptDaoMetrics.WithLabelValues(step.String(), status.String(), subStatus.String()).Inc()
}
