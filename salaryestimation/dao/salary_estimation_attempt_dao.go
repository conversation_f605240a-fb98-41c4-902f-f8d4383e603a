//nolint:dupl
package dao

import (
	"context"
	"fmt"
	"time"

	storage "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	cmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"
	sePb "github.com/epifi/gamma/api/salaryestimation"
	"github.com/epifi/gamma/salaryestimation"
	"github.com/epifi/gamma/salaryestimation/dao/model"
	seMetrics "github.com/epifi/gamma/salaryestimation/metrics"
)

var (
	SalaryEstimationAttemptPgDbDaoWireSet = wire.NewSet(
		NewSalaryEstimationAttemptPgDbDao,
	)

	_ salaryestimation.SalaryEstimationAttemptDao = &SalaryEstimationAttemptPgDbDao{}

	salaryEstimationAttemptColumnNames = map[sePb.SalaryEstimationAttemptFieldMask]string{
		sePb.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_SOURCE:        "source",
		sePb.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_CLIENT_PARAMS: "client_params",
		sePb.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STEP:          "step",
		sePb.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS:        "status",
		sePb.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_SUB_STATUS:    "sub_status",
		sePb.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_ATTEMPT_INFO:  "attempt_info",
		sePb.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UPDATED_AT:    "updated_at",
		sePb.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_DELETED_AT:    "deleted_at",
		sePb.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_EXPIRY_AT:     "expiry_at",
	}
)

// SalaryEstimationAttemptPgDbDao implements the SalaryEstimationAttemptDao interface
type SalaryEstimationAttemptPgDbDao struct {
	db *gorm.DB
}

// NewSalaryEstimationAttemptPgDbDao creates a new instance of SalaryEstimationAttemptPgDbDao
func NewSalaryEstimationAttemptPgDbDao(db cmdTypes.FeatureEngineeringPGDB) salaryestimation.SalaryEstimationAttemptDao {
	return &SalaryEstimationAttemptPgDbDao{
		db: db,
	}
}

// Create creates a new salary estimation attempt record
func (d *SalaryEstimationAttemptPgDbDao) Create(ctx context.Context, attempt *sePb.SalaryEstimationAttempt) (*sePb.SalaryEstimationAttempt, error) {
	defer metric_util.TrackDuration("salaryestimation/dao", "SalaryEstimationAttemptPgDbDao", "Create", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	attemptModel := model.NewSalaryEstimationAttempt(attempt)
	if err := db.Create(attemptModel).Error; err != nil {
		return nil, errors.Wrap(err, "failed to create salary estimation attempt")
	}
	seMetrics.RecordSeDaoMetric(attemptModel.GetProto().GetStep(), attemptModel.GetProto().GetStatus(), attemptModel.GetProto().GetSubStatus())
	return attemptModel.GetProto(), nil
}

// GetByClientReqID retrieves a salary estimation attempt by client request ID
func (d *SalaryEstimationAttemptPgDbDao) GetByClientReqID(ctx context.Context, clientReqID string) (*sePb.SalaryEstimationAttempt, error) {
	defer metric_util.TrackDuration("salaryestimation/dao", "SalaryEstimationAttemptPgDbDao", "GetByClientReqID", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	query := db.Model(&model.SalaryEstimationAttempt{}).Where("client_req_id = ?", clientReqID)
	var attempt model.SalaryEstimationAttempt
	if err := query.First(&attempt).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "salary estimation attempt not found")
		}
		return nil, errors.Wrap(err, "failed to get salary estimation attempt by client request ID")
	}
	return attempt.GetProto(), nil
}

// GetByActorId retrieves a salary estimation attempts filtered by actor id, sorted by created at desc
func (d *SalaryEstimationAttemptPgDbDao) GetByActorId(ctx context.Context, actorId string) ([]*sePb.SalaryEstimationAttempt, error) {
	defer metric_util.TrackDuration("salaryestimation/dao", "SalaryEstimationAttemptPgDbDao", "GetByActorId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	query := db.Model(&model.SalaryEstimationAttempt{}).Where("actor_id = ?", actorId)
	attempts := make([]model.SalaryEstimationAttempt, 0)
	if err := query.Order("created_at desc").Find(&attempts).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	if len(attempts) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	var attemptsPbs []*sePb.SalaryEstimationAttempt
	for _, itModel := range attempts {
		attemptsPbs = append(attemptsPbs, itModel.GetProto())
	}
	return attemptsPbs, nil
}

// GetByID retrieves a salary estimation attempt by ID
func (d *SalaryEstimationAttemptPgDbDao) GetByID(ctx context.Context, id string) (*sePb.SalaryEstimationAttempt, error) {
	defer metric_util.TrackDuration("salaryestimation/dao", "SalaryEstimationAttemptPgDbDao", "GetByID", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	query := db.Model(&model.SalaryEstimationAttempt{}).Where("id = ?", id)
	var attempt model.SalaryEstimationAttempt
	if err := query.First(&attempt).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "salary estimation attempt not found")
		}
		return nil, errors.Wrap(err, "failed to get salary estimation attempt by ID")
	}
	return attempt.GetProto(), nil
}

func (d *SalaryEstimationAttemptPgDbDao) Update(
	ctx context.Context,
	attempt *sePb.SalaryEstimationAttempt,
	fieldMasks []sePb.SalaryEstimationAttemptFieldMask,
) (*sePb.SalaryEstimationAttempt, error) {
	defer metric_util.TrackDuration("salaryestimation/dao", "SalaryEstimationAttemptPgDbDao", "Update", time.Now())
	if attempt.GetId() == "" {
		return nil, fmt.Errorf("primary identifier can't be empty for an update operation")
	}
	if len(fieldMasks) == 0 {
		return nil, fmt.Errorf("update mask can't be empty")
	}
	attemptModel := model.NewSalaryEstimationAttempt(attempt)
	updateColumns := d.selectedColumnsForUpdate(fieldMasks)
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	whereClause := &model.SalaryEstimationAttempt{
		ID: attempt.GetId(),
	}
	if err := db.Model(attemptModel).Where(whereClause).Select(updateColumns).Clauses(clause.Returning{}).Updates(attemptModel).Error; err != nil {
		return nil, errors.Wrap(err, "failed to update salary estimation attempt")
	}
	seMetrics.RecordSeDaoMetric(attemptModel.GetProto().GetStep(), attemptModel.GetProto().GetStatus(), attemptModel.GetProto().GetSubStatus())
	return attemptModel.GetProto(), nil
}

func (d *SalaryEstimationAttemptPgDbDao) selectedColumnsForUpdate(fieldMasks []sePb.SalaryEstimationAttemptFieldMask) []string {
	var selectColumns []string
	for _, field := range fieldMasks {
		if columnName, exists := salaryEstimationAttemptColumnNames[field]; exists {
			selectColumns = append(selectColumns, columnName)
		}
	}
	return selectColumns
}
